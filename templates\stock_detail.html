{% extends "layout.html" %}

{% block title %}股票详情 - {{ stock_code }} - 智能分析系统{% endblock %}

{% block head %}
<style>
    /* Enhanced Material Design 3 Stock Detail Styles */
    .stock-detail-header {
        background: linear-gradient(135deg, var(--md-sys-color-primary-container) 0%, var(--md-sys-color-secondary-container) 100%);
        border-radius: var(--md-sys-shape-corner-large);
        padding: 32px;
        margin-bottom: 32px;
        position: relative;
        overflow: hidden;
    }

    .stock-detail-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .stock-price-display {
        font-family: var(--md-sys-typescale-financial-large-font);
        font-size: var(--md-sys-typescale-financial-large-size);
        font-weight: var(--md-sys-typescale-financial-large-weight);
        color: var(--md-sys-color-on-primary-container);
        margin-bottom: 8px;
    }

    .stock-change-display {
        font-family: var(--md-sys-typescale-financial-medium-font);
        font-size: var(--md-sys-typescale-financial-medium-size);
        font-weight: var(--md-sys-typescale-financial-medium-weight);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .stock-controls {
        display: flex;
        gap: 16px;
        align-items: center;
        flex-wrap: wrap;
    }

    /* 评分加载状态样式 */
    .score-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        opacity: 0.7;
    }

    .score-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        color: var(--md-sys-color-error);
    }

    .score-error .material-icons {
        margin-bottom: 16px;
        opacity: 0.6;
    }

    /* 评分加载动画 */
    @keyframes scoreLoading {
        0% { opacity: 0.3; }
        50% { opacity: 0.8; }
        100% { opacity: 0.3; }
    }

    .score-loading .shimmer-skeleton {
        animation: scoreLoading 1.5s ease-in-out infinite;
    }
    }

    .stock-metrics-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 32px;
    }

    @media (max-width: 768px) {
        .stock-metrics-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        /* 移动设备上的评分明细样式优化 */
        .score-details-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }

        .score-progress-container {
            width: 100%;
            min-width: unset;
        }

        .score-dimension-name {
            font-size: 14px;
        }

        /* 移动端详情面板优化 */
        .indicators-grid {
            grid-template-columns: 1fr;
        }

        .score-detail-toggle-btn {
            padding: 6px 10px;
            gap: 4px;
        }

        .detail-toggle-text {
            font-size: 11px;
        }

        .score-detail-content {
            padding: 12px;
        }

        .score-detail-title {
            font-size: 16px;
        }

        .score-detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .score-detail-close-btn {
            align-self: flex-end;
            padding: 4px 8px;
            font-size: 11px;
        }

        /* 移动设备上的评分明细容器调整 */
        #score-details-container.expanded {
            max-height: 1000px; /* 移动设备需要更多空间 */
        }

        .score-dimension-desc {
            font-size: 12px;
            line-height: 1.3;
        }

        .score-weight-display {
            display: block;
            margin-left: 0;
            margin-top: 4px;
        }

        .score-details-summary {
            padding: 12px;
        }

        .score-details-summary h4 {
            font-size: 16px;
        }

        .score-details-summary p {
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        /* 超小屏幕设备优化 */
        .score-progress-container {
            flex-direction: column;
            gap: 8px;
        }

        .score-progress-bar {
            width: 100%;
        }

        .score-value-display {
            text-align: center;
            min-width: unset;
        }
    }

    .metric-card {
        background: var(--md-sys-color-surface-container);
        border-radius: var(--md-sys-shape-corner-medium);
        padding: 24px;
        text-align: center;
        transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
        border: 1px solid var(--md-sys-color-outline-variant);
    }

    .metric-card:hover {
        background: var(--md-sys-color-surface-container-high);
        transform: translateY(-4px);
        box-shadow: var(--md-sys-elevation-level2);
    }

    .metric-value {
        font-family: var(--md-sys-typescale-financial-medium-font);
        font-size: var(--md-sys-typescale-financial-medium-size);
        font-weight: var(--md-sys-typescale-financial-medium-weight);
        color: var(--md-sys-color-on-surface);
        margin-bottom: 8px;
    }

    .metric-label {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
    }

    .chart-tabs {
        margin-bottom: 32px;
    }

    .chart-container {
        background: var(--md-sys-color-surface);
        border-radius: var(--md-sys-shape-corner-large);
        border: 1px solid var(--md-sys-color-outline-variant);
        overflow: hidden;
    }

    .ai-analysis-section {
        background: var(--md-sys-color-surface-container-low);
        border-radius: var(--md-sys-shape-corner-large);
        padding: 32px;
        position: relative;
        min-height: 300px;
    }

    .ai-loader {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: rgba(var(--md-sys-color-surface-rgb), 0.9);
        backdrop-filter: blur(8px);
        border-radius: var(--md-sys-shape-corner-large);
        z-index: 10;
    }

    .typing-animation {
        display: flex;
        gap: 4px;
        margin-top: 16px;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        background: var(--md-sys-color-primary);
        border-radius: 50%;
        animation: typing-bounce 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typing-bounce {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1.2); opacity: 1; }
    }

    .shimmer-skeleton {
        background: linear-gradient(90deg, var(--md-sys-color-surface-container) 25%, var(--md-sys-color-surface-container-high) 50%, var(--md-sys-color-surface-container) 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: var(--md-sys-shape-corner-small);
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .score-circle {
        position: relative;
        width: 160px;
        height: 160px;
        margin: 0 auto;
    }

    .score-display {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }

    .score-number {
        font-family: var(--md-sys-typescale-financial-large-font);
        font-size: 48px;
        font-weight: var(--md-sys-typescale-financial-large-weight);
        color: var(--md-sys-color-primary);
        line-height: 1;
    }

    .score-text {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
        margin-top: 4px;
    }

    .support-resistance-table {
        width: 100%;
        border-collapse: collapse;
    }

    /* 评分明细样式 */
    .score-details-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .score-details-item:last-child {
        border-bottom: none;
    }

    .score-dimension {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

    .score-dimension-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
    }

    .score-dimension-name {
        font-family: var(--md-sys-typescale-body-medium-font);
        font-size: var(--md-sys-typescale-body-medium-size);
        font-weight: var(--md-sys-typescale-body-medium-weight);
        color: var(--md-sys-color-on-surface);
        display: flex;
        align-items: center;
    }

    .score-dimension-desc {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
    }

    .score-progress-container {
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 200px;
    }

    .score-progress-bar {
        flex: 1;
        height: 8px;
        background-color: var(--md-sys-color-surface-variant);
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .score-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-secondary));
        border-radius: 4px;
        transition: width 0.6s ease;
    }

    .score-value-display {
        font-family: var(--md-sys-typescale-label-medium-font);
        font-size: var(--md-sys-typescale-label-medium-size);
        font-weight: var(--md-sys-typescale-label-medium-weight);
        color: var(--md-sys-color-primary);
        min-width: 40px;
        text-align: right;
    }

    .score-weight-display {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
        margin-left: 8px;
    }

    /* 评分详情按钮样式 - 改进版 */
    .score-detail-toggle-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border-radius: 20px;
        border: none;
        background: var(--md-sys-color-surface-variant);
        color: var(--md-sys-color-on-surface-variant);
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: var(--md-sys-typescale-label-medium-font);
        font-size: var(--md-sys-typescale-label-medium-size);
        font-weight: var(--md-sys-typescale-label-medium-weight);
    }

    .score-detail-toggle-btn:hover {
        background: var(--md-sys-color-primary-container);
        color: var(--md-sys-color-on-primary-container);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .score-detail-toggle-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    }

    .score-detail-toggle-btn .material-icons {
        font-size: 18px;
    }

    .detail-toggle-text {
        font-size: 12px;
        white-space: nowrap;
    }

    /* 按钮激活状态 */
    .score-detail-toggle-btn.active {
        background: var(--md-sys-color-primary);
        color: var(--md-sys-color-on-primary);
    }

    .score-detail-toggle-btn.active:hover {
        background: var(--md-sys-color-primary-container);
        color: var(--md-sys-color-on-primary-container);
    }

    /* 评分详情面板样式 */
    .score-detail-panel {
        margin-top: 16px;
        background: var(--md-sys-color-surface-container-low);
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid var(--md-sys-color-outline-variant);
    }

    .score-detail-content {
        padding: 16px;
    }

    /* 详情面板头部样式 */
    .score-detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .score-detail-title {
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        font-weight: var(--md-sys-typescale-title-small-weight);
        color: var(--md-sys-color-on-surface);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 关闭按钮样式 */
    .score-detail-close-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        border-radius: 16px;
        border: none;
        background: var(--md-sys-color-error-container);
        color: var(--md-sys-color-on-error-container);
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: var(--md-sys-typescale-label-small-font);
        font-size: var(--md-sys-typescale-label-small-size);
        font-weight: var(--md-sys-typescale-label-small-weight);
    }

    .score-detail-close-btn:hover {
        background: var(--md-sys-color-error);
        color: var(--md-sys-color-on-error);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .score-detail-close-btn:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .score-detail-close-btn .material-icons {
        font-size: 16px;
    }

    .score-indicators-section,
    .score-logic-section {
        margin-bottom: 16px;
    }

    .score-indicators-section:last-child,
    .score-logic-section:last-child {
        margin-bottom: 0;
    }

    .score-indicators-section h6,
    .score-logic-section h6 {
        font-family: var(--md-sys-typescale-label-large-font);
        font-size: var(--md-sys-typescale-label-large-size);
        font-weight: var(--md-sys-typescale-label-large-weight);
        color: var(--md-sys-color-primary);
        margin: 0 0 8px 0;
    }

    .indicators-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 8px;
    }

    .indicator-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: var(--md-sys-color-surface-container);
        border-radius: 8px;
        border: 1px solid var(--md-sys-color-outline-variant);
    }

    .indicator-name {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
    }

    .indicator-value {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        font-weight: 500;
        color: var(--md-sys-color-on-surface);
    }

    .logic-list {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .logic-item {
        padding: 8px 12px;
        background: var(--md-sys-color-surface-container);
        border-radius: 8px;
        border-left: 3px solid var(--md-sys-color-primary);
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface);
        line-height: 1.4;
    }

    .score-details-summary {
        background-color: var(--md-sys-color-surface-container-high);
        border-radius: var(--md-sys-shape-corner-medium);
        padding: 16px;
        margin-bottom: 16px;
    }

    .score-details-summary h4 {
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        color: var(--md-sys-color-on-surface);
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .score-details-summary p {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
        margin: 0;
        line-height: 1.4;
    }

    /* 评分明细容器动画样式 */
    #score-details-container {
        overflow: hidden;
        transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-in-out;
        max-height: 0;
        opacity: 0;
        will-change: max-height, opacity;
    }

    #score-details-container.expanded {
        max-height: 800px; /* 足够容纳所有评分明细内容 */
        opacity: 1;
    }

    /* 确保评分明细展开时不影响其他元素布局 */
    .md3-card-body {
        position: relative;
    }

    /* 修复评分容器布局，确保评分数值位置固定 */
    #score-container {
        position: relative;
        z-index: 2;
    }

    #recommendation-container {
        position: relative;
        z-index: 2;
    }

    #score-details-toggle {
        position: relative;
        z-index: 2;
    }

    /* 评分明细容器不影响上方元素位置 */
    #score-details-container {
        position: relative;
        z-index: 1;
        /* 确保展开时不推动上方元素 */
        margin-top: 24px !important;
    }

    /* 评分明细按钮样式优化 */
    #score-details-toggle .md3-button {
        transition: all 0.2s ease;
    }

    #score-details-toggle .md3-button:hover {
        background-color: var(--md-sys-color-surface-container-high);
    }

    /* 确保评分明细内容在动画期间不会溢出 */
    #score-details-container > * {
        transform: translateZ(0); /* 启用硬件加速 */
    }

    .support-resistance-table th,
    .support-resistance-table td {
        padding: 12px 16px;
        text-align: center;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .support-resistance-table th {
        background: var(--md-sys-color-surface-container);
        color: var(--md-sys-color-on-surface);
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        font-weight: var(--md-sys-typescale-title-small-weight);
    }

    .trend-indicator {
        padding: 8px 16px;
        border-radius: var(--md-sys-shape-corner-small);
        font-family: var(--md-sys-typescale-label-medium-font);
        font-size: var(--md-sys-typescale-label-medium-size);
        font-weight: var(--md-sys-typescale-label-medium-weight);
    }

    .trend-up {
        background: var(--md-sys-color-bull-container);
        color: var(--md-sys-color-on-bull-container);
        border-left: 4px solid var(--md-sys-color-bull);
    }

    .trend-down {
        background: var(--md-sys-color-bear-container);
        color: var(--md-sys-color-on-bear-container);
        border-left: 4px solid var(--md-sys-color-bear);
    }

    /* Enhanced Material Design 3 Tab Styles */
    .md3-tab-bar {
        display: flex;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
        background-color: var(--md-sys-color-surface);
        border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0;
        overflow: hidden;
    }

    .md3-tab {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px 24px;
        border: none;
        background: none;
        color: var(--md-sys-color-on-surface-variant);
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        font-weight: 500;
        cursor: pointer;
        transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
        border-bottom: 3px solid transparent;
        flex: 1;
        justify-content: center;
        min-height: 48px;
    }

    .md3-tab:hover {
        background-color: var(--md-sys-color-surface-container-high);
        color: var(--md-sys-color-on-surface);
    }

    .md3-tab.md3-tab-active {
        color: var(--md-sys-color-primary);
        border-bottom-color: var(--md-sys-color-primary);
        background-color: var(--md-sys-color-primary-container);
    }

    .md3-tab-content {
        margin-top: 0;
    }

    .md3-tab-panel {
        display: none;
    }

    .md3-tab-panel.md3-tab-panel-active {
        display: block;
    }

    /* Tab Icons */
    .md3-tab i {
        font-size: 20px;
    }

    /* Responsive Tab Design */
    @media (max-width: 768px) {
        .md3-tab {
            padding: 12px 16px;
            font-size: 14px;
        }

        .md3-tab span {
            display: none;
        }

        .md3-tab i {
            font-size: 18px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-transition">
    <div id="alerts-container"></div>

    <!-- Enhanced Material Design 3 股票详情头部 -->
    <div class="stock-detail-header md3-animate-fade-in">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; position: relative; z-index: 1;">
            <div style="flex: 1;">
                <div id="stock-title" style="margin-bottom: 16px;">
                    <div class="shimmer-skeleton" style="width: 300px; height: 32px; margin-bottom: 8px;"></div>
                    <div class="shimmer-skeleton" style="width: 200px; height: 20px;"></div>
                </div>
                <div id="stock-price-section" style="margin-bottom: 24px;">
                    <div id="price-container">
                        <div class="shimmer-skeleton" style="width: 200px; height: 48px; margin-bottom: 8px;"></div>
                        <div class="shimmer-skeleton" style="width: 150px; height: 24px;"></div>
                    </div>
                </div>
                <div id="stock-basic-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 16px;">
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">今开</div>
                        <div id="open-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">最高</div>
                        <div id="high-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">最低</div>
                        <div id="low-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">成交量</div>
                        <div id="volume-info" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                </div>
            </div>
            <div class="stock-controls">
                <div class="md3-text-field md3-text-field-outlined md3-text-field-small">
                    <select class="md3-text-field-input" id="market-type">
                        <option value="A" {% if market_type == 'A' %}selected{% endif %}>A股</option>
                        <option value="HK" {% if market_type == 'HK' %}selected{% endif %}>港股</option>
                        <option value="US" {% if market_type == 'US' %}selected{% endif %}>美股</option>
                    </select>
                    <label class="md3-text-field-label">市场</label>
                </div>
                <div class="md3-text-field md3-text-field-outlined md3-text-field-small">
                    <select class="md3-text-field-input" id="analysis-period">
                        <option value="1m">1个月</option>
                        <option value="3m">3个月</option>
                        <option value="6m">6个月</option>
                        <option value="1y" selected>1年</option>
                    </select>
                    <label class="md3-text-field-label">周期</label>
                </div>
                <button id="refresh-btn" class="md3-button md3-button-filled md3-button-small">
                    <i class="material-icons">refresh</i>
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 系统指标区域 -->
    <div id="system-indicators">
        <!-- 综合评分和关键指标 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 综合评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">analytics</i> 综合评分
                    </h3>
                    <p class="md3-card-subtitle">基于多维度分析的投资评分</p>
                </div>
                <div class="md3-card-body" style="text-align: center; padding: 32px;">
                    <div id="score-container">
                        <div class="score-circle">
                            <div class="shimmer-skeleton" style="width: 160px; height: 160px; border-radius: 50%;"></div>
                            <div class="score-display">
                                <div class="shimmer-skeleton" style="width: 60px; height: 48px; margin: 0 auto 8px;"></div>
                                <div class="shimmer-skeleton" style="width: 80px; height: 16px; margin: 0 auto;"></div>
                            </div>
                        </div>
                    </div>
                    <div id="recommendation-container" style="margin-top: 24px;">
                        <div class="shimmer-skeleton" style="width: 120px; height: 32px; margin: 0 auto;"></div>
                    </div>

                    <!-- 评分明细展开/收起按钮 -->
                    <div id="score-details-toggle" style="margin-top: 24px; display: none;">
                        <button class="md3-button md3-button-text" onclick="toggleScoreDetails()">
                            <i class="material-icons" id="score-details-icon">expand_more</i>
                            <span id="score-details-text">查看评分明细</span>
                        </button>
                    </div>

                    <!-- 评分明细区域 -->
                    <div id="score-details-container" style="margin-top: 24px; text-align: left;">
                        <!-- 评分明细内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 关键指标卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">speed</i> 关键指标
                    </h3>
                    <p class="md3-card-subtitle">重要技术指标快速概览</p>
                </div>
                <div class="md3-card-body">
                    <div class="stock-metrics-grid">
                        <div class="metric-card" id="rsi-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="ma-trend-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="macd-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="volume-trend-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Material Design 3 图表区域 -->
        <div class="md3-card md3-card-elevated chart-container md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <h3 class="md3-card-title">
                    <i class="material-icons">show_chart</i> 技术分析图表
                </h3>
                <p class="md3-card-subtitle">价格走势与技术指标分析</p>
            </div>

            <!-- Enhanced Material Design 3 Chart Tabs -->
            <div class="chart-tabs">
                <div class="md3-tabs">
                    <div class="md3-tab-bar">
                        <button class="md3-tab md3-tab-active" id="price-tab" data-target="price-content">
                            <i class="material-icons">candlestick_chart</i>
                            <span>价格趋势</span>
                        </button>
                        <button class="md3-tab" id="indicators-tab" data-target="indicators-content">
                            <i class="material-icons">analytics</i>
                            <span>技术指标</span>
                        </button>
                        <button class="md3-tab" id="volume-tab" data-target="volume-content">
                            <i class="material-icons">bar_chart</i>
                            <span>成交量</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="md3-card-body" style="padding: 0;">
                <div class="md3-tab-content">
                    <div class="md3-tab-panel md3-tab-panel-active" id="price-content">
                        <div id="price-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                    <div class="md3-tab-panel" id="indicators-content">
                        <div id="indicators-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                    <div class="md3-tab-panel" id="volume-content">
                        <div id="volume-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Material Design 3 支撑压力位和雷达图 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 支撑与压力位卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">support</i> 支撑与压力位
                    </h3>
                    <p class="md3-card-subtitle">关键价格支撑与阻力位分析</p>
                </div>
                <div class="md3-card-body">
                    <div id="support-resistance-container">
                        <div class="shimmer-skeleton" style="width: 100%; height: 200px;"></div>
                    </div>
                </div>
            </div>

            <!-- 多维度评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">radar</i> 多维度评分
                    </h3>
                    <p class="md3-card-subtitle">技术面、基本面、资金面综合分析</p>
                </div>
                <div class="md3-card-body">
                    <div id="radar-chart-container">
                        <div class="shimmer-skeleton" style="width: 100%; height: 250px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 AI分析区域 -->
    <div class="md3-card md3-card-elevated md3-animate-slide-in-up">
        <div class="md3-card-header">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div>
                    <h3 class="md3-card-title">
                        <i class="material-icons">psychology</i> AI智能分析报告
                    </h3>
                    <p class="md3-card-subtitle">基于深度学习的股票投资分析</p>
                </div>
                <div>
                    <span id="ai-analysis-status" class="md3-badge md3-badge-info">准备分析...</span>
                </div>
            </div>
        </div>
        <div class="md3-card-body">
            <div class="ai-analysis-section">
                <!-- Enhanced Material Design 3 AI分析加载动画 -->
                <div id="ai-analysis-loader" class="ai-loader">
                    <div class="md3-progress-indicator" style="margin-bottom: 24px;"></div>
                    <h4 style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 16px;">
                        正在对 <span id="ai-analysis-stock-name" style="color: var(--md-sys-color-primary);">该股票</span> 进行智能分析
                    </h4>
                    <p style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); margin-bottom: 8px;">
                        已用时 <span id="ai-processing-time" style="color: var(--md-sys-color-primary); font-weight: 500;">0</span> 秒
                    </p>
                    <p id="ai-analysis-tips" style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-small-font); font-size: var(--md-sys-typescale-body-small-size); margin-bottom: 16px; font-style: italic;">
                        正在分析技术指标和基本面数据...
                    </p>
                    <div class="typing-animation">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <div style="width: 300px; height: 8px; background: var(--md-sys-color-surface-container-high); border-radius: var(--md-sys-shape-corner-small); margin: 24px 0; overflow: hidden;">
                        <div id="ai-progress-bar" style="height: 100%; background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-secondary)); width: 0%; transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);"></div>
                    </div>
                    <button id="cancel-analysis-btn" class="md3-button md3-button-outlined md3-button-small">
                        <i class="material-icons">close</i> 取消分析
                    </button>
                </div>

                <!-- AI分析内容 -->
                <div id="ai-analysis-content" style="opacity: 0; transition: opacity var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);">
                    <div id="ai-analysis" style="font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); line-height: 1.6; color: var(--md-sys-color-on-surface);">
                        <!-- AI分析内容将异步填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 错误重试区域 -->
    <div id="error-retry" style="display: none; margin-top: 32px;">
        <div class="md3-card md3-card-elevated" style="background: var(--md-sys-color-error-container); border: 1px solid var(--md-sys-color-error);">
            <div class="md3-card-body" style="text-align: center; padding: 32px;">
                <i class="material-icons" style="font-size: 48px; color: var(--md-sys-color-on-error-container); margin-bottom: 16px;">warning</i>
                <h4 style="color: var(--md-sys-color-on-error-container); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 16px;">分析过程中遇到错误</h4>
                <button id="retry-button" class="md3-button md3-button-filled">
                    <i class="material-icons">refresh</i> 重试分析
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const stockCode = '{{ stock_code }}';
    let marketType = '{{ market_type }}';
    let period = '1y';
    let stockData = [];
    let analysisResult = null;
    let processingTimer;
    let processingTime = 0;
    
    // 页面加载时的闪烁效果
    function addShimmerEffect() {
        let style = document.createElement('style');
        style.innerHTML = `
            .shimmer-bg {
                background: #f6f7f8;
                background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
                background-repeat: no-repeat;
                background-size: 800px 104px;
                display: inline-block;
                position: relative;
                animation-duration: 1.5s;
                animation-fill-mode: forwards;
                animation-iteration-count: infinite;
                animation-name: shimmer;
                animation-timing-function: linear;
                border-radius: 4px;
            }
            @keyframes shimmer {
                0% { background-position: -468px 0 }
                100% { background-position: 468px 0 }
            }
        `;
        document.head.appendChild(style);
    }

    $(document).ready(function() {
        console.log('页面加载完成，初始化股票详情页面');

        // 清理可能残留的轮询状态
        cleanupPollingState();

        addShimmerEffect();

        // 初始加载
        loadStockData();

        // 刷新按钮点击事件
        $('#refresh-btn').click(function() {
            console.log('用户点击刷新按钮');
            marketType = $('#market-type').val();
            period = $('#analysis-period').val();
            loadStockData();
        });

        // 市场类型改变事件
        $('#market-type').change(function() {
            marketType = $(this).val();
            console.log(`市场类型变更为: ${marketType}`);
        });

        // 分析周期改变事件
        $('#analysis-period').change(function() {
            period = $(this).val();
            console.log(`分析周期变更为: ${period}`);
        });

        // 取消分析按钮
        $('#cancel-analysis-btn').click(function() {
            console.log('用户点击取消分析按钮');
            cancelAnalysis();
        });

        // 重试按钮
        $('#retry-button').click(function() {
            console.log('用户点击重试按钮');
            $('#error-retry').hide();
            startAIAnalysis();
        });

        // 页面离开时清理状态
        $(window).on('beforeunload', function() {
            console.log('页面即将离开，清理轮询状态');
            cleanupPollingState();
        });

        // 页面隐藏时不清理状态，保持任务连续性
        $(document).on('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏，但保持轮询状态以确保任务连续性');
            } else {
                console.log('页面重新显示，轮询状态保持不变');
            }
        });

        // Enhanced Material Design 3 Tab functionality
        $('.md3-tab').click(function() {
            console.log('标签切换被点击:', $(this).attr('id'));
            const targetId = $(this).data('target');
            console.log('目标面板ID:', targetId);

            // Remove active class from all tabs and panels
            $('.md3-tab').removeClass('md3-tab-active');
            $('.md3-tab-panel').removeClass('md3-tab-panel-active');

            // Add active class to clicked tab and corresponding panel
            $(this).addClass('md3-tab-active');
            $(`#${targetId}`).addClass('md3-tab-panel-active');

            console.log('标签切换完成，当前激活面板:', targetId);
        });
    });

    // 加载股票数据
    function loadStockData() {
        resetUI();
        showSystemLoadingState();
        
        // 获取股票数据
        $.ajax({
            url: `/api/stock_data?stock_code=${stockCode}&market_type=${marketType}&period=${period}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (!response.data || response.data.length === 0) {
                    hideSystemLoadingState();
                    showError('未找到股票数据');
                    return;
                }

                stockData = response.data;
                
                // 渲染系统指标部分
                renderSystemIndicators();
                
                // 系统指标加载完成后，异步启动AI分析
                startAIAnalysis();
            },
            error: function(xhr, status, error) {
                hideSystemLoadingState();
                
                let errorMsg = '获取股票数据失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }
                showError(errorMsg);
            }
        });
    }
    
    // 重置UI状态
    function resetUI() {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        processingTime = 0;
        
        // 隐藏错误重试区域
        $('#error-retry').hide();
        
        // 重置AI分析区域
        $('#ai-analysis-status').text('准备分析...').removeClass('bg-success bg-danger').addClass('bg-info');
        $('#ai-analysis-content').css('opacity', '0');
        $('#ai-analysis-loader').show();
        $('#ai-progress-bar').css('width', '0%');
        $('#ai-processing-time').text('0');
    }
    
    // 显示系统指标加载状态
    function showSystemLoadingState() {
        // 已通过闪烁效果展示
    }
    
    // 隐藏系统指标加载状态
    function hideSystemLoadingState() {
        // 闪烁效果会在渲染实际内容时被替换
    }
    
    // 渲染系统指标部分
    function renderSystemIndicators() {
        try {
            if (!stockData || stockData.length === 0) {
                showError("无可用数据");
                return;
            }
            
            const latestData = stockData[stockData.length - 1];
            const prevData = stockData.length > 1 ? stockData[stockData.length - 2] : latestData;
            
            // 设置标题和基本信息
            updateStockInfo(latestData);
            
            // 渲染价格图表
            renderPriceChart();
            
            // 渲染指标图表
            renderIndicatorsChart();
            
            // 渲染成交量图表
            renderVolumeChart();
            
            // 获取或计算支撑压力位
            calculateAndRenderSupportResistance(latestData);
            
            // 调用后端API获取统一评分
            fetchAndRenderScore();
            
            // 隐藏加载状态
            hideSystemLoadingState();
        } catch (error) {
            console.error("渲染系统指标时出错:", error);
            showError("渲染系统指标时出错: " + error.message);
        }
    }

    // 添加到全局作用域
    function safeGet(obj, path, defaultValue) {
        if (!obj) return defaultValue;
        
        const props = path.split('.');
        let current = obj;
        
        for (let i = 0; i < props.length; i++) {
            if (current === undefined || current === null) {
                console.warn(`属性路径 ${path} 在 ${props.slice(0, i).join('.')} 处中断`);
                return defaultValue;
            }
            current = current[props[i]];
        }
        
        return current !== undefined && current !== null ? current : defaultValue;
    }
    
    // 更新股票基本信息
    function updateStockInfo(latestData) {

        console.info('数据集📚:', latestData);

        // 获取最新价格和涨跌
        const currentPrice = parseFloat(latestData.close);
        const previousPrice = stockData.length > 1 ? parseFloat(stockData[stockData.length - 2].close) : currentPrice;
        const priceChange = currentPrice - previousPrice;
        const priceChangePercent = (priceChange / previousPrice) * 100;

        // 尝试从多个来源获取股票名称
        let stockName = "加载中...";
        let industry = "行业数据加载中...";

        // 调试：检查latestData中的字段
        console.log('latestData字段:', Object.keys(latestData));
        console.log('latestData.stockName:', latestData.stockName);
        console.log('latestData.stock_name:', latestData.stock_name);

        // 1. 首先尝试从latestData获取股票名称
        if (latestData.stockName && latestData.stockName !== "未知") {
            stockName = latestData.stockName;
            console.log('使用latestData.stockName:', stockName);

            // 设置页面中的股票代码和名称
            $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
            $('#stock-info').text(`${industry} | 数据日期: ${new Date(latestData.date).toLocaleDateString()}`);
        } else if (latestData.stock_name && latestData.stock_name !== "未知") {
            stockName = latestData.stock_name;
            console.log('使用latestData.stock_name:', stockName);

            // 设置页面中的股票代码和名称
            $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
            $('#stock-info').text(`${industry} | 数据日期: ${new Date(latestData.date).toLocaleDateString()}`);
        } else {
            // 2. 如果没有股票名称，立即发起API请求获取基本信息
            console.log('latestData中没有有效的股票名称，调用fetchStockBasicInfo');

            // 先设置临时的加载状态
            $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
            $('#stock-info').text(`${industry} | 数据日期: ${new Date(latestData.date).toLocaleDateString()}`);

            // 然后异步获取股票名称
            fetchStockBasicInfo(stockCode, marketType);
        }
        // 根据市场类型显示不同标签
        let marketBadge = '';
        if (marketType === 'A') {
            marketBadge = '<span class="badge bg-primary">A股</span>';
        } else if (marketType === 'HK') {
            marketBadge = '<span class="badge bg-success">港股</span>';
        } else if (marketType === 'US') {
            marketBadge = '<span class="badge bg-info">美股</span>';
        }
        $('#market-badge').html(marketBadge);
        
        // 更新股票信息
        // const industry = latestData.industry || "未知行业";
        const date = new Date(latestData.date).toLocaleDateString();
        // $('#stock-info').text(`${industry} | 数据日期: ${date}`);
        
        // 更新价格
        const priceChangeClass = priceChange >= 0 ? 'trend-up' : 'trend-down';
        const priceChangeIcon = priceChange >= 0 ? '<i class="fas fa-caret-up"></i>' : '<i class="fas fa-caret-down"></i>';
        const priceChangeDisplay = `${priceChangeIcon} ${Math.abs(priceChange).toFixed(2)} (${Math.abs(priceChangePercent).toFixed(2)}%)`;
        
        $('#price-container').html(`
            <div class="stock-price ${priceChangeClass}">${currentPrice.toFixed(2)}</div>
            <div class="price-change ${priceChangeClass}">${priceChangeDisplay}</div>
        `);
        
        // 更新开盘、最高、最低价格，并清除shimmer-skeleton样式
        $('#open-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.open ? parseFloat(latestData.open).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        $('#high-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.high ? parseFloat(latestData.high).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        $('#low-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.low ? parseFloat(latestData.low).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        // 更新成交量信息，并清除shimmer-skeleton样式
        const volume = latestData.volume;
        let volumeText = '--';
        if (volume && volume > 0) {
            if (volume >= 100000000) {
                volumeText = (volume / 100000000).toFixed(2) + '亿';
            } else if (volume >= 10000) {
                volumeText = (volume / 10000).toFixed(2) + '万';
            } else {
                volumeText = volume.toFixed(0);
            }
        }
        $('#volume-info')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(volumeText)
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        // 更新AI分析股票名称
        $('#ai-analysis-stock-name').text(stockName);
    }

    // 常见股票代码到名称的映射表
    const stockNameMap = {
        '000001': '平安银行',
        '000002': '万科A',
        '000858': '五粮液',
        '000725': '京东方A',
        '600519': '贵州茅台',
        '600036': '招商银行',
        '000858': '五粮液',
        '002415': '海康威视',
        '600276': '恒瑞医药',
        '000063': '中兴通讯',
        '002594': '比亚迪',
        '600887': '伊利股份',
        '000166': '申万宏源',
        '600031': '三一重工',
        '002304': '洋河股份',
        '600900': '长江电力',
        '000568': '泸州老窖',
        '002142': '宁波银行',
        '600585': '海螺水泥',
        '000338': '潍柴动力'
    };

    // 获取股票基本信息的函数
    function fetchStockBasicInfo(stockCode, marketType) {
        // 首先尝试从本地映射表获取股票名称
        const localStockName = stockNameMap[stockCode];
        if (localStockName) {
            const stockName = localStockName;
            const industry = "未知行业";

            // 调试：检查元素是否存在
            console.log('检查#stock-title元素:', $('#stock-title').length);
            console.log('当前#stock-title内容:', $('#stock-title').html());

            // 更新股票标题 - 使用完整的HTML结构
            $('#stock-title').html(`
                <h1 style="font-size: 28px; font-weight: 600; margin: 0; color: var(--md-sys-color-on-surface); line-height: 1.2;">
                    ${stockName} <span style="color: var(--md-sys-color-outline); font-size: 20px; font-weight: 400;">${stockCode}</span>
                </h1>
                <div style="color: var(--md-sys-color-outline); font-size: 14px; margin-top: 4px;">
                    ${industry} | 数据日期: ${new Date().toLocaleDateString()}
                </div>
            `);

            // 调试：检查更新后的内容
            console.log('更新后#stock-title内容:', $('#stock-title').html());

            // 更新AI分析区域的股票名称
            $('#ai-analysis-stock-name').text(stockName);

            console.log(`使用本地映射获取股票名称: ${stockName}`);
            return;
        }

        // 如果本地映射表没有，再尝试API调用
        $.ajax({
            url: '/api/stock_basic_info',
            method: 'GET',
            data: {
                stock_code: stockCode,
                market_type: marketType || 'A'
            },
            success: function(response) {
                if (response && response.stock_name) {
                    // 更新股票名称
                    const stockName = response.stock_name;
                    const industry = response.industry || "未知行业";

                    // 更新股票标题 - 使用完整的HTML结构
                    $('#stock-title').html(`
                        <h1 style="font-size: 28px; font-weight: 600; margin: 0; color: var(--md-sys-color-on-surface); line-height: 1.2;">
                            ${stockName} <span style="color: var(--md-sys-color-outline); font-size: 20px; font-weight: 400;">${stockCode}</span>
                        </h1>
                        <div style="color: var(--md-sys-color-outline); font-size: 14px; margin-top: 4px;">
                            ${industry} | 数据日期: ${new Date().toLocaleDateString()}
                        </div>
                    `);

                    // 更新AI分析区域的股票名称
                    $('#ai-analysis-stock-name').text(stockName);

                    console.log(`成功获取股票基本信息: ${stockName} (${industry})`);
                } else {
                    console.warn('股票基本信息响应格式异常:', response);
                    // 使用备用名称
                    useFallbackStockName(stockCode);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取股票基本信息失败:', error);
                // 使用备用名称
                useFallbackStockName(stockCode);
            }
        });
    }

    // 使用备用股票名称的函数
    function useFallbackStockName(stockCode) {
        let fallbackName;

        // 根据股票代码前缀判断市场和生成合适的名称
        if (stockCode.startsWith('00')) {
            fallbackName = `深市股票${stockCode}`;
        } else if (stockCode.startsWith('60')) {
            fallbackName = `沪市股票${stockCode}`;
        } else if (stockCode.startsWith('30')) {
            fallbackName = `创业板${stockCode}`;
        } else {
            fallbackName = `股票${stockCode}`;
        }

        // 更新股票标题 - 使用完整的HTML结构
        $('#stock-title').html(`
            <h1 style="font-size: 28px; font-weight: 600; margin: 0; color: var(--md-sys-color-on-surface); line-height: 1.2;">
                ${fallbackName} <span style="color: var(--md-sys-color-outline); font-size: 20px; font-weight: 400;">${stockCode}</span>
            </h1>
            <div style="color: var(--md-sys-color-outline); font-size: 14px; margin-top: 4px;">
                未知行业 | 数据日期: ${new Date().toLocaleDateString()}
            </div>
        `);

        // 更新AI分析区域的股票名称
        $('#ai-analysis-stock-name').text(fallbackName);

        console.log(`使用备用名称: ${fallbackName}`);
    }
    
    // 从后端API获取统一评分并渲染
    function fetchAndRenderScore() {
        console.log(`开始获取股票 ${stockCode} 的统一评分`);

        // 显示加载状态
        $('#score-container').html(`
            <div class="score-indicator">
                <div class="score-loading">
                    <div class="shimmer-skeleton" style="width: 160px; height: 160px; border-radius: 50%;"></div>
                    <div class="score-display">
                        <div class="shimmer-skeleton" style="width: 60px; height: 48px; margin: 0 auto 8px;"></div>
                        <div class="shimmer-skeleton" style="width: 80px; height: 16px; margin: 0 auto;"></div>
                    </div>
                </div>
            </div>
        `);

        $('#recommendation-container').html(`
            <div class="shimmer-skeleton" style="width: 120px; height: 32px; margin: 0 auto;"></div>
        `);

        // 调用后端统一评分API
        $.ajax({
            url: '/api/stock_score',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: marketType
            }),
            timeout: 15000, // 15秒超时
            success: function(response) {
                console.log('获取评分成功:', response);

                const score = response.score || 0;
                const scoreDetails = response.score_details || {};
                const recommendation = response.recommendation || '无建议';

                // 渲染评分
                const scoreClass = getScoreColorClass(score);
                const scoreText = getScoreDescription(score);

                $('#score-container').html(`
                    <div class="score-indicator">
                        <div id="score-chart"></div>
                        <div class="score-display">
                            <p class="score-value">${score}</p>
                            <p class="score-label">综合评分</p>
                        </div>
                    </div>
                `);

                // 渲染环形图表
                renderScoreChart(score);

                // 渲染建议
                $('#recommendation-container').html(`
                    <div class="badge badge-pill ${scoreClass} px-3 py-2">${scoreText}</div>
                `);

                // 显示评分明细按钮
                $('#score-details-toggle').show();

                // 渲染评分明细
                const scoreBreakdown = response.score_breakdown || {};
                renderScoreDetails(scoreDetails, score, scoreBreakdown);

                console.log(`股票 ${stockCode} 评分渲染完成: ${score}`);

                // 渲染技术指标（保留原有逻辑，但从stockData获取）
                if (stockData && stockData.length > 0) {
                    renderTechnicalIndicators(stockData[stockData.length - 1]);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取评分失败:', error);

                let errorMsg = '获取评分失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }

                // 显示错误状态
                $('#score-container').html(`
                    <div class="score-indicator">
                        <div class="score-error">
                            <i class="material-icons" style="font-size: 48px; color: #f44336;">error</i>
                            <div class="score-display">
                                <p class="score-value">--</p>
                                <p class="score-label">评分获取失败</p>
                            </div>
                        </div>
                    </div>
                `);

                $('#recommendation-container').html(`
                    <div class="badge badge-pill badge-secondary px-3 py-2">评分获取失败</div>
                `);

                // 显示错误提示
                showError(errorMsg);

                // 渲染技术指标（保留原有逻辑，但从stockData获取）
                if (stockData && stockData.length > 0) {
                    renderTechnicalIndicators(stockData[stockData.length - 1]);
                }
            }
        });
    }

    // 渲染技术指标（从原函数中分离出来）
    function renderTechnicalIndicators(latestData) {
        const rsi = parseFloat(latestData.RSI || 50);
        const ma5 = parseFloat(latestData.MA5 || 0);
        const ma20 = parseFloat(latestData.MA20 || 0);
        const ma60 = parseFloat(latestData.MA60 || 0);
        const macd = parseFloat(latestData.MACD || 0);
        const signal = parseFloat(latestData.Signal || 0);
        const volRatio = parseFloat(latestData.Volume_Ratio || 1);

        // 渲染关键指标
        $('#rsi-container').html(`
            <div class="stat-value">${rsi.toFixed(1)}</div>
            <div class="stat-label">RSI</div>
        `);

        const maTrend = ma5 > ma20 ? 'trend-up' : 'trend-down';
        const maTrendIcon = ma5 > ma20 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#ma-trend-container').html(`
            <div class="stat-value ${maTrend}">${maTrendIcon}</div>
            <div class="stat-label">均线趋势</div>
        `);

        const macdTrend = macd > signal ? 'trend-up' : 'trend-down';
        const macdTrendIcon = macd > signal ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#macd-container').html(`
            <div class="stat-value ${macdTrend}">${macdTrendIcon}</div>
            <div class="stat-label">MACD</div>
        `);

        const volumeStatus = volRatio > 1 ? 'trend-up' : 'trend-down';
        const volumeIcon = volRatio > 1 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#volume-container').html(`
            <div class="stat-value ${volumeStatus}">${volRatio.toFixed(1)}</div>
            <div class="stat-label">量比</div>
        `);

        // 计算成交量趋势指标
        const currentVolume = parseFloat(latestData.volume || 0);
        const avgVolume = parseFloat(latestData.Volume_MA || currentVolume);
        const volumeTrendRatio = avgVolume > 0 ? (currentVolume / avgVolume) : 1;
        const volumeTrendStatus = volumeTrendRatio > 1 ? 'trend-up' : 'trend-down';
        const volumeTrendIcon = volumeTrendRatio > 1 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#volume-trend-container').html(`
            <div class="stat-value ${volumeTrendStatus}">${volumeTrendIcon}</div>
            <div class="stat-label">成交量趋势</div>
        `);

        // 渲染雷达图
        renderRadarChart(rsi/10, (ma5 > ma20 ? 8 : 3), (macd > signal ? 8 : 3), (volRatio > 1 ? 8 : 3));
    }
    
    // 计算并渲染支撑压力位
    function calculateAndRenderSupportResistance(latestData) {
        const currentPrice = parseFloat(latestData.close);
        
        // 布林带支撑压力
        const upperBand = parseFloat(latestData.BB_upper || (currentPrice * 1.05));
        const lowerBand = parseFloat(latestData.BB_lower || (currentPrice * 0.95));
        
        // 移动平均线支撑压力
        const ma5 = parseFloat(latestData.MA5 || 0);
        const ma20 = parseFloat(latestData.MA20 || 0);
        const ma60 = parseFloat(latestData.MA60 || 0);
        
        // 计算支撑位
        const supports = [];
        const resistances = [];
        
        // 如果价格在均线上方，将均线作为支撑位
        if (currentPrice > ma5) supports.push({level: ma5, type: 'MA5', distance: ((ma5 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma5, type: 'MA5', distance: ((ma5 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        if (currentPrice > ma20) supports.push({level: ma20, type: 'MA20', distance: ((ma20 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma20, type: 'MA20', distance: ((ma20 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        if (currentPrice > ma60) supports.push({level: ma60, type: 'MA60', distance: ((ma60 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma60, type: 'MA60', distance: ((ma60 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        // 布林带支撑压力
        supports.push({level: lowerBand, type: '布林下轨', distance: ((lowerBand - currentPrice) / currentPrice * 100).toFixed(2)});
        resistances.push({level: upperBand, type: '布林上轨', distance: ((upperBand - currentPrice) / currentPrice * 100).toFixed(2)});
        
        // 添加整数关口
        const integerSupport = Math.floor(currentPrice);
        const integerResistance = Math.ceil(currentPrice);
        
        if (integerSupport < currentPrice) {
            supports.push({level: integerSupport, type: '整数关口', distance: ((integerSupport - currentPrice) / currentPrice * 100).toFixed(2)});
        }
        
        if (integerResistance > currentPrice) {
            resistances.push({level: integerResistance, type: '整数关口', distance: ((integerResistance - currentPrice) / currentPrice * 100).toFixed(2)});
        }
        
        // 按距离排序
        supports.sort((a, b) => parseFloat(b.distance) - parseFloat(a.distance));
        resistances.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
        
        // 渲染表格
        let tableHtml = `
            <table class="table table-sm support-resistance-table">
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>价格</th>
                        <th>距离</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        // 添加压力位
        resistances.slice(0, 3).forEach(r => {
            tableHtml += `
                <tr>
                    <td><span class="badge bg-danger">压力</span> ${r.type}</td>
                    <td>${r.level.toFixed(2)}</td>
                    <td>+${Math.abs(r.distance)}%</td>
                </tr>
            `;
        });
        
        // 添加当前价格
        tableHtml += `
            <tr class="table-info">
                <td><span class="badge bg-primary">当前</span></td>
                <td>${currentPrice.toFixed(2)}</td>
                <td>-</td>
            </tr>
        `;
        
        // 添加支撑位
        supports.slice(0, 3).forEach(s => {
            tableHtml += `
                <tr>
                    <td><span class="badge bg-success">支撑</span> ${s.type}</td>
                    <td>${s.level.toFixed(2)}</td>
                    <td>${s.distance}%</td>
                </tr>
            `;
        });
        
        tableHtml += `
                </tbody>
            </table>
        `;
        
        $('#support-resistance-container').html(tableHtml);
    }
    
    // 渲染评分图表
    function renderScoreChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 180,
                type: 'radialBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -135,
                    endAngle: 135,
                    hollow: {
                        margin: 0,
                        size: '70%',
                    },
                    dataLabels: {
                        show: false
                    },
                    track: {
                        background: '#f2f2f2',
                        strokeWidth: '97%',
                        margin: 5,
                        dropShadow: {
                            enabled: false
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: [getScoreGradientColor(score)],
                    inverseColors: true,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            stroke: {
                lineCap: 'round'
            },
            colors: [getScoreColor(score)]
        };

        const chart = new ApexCharts(document.querySelector("#score-chart"), options);
        chart.render();
    }
    
    // 渲染雷达图
    function renderRadarChart(rsiScore, trendScore, macdScore, volumeScore) {
        $('#radar-chart-container').html('<div id="radar-chart" style="height: 200px;"></div>');
        
        const options = {
            series: [{
                name: '评分',
                data: [rsiScore, trendScore, macdScore, volumeScore]
            }],
            chart: {
                height: 200,
                type: 'radar',
                toolbar: {
                    show: false
                },
                dropShadow: {
                    enabled: true,
                    blur: 1,
                    left: 1,
                    top: 1
                }
            },
            stroke: {
                width: 2
            },
            fill: {
                opacity: 0.2
            },
            markers: {
                size: 3
            },
            xaxis: {
                categories: ['RSI', '趋势', 'MACD', '成交量']
            },
            yaxis: {
                show: false,
                min: 0,
                max: 10
            },
            plotOptions: {
                radar: {
                    polygons: {
                        strokeColors: '#e9e9e9',
                        fill: {
                            colors: ['#f8f8f8', '#fff']
                        }
                    }
                }
            }
        };

        const chart = new ApexCharts(document.querySelector("#radar-chart"), options);
        chart.render();
    }
    
    // 渲染价格图表
    function renderPriceChart() {
        $('#price-chart').empty();
        
        try {
            // 准备数据
            const closePrices = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.close || 0)
            }));
            
            const ma5Data = stockData.map(item => ({
                x: new Date(item.date),
                y: item.MA5 ? parseFloat(item.MA5) : null
            }));

            const ma20Data = stockData.map(item => ({
                x: new Date(item.date),
                y: item.MA20 ? parseFloat(item.MA20) : null
            }));

            const ma60Data = stockData.map(item => ({
                x: new Date(item.date),
                y: item.MA60 ? parseFloat(item.MA60) : null
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: '价格',
                        type: 'line',
                        data: closePrices
                    },
                    {
                        name: 'MA5',
                        type: 'line',
                        data: ma5Data
                    },
                    {
                        name: 'MA20',
                        type: 'line',
                        data: ma20Data
                    },
                    {
                        name: 'MA60',
                        type: 'line',
                        data: ma60Data
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    }
                },
                stroke: {
                    width: [3, 2, 2, 2],
                    curve: 'smooth',
                    dashArray: [0, 0, 0, 0]
                },
                colors: ['#4e73df', '#36b9cc', '#1cc88a', '#f6c23e'],
                fill: {
                    type: 'solid',
                    opacity: [1, 0.8, 0.8, 0.8],
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value.toFixed(2);
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return value ? value.toFixed(2) : '-';
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                },
                grid: {
                    borderColor: '#e0e0e0',
                    strokeDashArray: 5,
                    xaxis: {
                        lines: {
                            show: false
                        }
                    },
                    yaxis: {
                        lines: {
                            show: true
                        }
                    }
                }
            };

            const chart = new ApexCharts(document.querySelector("#price-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染价格图表时出错:", error);
            $('#price-chart').html('<div class="alert alert-danger">价格图表渲染失败</div>');
        }
    }
    
    // 渲染技术指标图表
    function renderIndicatorsChart() {
        $('#indicators-chart').empty();
        
        try {
            // 准备数据
            const macdData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.MACD ? parseFloat(item.MACD) : 0
            }));

            const signalData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.Signal ? parseFloat(item.Signal) : 0
            }));

            const histogramData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.MACD_hist ? parseFloat(item.MACD_hist) : 0
            }));

            const rsiData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.RSI ? parseFloat(item.RSI) : 50
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: 'MACD',
                        type: 'line',
                        data: macdData
                    },
                    {
                        name: 'Signal',
                        type: 'line',
                        data: signalData
                    },
                    {
                        name: 'Histogram',
                        type: 'bar',
                        data: histogramData
                    },
                    {
                        name: 'RSI',
                        type: 'line',
                        data: rsiData
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true
                    }
                },
                stroke: {
                    width: [3, 3, 0, 3],
                    curve: 'smooth'
                },
                colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                plotOptions: {
                    bar: {
                        columnWidth: '80%'
                    }
                },
                fill: {
                    opacity: [1, 1, 0.8, 1]
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: [
                    {
                        title: {
                            text: 'MACD'
                        },
                        labels: {
                            formatter: function (value) {
                                return value.toFixed(3);
                            }
                        }
                    },
                    {
                        show: false,
                        seriesName: 'Signal'
                    },
                    {
                        show: false,
                        seriesName: 'Histogram'
                    },
                    {
                        opposite: true,
                        title: {
                            text: 'RSI'
                        },
                        min: 0,
                        max: 100,
                        seriesName: 'RSI',
                        labels: {
                            formatter: function (value) {
                                return value.toFixed(1);
                            }
                        }
                    }
                ],
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value, { seriesIndex }) {
                            if (seriesIndex === 0 || seriesIndex === 1 || seriesIndex === 2) {
                                return value.toFixed(3);
                            }
                            return value.toFixed(1);
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                }
            };

            const chart = new ApexCharts(document.querySelector("#indicators-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染指标图表时出错:", error);
            $('#indicators-chart').html('<div class="alert alert-danger">指标图表渲染失败</div>');
        }
    }
    
    // 渲染成交量图表
    function renderVolumeChart() {
        $('#volume-chart').empty();
        
        try {
            // 准备数据
            const volumeData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.volume ? parseFloat(item.volume) : 0
            }));

            const volMaData = stockData.map(item => ({
                x: new Date(item.date),
                y: item.Volume_MA ? parseFloat(item.Volume_MA) : 0
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: '成交量',
                        type: 'bar',
                        data: volumeData
                    },
                    {
                        name: '均量线',
                        type: 'line',
                        data: volMaData
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true
                    }
                },
                stroke: {
                    width: [0, 3],
                    curve: 'smooth'
                },
                colors: ['#4e73df', '#1cc88a'],
                fill: {
                    opacity: [0.8, 1]
                },
                plotOptions: {
                    bar: {
                        columnWidth: '80%'
                    }
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value;
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value, { seriesIndex }) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(2) + ' 百万';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(2) + ' 千';
                            }
                            return value;
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                }
            };

            const chart = new ApexCharts(document.querySelector("#volume-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染成交量图表时出错:", error);
            $('#volume-chart').html('<div class="alert alert-danger">成交量图表渲染失败</div>');
        }
    }
    
    // 启动AI分析
    function startAIAnalysis() {
        console.log(`开始启动AI分析，股票代码: ${stockCode}, 市场类型: ${marketType}`);

        resetAIAnalysisUI();

        // 启动处理时间计时器
        processingTime = 0;
        processingTimer = setInterval(function() {
            processingTime++;
            $('#ai-processing-time').text(processingTime);

            // 更新进度条 - 创建一个假的进度
            const progressPercent = Math.min(95, processingTime / 2);
            $('#ai-progress-bar').css('width', progressPercent + '%');
        }, 1000);

        // 启动AI分析任务
        $.ajax({
            url: '/api/start_stock_analysis',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: marketType
            }),
            timeout: 30000, // 30秒超时
            success: function(response) {
                console.log('AI分析任务启动响应:', response);

                // 验证响应格式
                if (!response || typeof response !== 'object') {
                    console.error('无效的响应格式:', response);
                    handleAIAnalysisError(null, 'invalid_response', '服务器响应格式错误');
                    return;
                }

                // 检查是否已有结果
                if (response.status === 'completed' && response.result) {
                    console.log('任务已完成，直接处理结果');
                    // 任务已完成，直接处理结果
                    handleAIAnalysisResult(response.result);
                    return;
                }

                // 验证任务ID
                if (!response.task_id || typeof response.task_id !== 'string') {
                    console.error('无效的任务ID:', response.task_id);
                    handleAIAnalysisError(null, 'invalid_task_id', '服务器返回无效的任务ID');
                    return;
                }

                console.log(`任务创建成功，任务ID: ${response.task_id}`);

                // 开始轮询任务状态
                pollAIAnalysisStatus(response.task_id);
            },
            error: function(xhr, status, error) {
                console.error('启动AI分析失败:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    error: error
                });
                handleAIAnalysisError(xhr, status, error);
            }
        });
    }
    
    // 轮询AI分析状态 - 优化版本
    function pollAIAnalysisStatus(taskId) {
        // 验证任务ID
        if (!taskId || typeof taskId !== 'string') {
            console.error('无效的任务ID:', taskId);
            handleAIAnalysisError(null, 'invalid_task_id', '无效的任务ID');
            return;
        }

        // 清理旧的轮询状态
        cleanupPollingState();

        // 保存当前任务ID，用于取消
        window.currentAnalysisTaskId = taskId;

        // 初始化轮询状态
        window.pollRetryCount = 0;
        window.pollStartTime = Date.now();
        window.maxPollRetries = 10; // 最大重试次数：10次
        window.maxPollDuration = 1800000; // 最大轮询时间：30分钟（适应长时间任务）

        console.log(`开始轮询任务状态: ${taskId}`);

        // 固定轮询间隔：30秒
        function getPollingInterval() {
            return 30000; // 统一使用30秒间隔
        }

        // 更新用户界面显示
        function updatePollingUI() {
            const elapsed = Math.floor((Date.now() - window.pollStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timeStr = minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`;

            // 更新处理时间显示
            $('#ai-processing-time').text(elapsed);

            // 根据时间长度显示不同的提示和状态
            let statusText = '正在分析...';
            let tipsText = '正在分析技术指标和基本面数据...';
            let statusClass = 'md3-badge-info';

            if (elapsed > 300) { // 5分钟以上
                statusText = `深度分析中... (${timeStr})`;
                tipsText = '正在进行复杂的量化分析，请耐心等待...';
                statusClass = 'md3-badge-warning';
            } else if (elapsed > 180) { // 3分钟以上
                statusText = `深度分析中... (${timeStr})`;
                tipsText = '正在分析历史数据和市场趋势...';
                statusClass = 'md3-badge-warning';
            } else if (elapsed > 60) { // 1分钟以上
                statusText = `智能分析中... (${timeStr})`;
                tipsText = '正在计算技术指标和风险评估...';
                statusClass = 'md3-badge-info';
            } else if (elapsed > 30) { // 30秒以上
                statusText = `分析中... (${timeStr})`;
                tipsText = '正在获取最新数据和基本面信息...';
                statusClass = 'md3-badge-info';
            }

            // 更新状态显示
            $('#ai-analysis-status')
                .text(statusText)
                .removeClass('md3-badge-info md3-badge-warning md3-badge-error md3-badge-success')
                .addClass(statusClass);

            // 更新提示信息
            $('#ai-analysis-tips').text(tipsText);
        }

        function checkStatus() {
            // 检查轮询超时
            const elapsed = Date.now() - window.pollStartTime;
            if (elapsed > window.maxPollDuration) {
                console.error(`轮询超时，已运行 ${elapsed/1000} 秒`);
                cleanupPollingState();
                handleAIAnalysisError(null, 'timeout', '分析超时，请重试');
                return;
            }

            // 检查重试次数
            if (window.pollRetryCount >= window.maxPollRetries) {
                console.error(`轮询重试次数超限: ${window.pollRetryCount}`);
                cleanupPollingState();
                handleAIAnalysisError(null, 'max_retries', '重试次数过多，请重试');
                return;
            }

            // 更新UI显示
            updatePollingUI();

            $.ajax({
                url: `/api/analysis_status/${taskId}`,
                type: 'GET',
                timeout: 15000, // 增加超时时间到15秒
                success: function(response) {
                    console.log(`轮询响应:`, response);

                    // 重置重试计数
                    window.pollRetryCount = 0;

                    // 根据任务状态处理
                    if (response.status === 'completed') {
                        // 分析完成，停止轮询和计时器
                        console.log('任务完成，停止轮询');
                        cleanupPollingState();

                        // 处理结果
                        handleAIAnalysisResult(response.result);
                    } else if (response.status === 'failed') {
                        // 分析失败，停止轮询
                        console.log('任务失败，停止轮询');
                        cleanupPollingState();

                        handleAIAnalysisError(null, 'failed', response.error || '未知错误');
                    } else {
                        // 更新进度
                        const progress = response.progress || 0;
                        $('#ai-progress-bar').css('width', progress + '%');
                        console.log(`任务进行中，进度: ${progress}%`);

                        // 继续轮询，使用动态间隔
                        const interval = getPollingInterval();
                        console.log(`下次轮询间隔: ${interval}ms`);
                        window.analysisStatusTimeout = setTimeout(checkStatus, interval);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("轮询状态出错:", {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        error: error,
                        taskId: taskId,
                        retryCount: window.pollRetryCount
                    });

                    // 统一错误处理：所有错误都使用相同的重试策略
                    window.pollRetryCount++;
                    console.warn(`轮询错误，重试次数: ${window.pollRetryCount}/${window.maxPollRetries}`);

                    if (window.pollRetryCount <= window.maxPollRetries) {
                        // 继续重试，使用固定30秒间隔
                        const retryDelay = 30000; // 30秒后重试
                        console.log(`轮询错误重试，${retryDelay}ms后重试`);
                        window.analysisStatusTimeout = setTimeout(checkStatus, retryDelay);
                        return;
                    } else {
                        // 重试次数用尽，但不清理状态，继续30秒后重试
                        console.warn('重试次数用尽，但继续保持轮询状态');
                        window.pollRetryCount = 0; // 重置重试计数，继续轮询
                        const retryDelay = 30000; // 30秒后继续
                        console.log(`重置重试计数，${retryDelay}ms后继续轮询`);
                        window.analysisStatusTimeout = setTimeout(checkStatus, retryDelay);
                        return;
                    }
                }
            });
        }

        // 立即执行一次
        checkStatus();
        console.log('智能轮询已启动，使用动态间隔策略');
    }
    
    // 处理AI分析结果
    function handleAIAnalysisResult(result) {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        
        // 更新AI分析状态
        $('#ai-analysis-status').text('分析完成').removeClass('bg-info bg-danger').addClass('bg-success');
        
        // 渲染AI分析内容
        const aiAnalysis = result.ai_analysis || '无法获取AI分析结果';
        $('#ai-analysis').html(formatAIAnalysis(aiAnalysis));
        
        // 隐藏加载动画，显示内容
        $('#ai-analysis-loader').fadeOut(300, function() {
            $('#ai-analysis-content').css('opacity', '1');
        });

        // 从AI分析结果中更新股票信息
        if (result.basic_info) {
            const stockName = result.basic_info.stock_name || $('#stock-title').text().split(' ')[0];
            const industry = result.basic_info.industry || $('#stock-info').text().split(' | ')[0];
            
            $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
            $('#stock-info').text(`${industry} | 数据日期: ${$('#stock-info').text().split(' | ')[1]}`);
            $('#ai-analysis-stock-name').text(stockName);
        }
}
    
    // 处理AI分析错误
    function handleAIAnalysisError(xhr, status, error) {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }

        // 更新AI分析状态
        $('#ai-analysis-status')
            .text('分析失败')
            .removeClass('md3-badge-info md3-badge-warning md3-badge-success')
            .addClass('md3-badge-error');

        // 构建友好的错误消息
        let errorMsg = '分析过程中出错';
        let userTip = '请稍后重试';

        if (status === 'timeout') {
            errorMsg = '分析超时';
            userTip = '该股票分析需要较长时间，请稍后重试';
        } else if (status === 'not_found') {
            errorMsg = '分析任务丢失';
            userTip = '任务可能因为网络问题丢失，请重新开始分析';
        } else if (status === 'network_error') {
            errorMsg = '网络连接问题';
            userTip = '请检查网络连接后重试';
        } else if (status === 'max_retries') {
            errorMsg = '重试次数过多';
            userTip = '服务器可能繁忙，请稍后重试';
        } else if (xhr && (xhr.status === 524 || xhr.status === 504)) {
            errorMsg = '服务器处理超时';
            userTip = '服务器处理时间过长，请稍后重试';
        } else if (xhr && xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg = xhr.responseJSON.error;
            userTip = '请检查股票代码是否正确';
        } else if (error) {
            errorMsg = error;
        }

        // 更新提示信息
        $('#ai-analysis-tips').text(userTip);

        // 显示错误重试区域
        $('#error-retry').show();

        // 隐藏加载动画
        $('#ai-analysis-loader').hide();

        // 显示详细错误信息
        console.error('AI分析错误详情:', {
            status: status,
            error: error,
            xhr: xhr,
            errorMsg: errorMsg,
            userTip: userTip
        });
    }
    
    // 取消AI分析
    function cancelAnalysis() {
        if (window.currentAnalysisTaskId) {
            $.ajax({
                url: `/api/cancel_analysis/${window.currentAnalysisTaskId}`,
                type: 'POST',
                success: function(response) {
                    // 停止计时器
                    if (processingTimer) {
                        clearInterval(processingTimer);
                        processingTimer = null;
                    }
                    
                    // 停止轮询
                    cleanupPollingState();
                    
                    // 更新UI
                    $('#ai-analysis-status').text('已取消').removeClass('bg-info bg-success').addClass('bg-warning');
                    $('#ai-analysis-loader').hide();
                    $('#ai-analysis').html('<div class="alert alert-warning">AI分析已取消，您可以点击"重试分析"按钮重新开始分析。</div>');
                    $('#ai-analysis-content').css('opacity', '1');
                    $('#error-retry').show();
                },
                error: function(error) {
                    console.error('取消分析失败:', error);
                }
            });
        }
    }
    
    // 清理轮询状态
    function cleanupPollingState() {
        console.log('清理轮询状态');

        // 停止轮询定时器
        if (window.analysisStatusInterval) {
            clearInterval(window.analysisStatusInterval);
            window.analysisStatusInterval = null;
            console.log('轮询定时器已清理');
        }

        // 停止轮询超时器
        if (window.analysisStatusTimeout) {
            clearTimeout(window.analysisStatusTimeout);
            window.analysisStatusTimeout = null;
            console.log('轮询超时器已清理');
        }

        // 清理轮询相关的全局变量
        window.currentAnalysisTaskId = null;
        window.pollRetryCount = 0;
        window.pollStartTime = null;
        window.maxPollRetries = null;
        window.maxPollDuration = null;

        console.log('轮询状态已清理');
    }

    // 重置AI分析UI
    function resetAIAnalysisUI() {
        console.log('重置AI分析UI');

        // 清理轮询状态
        cleanupPollingState();

        // 停止现有计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }

        // 重置处理时间
        processingTime = 0;
        $('#ai-processing-time').text('0');

        // 重置进度条
        $('#ai-progress-bar').css('width', '0%');

        // 更新状态显示
        $('#ai-analysis-status').text('正在分析...').removeClass('bg-success bg-danger bg-warning').addClass('bg-info');

        // 显示加载动画，隐藏内容
        $('#ai-analysis-loader').show();
        $('#ai-analysis-content').css('opacity', '0');

        // 隐藏错误重试区域
        $('#error-retry').hide();

        console.log('AI分析UI已重置');
    }
    
    // 格式化AI分析
    function formatAIAnalysis(text) {
    if (!text) return '';

    // 对文本进行HTML转义，确保安全
    const safeText = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

    // 替换 Markdown 元素
    let formatted = safeText
        // 标题 - 增加对####和#####的支持
        .replace(/^# (.*?)$/gm, '<h4 class="mt-3 mb-2">$1</h4>')
        .replace(/^## (.*?)$/gm, '<h5 class="mt-2 mb-2">$1</h5>')
        .replace(/^### (.*?)$/gm, '<h6 class="mt-2 mb-1">$1</h6>')
        .replace(/^#### (.*?)$/gm, '<h6 class="mt-2 mb-1">$1</h6>')
        .replace(/^##### (.*?)$/gm, '<div class="section-header bg-light p-2 rounded mb-2"><strong>$1</strong></div>')
        .replace(/^###### (.*?)$/gm, '<div class="subsection-header p-1 border-bottom mb-2"><strong>$1</strong></div>')
        
        // 加粗和斜体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/__(.*?)__/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/_(.*?)_/g, '<em>$1</em>')
        
        // 颜色标记
        .replace(/\[\[上涨\]\]/g, '<span class="trend-up"><i class="fas fa-arrow-up"></i> 上涨</span>')
        .replace(/\[\[下跌\]\]/g, '<span class="trend-down"><i class="fas fa-arrow-down"></i> 下跌</span>')
        
        // 应用特殊样式到金融术语
        .replace(/支撑位/g, '<span class="keyword">支撑位</span>')
        .replace(/压力位/g, '<span class="keyword">压力位</span>')
        .replace(/趋势/g, '<span class="keyword">趋势</span>')
        .replace(/均线/g, '<span class="keyword">均线</span>')
        .replace(/MACD/g, '<span class="term">MACD</span>')
        .replace(/RSI/g, '<span class="term">RSI</span>')
        .replace(/KDJ/g, '<span class="term">KDJ</span>')
        
        // 突出显示价格模式和变动
        .replace(/\b(上涨|升|涨)\b/g, '<span class="trend-up">$1</span>')
        .replace(/\b(下跌|降|跌)\b/g, '<span class="trend-down">$1</span>')
        .replace(/\b(买入|做多|多头|突破)\b/g, '<span class="trend-up">$1</span>')
        .replace(/\b(卖出|做空|空头|跌破)\b/g, '<span class="trend-down">$1</span>')
        
        // 突出显示价格数值 (匹配如 31.25, 120.50)
        .replace(/(\d+\.\d{2})/g, '<span class="price">$1</span>')
        
        // 将多个换行转换为段落
        .replace(/\n\n+/g, '</p><p class="analysis-para">')
        .replace(/\n/g, '<br>');

    // 包装在段落标签中以保持一致的样式
    return '<p class="analysis-para">' + formatted + '</p>';
}
    
    // 辅助函数：获取评分颜色类
    function getScoreColorClass(score) {
        if (score >= 80) return 'bg-success';
        if (score >= 60) return 'bg-primary';
        if (score >= 40) return 'bg-warning text-dark';
        return 'bg-danger';
    }
    
    // 辅助函数：获取评分颜色
    function getScoreColor(score) {
        if (score >= 80) return '#28a745';
        if (score >= 60) return '#4e73df';
        if (score >= 40) return '#f6c23e';
        return '#e74a3b';
    }
    
    // 辅助函数：获取评分梯度颜色
    function getScoreGradientColor(score) {
        if (score >= 80) return '#1cc88a';
        if (score >= 60) return '#36b9cc';
        if (score >= 40) return '#f6c23e';
        return '#e74a3b';
    }
    
    // 辅助函数：获取评分描述
    function getScoreDescription(score) {
        if (score >= 90) return '强烈推荐';
        if (score >= 80) return '推荐';
        if (score >= 70) return '持续关注';
        if (score >= 60) return '关注';
        if (score >= 50) return '观望';
        if (score >= 40) return '中性';
        if (score >= 30) return '减持';
        return '卖出';
    }
    
    // 显示错误提示
    function showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        $('#alerts-container').html(alertHtml);
    }

    // 评分明细展开/收起功能
    let scoreDetailsExpanded = false;

    function toggleScoreDetails() {
        const container = $('#score-details-container');
        const icon = $('#score-details-icon');
        const text = $('#score-details-text');

        if (scoreDetailsExpanded) {
            // 收起
            container.removeClass('expanded');
            icon.text('expand_more');
            text.text('查看评分明细');
            scoreDetailsExpanded = false;
        } else {
            // 展开
            container.addClass('expanded');
            icon.text('expand_less');
            text.text('收起评分明细');
            scoreDetailsExpanded = true;
        }
    }

    // 渲染评分明细
    function renderScoreDetails(scoreDetails, totalScore, scoreBreakdown = {}) {
        if (!scoreDetails || Object.keys(scoreDetails).length === 0) {
            return;
        }

        // 评分维度配置
        const dimensionConfig = {
            trend: {
                name: '趋势分析',
                description: '基于移动平均线和价格趋势的技术分析',
                weight: 30,
                maxScore: 30,
                icon: 'trending_up'
            },
            technical: {
                name: '技术指标',
                description: '包括RSI、MACD、布林带等技术指标综合评估',
                weight: 25,
                maxScore: 25,
                icon: 'show_chart'
            },
            volume: {
                name: '成交量分析',
                description: '基于成交量变化和资金流向的分析',
                weight: 20,
                maxScore: 20,
                icon: 'bar_chart'
            },
            volatility: {
                name: '波动率评估',
                description: '股价波动率和风险评估指标',
                weight: 15,
                maxScore: 15,
                icon: 'timeline'
            },
            momentum: {
                name: '动量指标',
                description: '价格动量和变化率分析',
                weight: 10,
                maxScore: 10,
                icon: 'speed'
            }
        };

        let detailsHtml = `
            <div class="score-details-summary">
                <h4>
                    <i class="material-icons">info</i>
                    评分计算说明
                </h4>
                <p>综合评分基于五个核心维度的加权计算，总分100分。各维度权重和评分标准如下：</p>
            </div>
        `;

        // 渲染各个维度
        Object.keys(dimensionConfig).forEach(key => {
            const config = dimensionConfig[key];
            const score = scoreDetails[key] || 0;
            const percentage = (score / config.maxScore) * 100;
            const breakdown = scoreBreakdown[key] || {};
            const hasDetailedInfo = breakdown.indicators && breakdown.logic;

            detailsHtml += `
                <div class="score-details-item">
                    <div class="score-dimension">
                        <div class="score-dimension-header">
                            <div class="score-dimension-name">
                                <i class="material-icons" style="font-size: 18px; margin-right: 8px; color: var(--md-sys-color-primary);">${config.icon}</i>
                                ${config.name}
                                <span class="score-weight-display">(权重: ${config.weight}%)</span>
                            </div>
                            ${hasDetailedInfo ? `
                                <button class="score-detail-toggle-btn"
                                        onclick="toggleScoreDetail('${key}')"
                                        aria-label="查看${config.name}详情">
                                    <i class="material-icons">info</i>
                                    <span class="detail-toggle-text">查看详情</span>
                                </button>
                            ` : ''}
                        </div>
                        <div class="score-dimension-desc">${config.description}</div>
                    </div>
                    <div class="score-progress-container">
                        <div class="score-progress-bar">
                            <div class="score-progress-fill" style="width: ${percentage}%;"></div>
                        </div>
                        <div class="score-value-display">${score}/${config.maxScore}</div>
                    </div>
                    ${hasDetailedInfo ? `
                        <div class="score-detail-panel" id="detail-${key}" style="display: none;">
                            <div class="score-detail-content">
                                <div class="score-detail-header">
                                    <h5 class="score-detail-title">
                                        <i class="material-icons">analytics</i>
                                        评分依据详情
                                    </h5>
                                    <button class="score-detail-close-btn"
                                            onclick="toggleScoreDetail('${key}')"
                                            aria-label="关闭详情">
                                        <i class="material-icons">close</i>
                                        <span>收起详情</span>
                                    </button>
                                </div>

                                <div class="score-indicators-section">
                                    <h6>关键指标数值</h6>
                                    <div class="indicators-grid">
                                        ${Object.entries(breakdown.indicators || {}).map(([indicator, value]) => `
                                            <div class="indicator-item">
                                                <span class="indicator-name">${indicator}:</span>
                                                <span class="indicator-value">${typeof value === 'number' ? value.toFixed(2) : value}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>

                                <div class="score-logic-section">
                                    <h6>评分逻辑</h6>
                                    <div class="logic-list">
                                        ${(breakdown.logic || []).map(logic => `
                                            <div class="logic-item">${logic}</div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        // 添加总分说明
        detailsHtml += `
            <div class="score-details-item" style="border-top: 2px solid var(--md-sys-color-primary); margin-top: 16px; padding-top: 16px;">
                <div class="score-dimension">
                    <div class="score-dimension-name">
                        <i class="material-icons" style="font-size: 18px; margin-right: 8px; color: var(--md-sys-color-primary);">analytics</i>
                        综合评分
                    </div>
                    <div class="score-dimension-desc">基于各维度加权计算的最终投资评分</div>
                </div>
                <div class="score-progress-container">
                    <div class="score-progress-bar">
                        <div class="score-progress-fill" style="width: ${totalScore}%; background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-tertiary));"></div>
                    </div>
                    <div class="score-value-display" style="font-size: 18px; font-weight: bold;">${totalScore}/100</div>
                </div>
            </div>
        `;

        $('#score-details-container').html(detailsHtml);
    }

    // 切换评分详情面板显示
    function toggleScoreDetail(dimensionKey) {
        const panel = $(`#detail-${dimensionKey}`);
        const toggleButton = panel.siblings('.score-dimension').find('.score-detail-toggle-btn');
        const toggleIcon = toggleButton.find('.material-icons');
        const toggleText = toggleButton.find('.detail-toggle-text');

        if (panel.is(':visible')) {
            // 关闭当前面板
            panel.slideUp(300);
            toggleIcon.text('info');
            toggleText.text('查看详情');
            toggleButton.removeClass('active');
        } else {
            // 先关闭其他已打开的面板
            $('.score-detail-panel:visible').slideUp(300);
            $('.score-detail-toggle-btn .material-icons').text('info');
            $('.score-detail-toggle-btn .detail-toggle-text').text('查看详情');
            $('.score-detail-toggle-btn').removeClass('active');

            // 打开当前面板
            panel.slideDown(300);
            toggleIcon.text('info_outline');
            toggleText.text('收起详情');
            toggleButton.addClass('active');
        }
    }

    // 显示信息提示
    function showInfo(message) {
        const alertHtml = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        $('#alerts-container').html(alertHtml);
    }
</script>
{% endblock %}