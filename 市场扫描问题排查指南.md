# 市场扫描长时间运行问题排查指南

## 问题现象
市场扫描页面运行超过30分钟仍显示"正在扫描市场"状态，无法完成或显示结果。

## 已实施的改进措施

### 1. 超时控制机制 ✅
- **数据获取超时**: 每个股票数据获取限制在30秒内
- **快速分析超时**: 单个股票分析限制在20秒内  
- **任务级别超时**: 整体扫描任务监控

### 2. 错误处理优化 ✅
- **网络重试机制**: 自动重试网络连接失败3次
- **指数退避策略**: 重试间隔逐渐增加(1s, 2s, 4s)
- **错误分类处理**: 区分网络错误、数据错误、超时错误

### 3. 任务取消功能 ✅
- **取消按钮**: 扫描过程中显示取消按钮
- **状态同步**: 取消后立即更新任务状态
- **资源清理**: 正确释放后台线程资源

### 4. 进度监控增强 ✅
- **详细进度**: 显示已处理/总数、找到数量、失败数量
- **时间预估**: 基于当前速度预估剩余时间
- **实时统计**: 成功、失败、超时股票分别统计

### 5. 批处理优化 ✅
- **批次大小**: 从10只股票减少到5只，提高响应性
- **并发控制**: 避免过多并发请求导致资源竞争
- **内存管理**: 及时清理中间结果

## 排查步骤

### 第一步：检查网络连接
```bash
# 检查是否能访问数据源
curl -I https://17.push2.eastmoney.com
ping 17.push2.eastmoney.com
```

**常见问题**:
- 代理设置问题
- 防火墙阻止
- DNS解析失败

### 第二步：查看日志文件
```bash
# 查看最新日志
tail -f flask_app.log

# 搜索错误信息
grep -i "error\|timeout\|failed" flask_app.log
```

**关键日志信息**:
- `ProxyError`: 代理连接问题
- `RemoteDisconnected`: 远程连接断开
- `TimeoutError`: 请求超时
- `扫描任务 xxx 失败`: 任务执行失败

### 第三步：测试单个股票分析
```python
# 在Python控制台测试
from stock_analyzer import StockAnalyzer
analyzer = StockAnalyzer()

# 测试单个股票
try:
    result = analyzer.quick_analyze_stock("000001", "A", timeout=15)
    print(f"分析成功: {result['score']}")
except Exception as e:
    print(f"分析失败: {e}")
```

### 第四步：检查系统资源
```bash
# 检查内存使用
free -h

# 检查CPU使用
top -p $(pgrep -f python)

# 检查磁盘空间
df -h
```

### 第五步：使用测试脚本
```bash
# 运行改进测试脚本
python test_market_scan_improvements.py
```

## 解决方案

### 方案1：网络问题解决
```python
# 在stock_analyzer.py中添加代理设置
import os
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

# 或者设置代理
# os.environ['http_proxy'] = 'http://proxy:port'
# os.environ['https_proxy'] = 'http://proxy:port'
```

### 方案2：减少扫描范围
- 选择较小的指数(如创业板50而非沪深300)
- 使用自定义股票列表(限制在20只以内)
- 提高最低分数阈值(从60提高到70)

### 方案3：调整超时参数
```python
# 在web_server.py中调整超时设置
analyzer.quick_analyze_stock(stock_code, market_type, timeout=10)  # 减少到10秒
```

### 方案4：启用调试模式
```python
# 在web_server.py中启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 预防措施

### 1. 定期维护
- 每周清理日志文件
- 监控系统资源使用
- 更新akshare库到最新版本

### 2. 监控设置
- 设置任务超时告警(超过5分钟)
- 监控错误率(失败率超过50%)
- 跟踪平均处理时间

### 3. 用户指导
- 建议非交易时间使用
- 推荐使用较小的股票列表
- 提供预估完成时间

## 常见错误码

| 错误类型 | 描述 | 解决方法 |
|---------|------|----------|
| ProxyError | 代理连接失败 | 检查网络设置，禁用代理 |
| TimeoutError | 请求超时 | 减少股票数量，检查网络 |
| RemoteDisconnected | 远程断开 | 重试或稍后再试 |
| 404 Not Found | 任务不存在 | 重新启动扫描任务 |
| 500 Internal Error | 服务器内部错误 | 查看详细日志，重启服务 |

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 错误发生的具体时间
2. 使用的股票列表或指数
3. 相关的日志文件内容
4. 系统环境信息(操作系统、Python版本等)

---
*最后更新: 2025-06-07*
*版本: 1.0*
