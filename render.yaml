services:
  - type: web
    name: stock-analysis-system
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 300 web_server:app
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PYTHONUNBUFFERED
        value: 1
      - key: PYTHONDONTWRITEBYTECODE
        value: 1
      - key: USE_DATABASE
        value: true
      - key: DATABASE_URL
        fromDatabase:
          name: stock-analysis-db
          property: connectionString
    healthCheckPath: /

  - type: pserv
    name: stock-analysis-db
    env: postgresql
    plan: free
    databaseName: stock_analysis
    user: stock_user
