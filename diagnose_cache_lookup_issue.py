#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断缓存查找问题
分析为什么股票000043.XSHE无法找到缓存数据
"""

import os
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_cache_structure(cache_dir="stock_cache"):
    """分析缓存目录结构"""
    print("=" * 60)
    print("缓存目录结构分析")
    print("=" * 60)
    
    cache_path = Path(cache_dir)
    if not cache_path.exists():
        print(f"❌ 缓存目录不存在: {cache_dir}")
        return False
    
    data_dir = cache_path / "data"
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 检查000开头的股票缓存
    zero_dir = data_dir / "000"
    if zero_dir.exists():
        print(f"✅ 找到000目录: {zero_dir}")
        
        # 列出所有000开头的缓存文件
        cache_files = list(zero_dir.glob("000043*"))
        print(f"\n000043相关的缓存文件:")
        if cache_files:
            for file in cache_files:
                print(f"  ✅ {file.name} (大小: {file.stat().st_size} bytes)")
        else:
            print(f"  ❌ 没有找到000043相关的缓存文件")
        
        # 列出前几个文件作为参考
        all_files = list(zero_dir.glob("*"))[:10]
        print(f"\n000目录中的前10个文件:")
        for file in all_files:
            print(f"  - {file.name}")
    else:
        print(f"❌ 000目录不存在: {zero_dir}")
    
    return True

def test_stock_code_normalization():
    """测试股票代码标准化逻辑"""
    print(f"\n" + "=" * 60)
    print("股票代码标准化测试")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache()
        
        test_codes = [
            "000043",
            "000043.XSHE",
            "000037",
            "000037.XSHE",
            "000042",
            "000042.XSHE"
        ]
        
        print("股票代码标准化结果:")
        for code in test_codes:
            normalized = cache._normalize_stock_code(code)
            cache_path = cache._get_cache_file_path(code)
            print(f"  {code:<12} -> {normalized:<8} -> {cache_path.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票代码标准化测试失败: {e}")
        return False

def test_cache_lookup_logic():
    """测试缓存查找逻辑"""
    print(f"\n" + "=" * 60)
    print("缓存查找逻辑测试")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache()
        
        # 测试不同格式的股票代码
        test_cases = [
            ("000043", "2024-10-18", "2025-03-17"),
            ("000043.XSHE", "2024-10-18", "2025-03-17"),
            ("000037", "2024-10-18", "2025-03-17"),
            ("000037.XSHE", "2024-10-18", "2025-03-17"),
        ]
        
        for stock_code, start_date, end_date in test_cases:
            print(f"\n测试股票: {stock_code}")
            
            # 检查缓存文件是否存在
            cache_file = cache._get_cache_file_path(stock_code)
            file_exists = cache_file.exists()
            print(f"  缓存文件: {cache_file}")
            print(f"  文件存在: {'✅' if file_exists else '❌'}")
            
            # 检查缓存有效性
            if file_exists:
                is_valid = cache._is_cache_valid(stock_code)
                print(f"  缓存有效: {'✅' if is_valid else '❌'}")
                
                # 尝试读取数据
                try:
                    data = cache.get_stock_data(stock_code, start_date, end_date)
                    if data is not None:
                        print(f"  数据读取: ✅ ({len(data)} 条记录)")
                    else:
                        print(f"  数据读取: ❌ (返回None)")
                except Exception as e:
                    print(f"  数据读取: ❌ (异常: {e})")
            else:
                print(f"  跳过读取测试 (文件不存在)")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存查找逻辑测试失败: {e}")
        return False

def check_cache_index():
    """检查缓存索引文件"""
    print(f"\n" + "=" * 60)
    print("缓存索引检查")
    print("=" * 60)
    
    try:
        from stock_data_cache import StockDataCache
        
        cache = StockDataCache()
        
        print(f"缓存索引文件: {cache.index_file}")
        print(f"索引文件存在: {'✅' if cache.index_file.exists() else '❌'}")
        
        if cache.cache_index:
            print(f"索引中的股票数量: {len(cache.cache_index)}")
            
            # 检查000043相关的索引
            related_stocks = [key for key in cache.cache_index.keys() if '000043' in key]
            print(f"\n000043相关的索引条目:")
            if related_stocks:
                for stock in related_stocks:
                    info = cache.cache_index[stock]
                    print(f"  ✅ {stock}: {info['record_count']} 条记录, 更新时间: {info['last_update']}")
            else:
                print(f"  ❌ 没有找到000043相关的索引条目")
            
            # 显示前几个索引条目作为参考
            print(f"\n前5个索引条目:")
            for i, (stock, info) in enumerate(list(cache.cache_index.items())[:5]):
                print(f"  {i+1}. {stock}: {info['record_count']} 条记录")
        else:
            print("❌ 缓存索引为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存索引检查失败: {e}")
        return False

def suggest_fixes():
    """建议修复方案"""
    print(f"\n" + "=" * 60)
    print("问题诊断和修复建议")
    print("=" * 60)
    
    print("基于分析结果，可能的问题和解决方案:")
    
    print(f"\n🔍 问题A: 股票代码标准化不一致")
    print("  - 现象: 查找000043.XSHE但缓存的是000043")
    print("  - 解决: 在get_stock_data中统一使用标准化代码")
    
    print(f"\n🔍 问题B: 缓存覆盖不完整")
    print("  - 现象: 000043没有被预缓存")
    print("  - 解决: 重新运行预缓存程序包含所有股票")
    
    print(f"\n🔍 问题C: 缓存文件损坏或权限问题")
    print("  - 现象: 文件存在但无法读取")
    print("  - 解决: 检查文件权限和完整性")
    
    print(f"\n🔍 问题D: 日期范围计算错误")
    print("  - 现象: 日期范围超出缓存数据范围")
    print("  - 解决: 检查涨停日期解析和范围计算")
    
    print(f"\n💡 立即诊断步骤:")
    print("1. 检查缓存目录中是否存在000043相关文件")
    print("2. 验证股票代码标准化逻辑")
    print("3. 测试不同格式代码的查找结果")
    print("4. 检查缓存索引的完整性")

def main():
    """主诊断函数"""
    print("股票缓存查找问题诊断工具")
    print("分析000043.XSHE缓存查找失败的原因")
    
    success_count = 0
    total_tests = 4
    
    # 1. 分析缓存目录结构
    if analyze_cache_structure():
        success_count += 1
    
    # 2. 测试股票代码标准化
    if test_stock_code_normalization():
        success_count += 1
    
    # 3. 测试缓存查找逻辑
    if test_cache_lookup_logic():
        success_count += 1
    
    # 4. 检查缓存索引
    if check_cache_index():
        success_count += 1
    
    # 5. 建议修复方案
    suggest_fixes()
    
    print(f"\n" + "=" * 60)
    print(f"诊断完成: {success_count}/{total_tests} 项测试成功")
    
    if success_count == total_tests:
        print("🎉 所有诊断测试通过，请查看上述分析结果")
    else:
        print("⚠️ 部分诊断测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
