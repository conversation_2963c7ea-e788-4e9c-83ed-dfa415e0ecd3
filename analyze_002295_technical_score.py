#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析002295精艺股份的技术指标评分差异
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from standalone_stock_scorer import StockScorer

def analyze_002295_technical_score():
    """分析002295精艺股份的技术指标评分差异"""
    print("=" * 80)
    print("002295 精艺股份技术指标评分差异分析")
    print("=" * 80)
    
    # 创建评分器
    scorer = StockScorer()
    
    # 模拟股票数据（使用CSV中的价格信息）
    stock_code = "002295.XSHE"
    stock_name = "精艺股份"
    current_price = 11.04  # 从独立程序结果中获取
    
    print(f"📊 分析股票: {stock_code} {stock_name}")
    print(f"💰 当前价格: {current_price}")
    
    try:
        # 创建模拟数据框
        df = pd.DataFrame({
            'secID': [stock_code],
            'closePrice': [current_price],
            '名称': [stock_name]
        })
        
        # 使用评分器进行评分
        result = scorer.score_single_stock(df, stock_code, stock_name)
        
        if result.get('skipped', False):
            print(f"❌ 股票评分被跳过: {result.get('skip_reason', '未知原因')}")
            return
        
        print(f"\n📈 评分结果:")
        print(f"  总评分: {result['total_score']}")
        print(f"  技术指标评分: {result['dimension_scores']['technical']}")
        print(f"  趋势评分: {result['dimension_scores']['trend']}")
        print(f"  成交量评分: {result['dimension_scores']['volume']}")
        print(f"  波动率评分: {result['dimension_scores']['volatility']}")
        print(f"  动量评分: {result['dimension_scores']['momentum']}")
        
        # 分析技术指标评分的详细构成
        print(f"\n🔍 技术指标评分详细分析:")
        
        # 获取详细评分逻辑
        if 'detailed_analysis' in result:
            technical_logic = result['detailed_analysis'].get('technical', {}).get('logic', [])
            print(f"  技术指标评分逻辑:")
            for logic in technical_logic:
                print(f"    {logic}")
        
        # 对比网页版结果
        print(f"\n📊 与网页版对比:")
        web_technical_score = 13
        standalone_technical_score = result['dimension_scores']['technical']
        difference = standalone_technical_score - web_technical_score
        
        print(f"  网页版技术指标评分: {web_technical_score}")
        print(f"  独立程序技术指标评分: {standalone_technical_score}")
        print(f"  差异: {difference:+d} 分")
        
        # 分析可能的差异原因
        print(f"\n🔍 差异原因分析:")
        
        if difference > 0:
            print(f"  独立程序评分更高，可能原因:")
            print(f"    1. RSI计算差异：可能RSI值落在不同的评分区间")
            print(f"    2. MACD信号差异：可能MACD金叉/死叉状态不同")
            print(f"    3. 布林带位置差异：可能价格在布林带中的位置计算不同")
            print(f"    4. 数据时间点差异：网页版和独立程序获取数据的时间可能不同")
            
            # 技术指标评分构成分析
            print(f"\n📈 技术指标评分构成（最高25分）:")
            print(f"    RSI评分: 最高10分")
            print(f"      - 40-60: 7分（中性区域）")
            print(f"      - 30-40或60-70: 10分（阈值区域）")
            print(f"      - <30: 8分（超卖区域）")
            print(f"      - >70: 2分（超买区域）")
            print(f"    MACD评分: 最高10分")
            print(f"      - 金叉且柱状图为正: 10分")
            print(f"      - 金叉: 8分")
            print(f"      - 死叉且柱状图为负: 0分")
            print(f"      - 柱状图增长: 5分")
            print(f"    布林带评分: 最高5分")
            print(f"      - 中间区域(0.3-0.7): 3分")
            print(f"      - 接近下轨(<0.2): 5分")
            print(f"      - 接近上轨(>0.8): 1分")
            
            print(f"\n💡 推测:")
            if difference == 8:
                print(f"    差异为8分，最可能的原因是:")
                print(f"    1. RSI评分差异: 独立程序可能得到10分，网页版得到2分（差异8分）")
                print(f"    2. 或者MACD评分差异: 独立程序可能得到8分，网页版得到0分（差异8分）")
                print(f"    3. 或者多个指标的组合差异")
        
        return result
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_technical_algorithms():
    """对比技术指标算法的差异"""
    print(f"\n" + "=" * 80)
    print("技术指标算法对比分析")
    print("=" * 80)
    
    print(f"\n📋 技术指标评分算法（独立程序 vs 网页版）:")
    
    print(f"\n1. RSI评分标准:")
    print(f"   独立程序:")
    print(f"     40-60: 7分, 30-40或60-70: 10分, <30: 8分, >70: 2分")
    print(f"   网页版:")
    print(f"     应该使用相同的标准（需要验证）")
    
    print(f"\n2. MACD评分标准:")
    print(f"   独立程序:")
    print(f"     金叉且柱状图为正: 10分")
    print(f"     金叉: 8分")
    print(f"     死叉且柱状图为负: 0分")
    print(f"     柱状图增长: 5分")
    print(f"   网页版:")
    print(f"     应该使用相同的标准（需要验证）")
    
    print(f"\n3. 布林带评分标准:")
    print(f"   独立程序:")
    print(f"     中间区域(0.3-0.7): 3分")
    print(f"     接近下轨(<0.2): 5分")
    print(f"     接近上轨(>0.8): 1分")
    print(f"   网页版:")
    print(f"     应该使用相同的标准（需要验证）")
    
    print(f"\n🔍 可能的差异来源:")
    print(f"   1. 数据获取时间差异：网页版16:00:56 vs 独立程序17:17:52")
    print(f"   2. 技术指标计算参数差异：RSI周期、MACD参数等")
    print(f"   3. 数据预处理差异：价格调整、数据清洗等")
    print(f"   4. 浮点数精度差异：计算过程中的舍入误差")

def main():
    """主函数"""
    result = analyze_002295_technical_score()
    compare_technical_algorithms()
    
    print(f"\n" + "=" * 80)
    print("分析总结")
    print("=" * 80)
    
    print(f"\n🎯 关键发现:")
    print(f"   1. 002295精艺股份技术指标评分差异为8分（21 vs 13）")
    print(f"   2. 这是一个显著的差异，需要深入调查")
    print(f"   3. 最可能的原因是RSI或MACD评分的差异")
    print(f"   4. 数据获取时间差异可能是根本原因")
    
    print(f"\n🔧 建议修复方案:")
    print(f"   1. 验证RSI计算的准确性和参数设置")
    print(f"   2. 验证MACD计算的准确性和参数设置")
    print(f"   3. 确保数据获取的时间一致性")
    print(f"   4. 添加详细的技术指标计算日志")

if __name__ == "__main__":
    main()
