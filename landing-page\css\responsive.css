/* Responsive Design */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .hero-content {
        gap: var(--spacing-3xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .screenshots-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .screenshots-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-md);
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 64px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 64px);
        background: var(--bg-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-2xl);
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Hero Section */
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .hero-visual {
        order: -1;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    /* Screenshots */
    .screenshots-grid {
        grid-template-columns: 1fr;
    }
    
    /* About */
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .about-features {
        align-items: center;
    }
    
    .about-feature {
        text-align: left;
        max-width: 400px;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .footer-info {
        text-align: center;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-md);
    }
    
    /* Typography adjustments */
    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }
    
    .section-title {
        font-size: clamp(1.75rem, 6vw, 2.5rem);
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .section-subtitle {
        font-size: 1.125rem;
    }
    
    /* Spacing adjustments */
    section {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    /* Hero adjustments */
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    /* Feature cards */
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 56px;
        height: 56px;
    }
    
    .feature-icon .material-icons {
        font-size: 1.75rem;
    }
    
    .feature-title {
        font-size: 1.25rem;
    }
    
    /* Hero card */
    .hero-card {
        max-width: 280px;
        padding: var(--spacing-lg);
    }
    
    /* About features */
    .about-features {
        gap: var(--spacing-md);
    }
    
    .about-feature {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .about-feature .material-icons {
        margin-top: 0;
    }
    
    /* Tech stack */
    .tech-stack {
        padding: var(--spacing-lg);
    }
    
    .tech-items {
        gap: var(--spacing-sm);
    }
    
    .tech-item {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    /* CTA */
    .cta-title {
        font-size: clamp(1.75rem, 6vw, 2.5rem);
    }
    
    .cta-subtitle {
        font-size: 1.125rem;
    }
    
    /* Footer */
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-md);
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Navigation */
    .nav-container {
        padding: 0 var(--spacing-sm);
    }
    
    .nav-brand {
        font-size: 1.125rem;
    }
    
    /* Hero */
    .hero-content {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-title {
        font-size: clamp(1.75rem, 10vw, 2.5rem);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-stats {
        gap: var(--spacing-md);
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    /* Sections */
    section {
        padding: var(--spacing-xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: clamp(1.5rem, 8vw, 2rem);
        margin-bottom: var(--spacing-md);
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* Feature cards */
    .features-grid {
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-md);
    }
    
    .feature-icon {
        width: 48px;
        height: 48px;
        margin-bottom: var(--spacing-md);
    }
    
    .feature-icon .material-icons {
        font-size: 1.5rem;
    }
    
    .feature-title {
        font-size: 1.125rem;
        margin-bottom: var(--spacing-sm);
    }
    
    .feature-description {
        font-size: 0.875rem;
        margin-bottom: var(--spacing-md);
    }
    
    .feature-list li {
        font-size: 0.875rem;
        padding: var(--spacing-xs) 0;
    }
    
    /* Screenshots */
    .screenshots-grid {
        gap: var(--spacing-lg);
    }
    
    .screenshot-info {
        padding: var(--spacing-md);
    }
    
    .screenshot-info h3 {
        font-size: 1.125rem;
    }
    
    .screenshot-info p {
        font-size: 0.875rem;
    }
    
    /* About */
    .about-description {
        font-size: 1rem;
        margin-bottom: var(--spacing-lg);
    }
    
    .about-feature h4 {
        font-size: 1rem;
    }
    
    .about-feature p {
        font-size: 0.875rem;
    }
    
    /* CTA */
    .cta-title {
        font-size: clamp(1.5rem, 8vw, 2rem);
    }
    
    .cta-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-lg);
    }
    
    .cta-note {
        font-size: 0.75rem;
        margin-top: var(--spacing-md);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-lg) 0;
    }
    
    .footer-content {
        gap: var(--spacing-md);
    }
    
    .footer-brand {
        font-size: 1rem;
    }
    
    .footer-links {
        gap: var(--spacing-sm);
    }
    
    .footer-links a {
        font-size: 0.875rem;
    }
    
    .footer-info p {
        font-size: 0.75rem;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-background::before {
        background-size: 20px 20px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .nav-toggle,
    .theme-toggle,
    .hero-actions,
    .cta,
    .footer {
        display: none;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    .hero-background,
    .hero-overlay {
        display: none;
    }
    
    .hero-text {
        color: var(--text-primary);
    }
    
    section {
        padding: var(--spacing-lg) 0;
        break-inside: avoid;
    }
    
    .feature-card,
    .screenshot-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}
