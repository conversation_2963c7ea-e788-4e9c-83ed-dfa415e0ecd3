# Python
__pycache__/
**/__pycache__/
*/__pycache__/*
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
# Virtual Environment
venv/
env/
ENV/
myenv/
myenv2/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log
logs/
log/

# Database
*.db
*.sqlite3
*.sqlite
data/

# Docker
.docker/

# Test
.coverage
htmlcov/
.pytest_cache/
.tox/

# Distribution
*.tar.gz
*.zip

# Cache
.cache/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Local development settings
local_settings.py

# Redis
dump.rdb

# Other
*.bak
*.tmp
*.temp
.env.local
.env.development.local
.env.test.local
.env.production.local # 敏感信息文件
*.key
*.pem
config/secrets.py
secrets/
# 数据库文件
# 日志文件
# 临时文件
*.pyc
*.pyo
landing-page/DEPLOYMENT_GUIDE.md
akshare_api_usage_analysis.txt
MySQL缓存架构详细文档.md
landing-page/DELIVERY_CHECKLIST.md
landing-page/images/README.md
