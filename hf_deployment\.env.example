# Hugging Face Spaces 环境变量配置
# 请在 Hugging Face Spaces 的 Settings -> Variables 中设置这些变量

# API 提供商 (OpenAI SDK)
API_PROVIDER=openai

# OpenAI API 配置 (必需)
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_MODEL=gpt-4o

# 新闻模型配置
NEWS_MODEL=gpt-4o

# 数据库配置 (Hugging Face Spaces 建议关闭)
USE_DATABASE=False

# 缓存配置 (Hugging Face Spaces 建议使用简单缓存)
USE_REDIS_CACHE=False

# 应用端口 (Hugging Face Spaces 会自动设置)
PORT=7860

# Tavily API (用于新闻搜索，可选)
TAVILY_API_KEY=your_tavily_api_key_here
