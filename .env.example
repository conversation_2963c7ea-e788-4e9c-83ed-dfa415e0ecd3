# API 配置
API_PROVIDER=openai
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_KEY=[YOUR_OPENAI_API_KEY]
OPENAI_API_MODEL=gpt-4o
NEWS_MODEL=gpt-4o

# 数据库配置
USE_DATABASE=true
DATABASE_URL=postgresql://[USERNAME]:[PASSWORD]@[HOSTNAME]:[PORT]/[DATABASE]

# 缓存配置
USE_REDIS_CACHE=false
REDIS_URL=redis://localhost:6379

# 服务器配置
PORT=8888
FLASK_ENV=production

# Tavily API (用于新闻搜索)
TAVILY_API_KEY=[YOUR_TAVILY_API_KEY]
