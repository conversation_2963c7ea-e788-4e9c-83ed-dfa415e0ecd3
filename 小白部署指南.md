# 股票分析系统小白部署指南

## 📋 准备工作

### 1. 需要准备的账号和密钥

#### 必需的账号：
- **GitHub 账号**：用于存储代码和连接部署平台
- **OpenAI 账号**：获取 API 密钥（用于 AI 分析功能）

#### 可选的账号：
- **Tavily 账号**：获取 API 密钥（用于新闻搜索，可不配置）

### 2. 获取 OpenAI API 密钥

1. 访问 [OpenAI 官网](https://platform.openai.com/)
2. 注册并登录账号
3. 点击右上角头像 → "View API keys"
4. 点击 "Create new secret key"
5. 复制生成的密钥（格式类似：sk-xxxxxxxxxxxxxxxx）
6. **重要**：妥善保存这个密钥，只会显示一次！

### 3. 上传代码到 GitHub

如果你还没有将代码上传到 GitHub：

1. 访问 [GitHub](https://github.com)，注册并登录
2. 点击右上角 "+" → "New repository"
3. 填写仓库名称，如：`stock-analysis-system`
4. 选择 "Public"（免费用户只能用公开仓库）
5. 点击 "Create repository"
6. 按照页面提示上传你的代码

## 🚂 方案一：Railway 部署（推荐）

### 为什么选择 Railway？
- 每月 $5 免费额度，足够运行
- 部署简单，适合新手
- 自动提供 HTTPS 和域名
- 免费 PostgreSQL 数据库

### 详细部署步骤

#### 第一步：注册 Railway 账号
1. 访问 [Railway 官网](https://railway.app)
2. 点击右上角 "Login"
3. 选择 "Login with GitHub"
4. 授权 Railway 访问你的 GitHub 账号

#### 第二步：创建新项目
1. 登录后，点击 "New Project"
2. 选择 "Deploy from GitHub repo"
3. 找到并选择你的股票分析系统仓库
4. 点击 "Deploy Now"

#### 第三步：添加数据库
1. 在项目页面，点击 "New" 按钮
2. 选择 "Database" → "Add PostgreSQL"
3. 等待数据库创建完成（通常 1-2 分钟）

#### 第四步：配置环境变量
1. 点击你的 Web 服务（通常显示为仓库名）
2. 切换到 "Variables" 标签页
3. 添加以下环境变量：

```
OPENAI_API_KEY = 你的OpenAI密钥
OPENAI_API_MODEL = gpt-4o
USE_DATABASE = true
```

**如何添加环境变量：**
- 点击 "New Variable"
- 在 "Name" 输入变量名（如 OPENAI_API_KEY）
- 在 "Value" 输入对应的值
- 点击 "Add" 保存

#### 第五步：等待部署完成
1. 回到 "Deployments" 标签页
2. 等待部署状态变为 "Success"（通常需要 3-5 分钟）
3. 部署成功后，会显示一个网址链接

#### 第六步：访问你的应用
1. 点击生成的网址链接
2. 如果看到股票分析系统首页，说明部署成功！
3. 可以尝试分析一只股票测试功能

### Railway 故障排除

**如果部署失败：**
1. 检查 "Deployments" 页面的错误日志
2. 确认环境变量设置正确
3. 检查 OpenAI API 密钥是否有效

**如果访问超时：**
1. Railway 免费版有冷启动时间，第一次访问可能较慢
2. 等待 30-60 秒再试

## 🎨 方案二：Render 部署（完全免费）

### 为什么选择 Render？
- 完全免费（750 小时/月）
- 自动 SSL 证书
- 简单易用
- 免费 PostgreSQL 数据库

### 详细部署步骤

#### 第一步：注册 Render 账号
1. 访问 [Render 官网](https://render.com)
2. 点击右上角 "Get Started"
3. 选择 "GitHub" 登录
4. 授权 Render 访问你的 GitHub 账号

#### 第二步：创建数据库
1. 在 Render 控制台，点击 "New +"
2. 选择 "PostgreSQL"
3. 填写数据库信息：
   - **Name**: `stock-analysis-db`
   - **Database**: `stock_analysis`
   - **User**: `stock_user`
   - **Region**: 选择离你最近的区域
4. 点击 "Create Database"
5. **重要**：复制生成的 "External Database URL"，后面要用

#### 第三步：创建 Web 服务
1. 点击 "New +" → "Web Service"
2. 选择 "Build and deploy from a Git repository"
3. 点击 "Connect" 连接你的 GitHub 仓库
4. 选择你的股票分析系统仓库
5. 填写服务信息：
   - **Name**: `stock-analysis-system`
   - **Region**: 选择与数据库相同的区域
   - **Branch**: `main` 或 `master`
   - **Runtime**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 300 web_server:app`

#### 第四步：配置环境变量
在 "Environment Variables" 部分添加：

```
OPENAI_API_KEY = 你的OpenAI密钥
OPENAI_API_MODEL = gpt-4o
USE_DATABASE = true
DATABASE_URL = 你在第二步复制的数据库URL
```

#### 第五步：部署
1. 点击 "Create Web Service"
2. 等待部署完成（通常需要 5-10 分钟）
3. 部署成功后会显示一个 `.onrender.com` 的网址

#### 第六步：访问应用
1. 点击生成的网址
2. 第一次访问可能需要等待 30 秒（冷启动）
3. 看到首页说明部署成功！

### Render 注意事项

**免费版限制：**
- 应用在无访问时会自动休眠
- 冷启动需要 30 秒左右
- 每月 750 小时免费时长

**如何保持应用活跃：**
- 可以使用 UptimeRobot 等服务定期访问你的应用
- 或者升级到付费版（$7/月）

## 🔧 部署后的配置和测试

### 1. 功能测试清单

部署完成后，请测试以下功能：

- [ ] 访问首页，检查界面是否正常显示
- [ ] 点击"智能仪表盘"，输入股票代码（如：000001）
- [ ] 检查是否能正常分析股票
- [ ] 测试 AI 问答功能
- [ ] 查看 API 文档：访问 `/api/docs`

### 2. 常见问题解决

#### 问题1：页面显示 "Application Error"
**解决方案：**
1. 检查环境变量是否设置正确
2. 确认 OpenAI API 密钥有效
3. 查看部署日志寻找具体错误

#### 问题2：股票分析功能不工作
**解决方案：**
1. 检查 OpenAI API 密钥是否正确
2. 确认 API 密钥有足够余额
3. 检查网络连接是否正常

#### 问题3：数据库连接失败
**解决方案：**
1. 检查 DATABASE_URL 是否正确
2. 确认数据库服务正常运行
3. 检查 USE_DATABASE 是否设置为 true

### 3. 性能优化建议

#### 提高访问速度：
1. 选择离你最近的服务器区域
2. 使用 CDN 加速（付费功能）
3. 优化图片和静态资源

#### 降低成本：
1. Railway：监控使用量，避免超出免费额度
2. Render：合理使用，避免超出 750 小时限制

## 📞 获取帮助

如果遇到问题，可以：

1. **查看部署日志**：在平台控制台查看详细错误信息
2. **检查环境变量**：确认所有必需的环境变量都已设置
3. **测试 API 密钥**：在 OpenAI 官网确认密钥有效
4. **联系支持**：
   - Railway: [帮助文档](https://docs.railway.app)
   - Render: [帮助文档](https://render.com/docs)

## 🎉 恭喜！

如果你成功完成了上述步骤，你的股票分析系统现在已经在云端运行了！

你可以：
- 分享网址给朋友使用
- 继续完善系统功能
- 学习更多云部署知识

**记住**：这是一个学习项目，AI 生成的投资建议仅供参考，不要作为实际投资决策的依据！

## 📸 部署过程截图指南

### Railway 部署关键截图

#### 1. 创建项目页面
当你看到这个页面时，选择 "Deploy from GitHub repo"：
```
┌─────────────────────────────────────┐
│  New Project                        │
│  ┌─────────────────────────────────┐ │
│  │  Deploy from GitHub repo        │ │
│  └─────────────────────────────────┘ │
│  ┌─────────────────────────────────┐ │
│  │  Deploy from template           │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 2. 环境变量设置页面
在 Variables 标签页，应该看到类似这样的界面：
```
┌─────────────────────────────────────┐
│  Environment Variables              │
│  ┌─────────────────────────────────┐ │
│  │  + New Variable                 │ │
│  └─────────────────────────────────┘ │
│  Name: OPENAI_API_KEY               │
│  Value: sk-xxxxxxxxxxxxxxxx         │
│  ┌─────────────────────────────────┐ │
│  │  Add                            │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### Render 部署关键截图

#### 1. 创建 Web Service 页面
填写信息时应该是这样：
```
┌─────────────────────────────────────┐
│  Create a new Web Service           │
│  Name: stock-analysis-system        │
│  Region: Oregon (US West)           │
│  Branch: main                       │
│  Runtime: Python 3                  │
│  Build Command: pip install -r...   │
│  Start Command: gunicorn --bind...  │
└─────────────────────────────────────┘
```

## 🚨 常见错误和解决方案

### 错误1：部署失败 - "Build failed"

**错误信息示例：**
```
ERROR: Could not find a version that satisfies the requirement...
```

**解决方案：**
1. 检查 `requirements.txt` 文件是否存在
2. 确认 Python 版本兼容性
3. 尝试删除有问题的依赖包版本号

### 错误2：应用启动失败 - "Application failed to start"

**错误信息示例：**
```
ModuleNotFoundError: No module named 'web_server'
```

**解决方案：**
1. 检查启动命令是否正确：`web_server:app`
2. 确认主文件名是 `web_server.py`
3. 检查文件是否正确上传到 GitHub

### 错误3：数据库连接失败

**错误信息示例：**
```
sqlalchemy.exc.OperationalError: could not connect to server
```

**解决方案：**
1. 检查 `DATABASE_URL` 环境变量
2. 确认数据库服务正在运行
3. 检查数据库用户名和密码

## 💡 小贴士和最佳实践

### 1. 环境变量安全
- ❌ 不要将 API 密钥直接写在代码里
- ✅ 始终使用环境变量存储敏感信息
- ✅ 定期更换 API 密钥

### 2. 成本控制
- **Railway**: 监控使用量，设置预算提醒
- **Render**: 合理安排应用使用时间
- **通用**: 不需要时可以暂停服务

### 3. 备份和版本控制
- ✅ 定期备份重要数据
- ✅ 使用 Git 管理代码版本
- ✅ 记录重要的配置更改

### 4. 监控和维护
- 定期检查应用运行状态
- 监控 API 使用量和费用
- 及时更新依赖包版本

## 📱 移动端访问优化

部署成功后，你的应用也可以在手机上访问：

1. **响应式设计**：系统已适配移动设备
2. **添加到主屏幕**：在手机浏览器中可以"添加到主屏幕"
3. **离线功能**：部分功能支持离线使用

## 🔄 更新和维护

### 如何更新应用：

1. **修改代码**：在本地修改代码
2. **提交到 GitHub**：
   ```bash
   git add .
   git commit -m "更新功能"
   git push
   ```
3. **自动部署**：Railway 和 Render 会自动检测更改并重新部署

### 定期维护任务：

- [ ] 每月检查 API 使用量
- [ ] 每季度更新依赖包
- [ ] 定期备份重要数据
- [ ] 监控应用性能

## 🎓 进阶学习

部署成功后，你可以继续学习：

1. **Docker 容器化**：学习使用 Docker 部署
2. **CI/CD 流水线**：自动化测试和部署
3. **监控和日志**：应用性能监控
4. **安全加固**：提高应用安全性
5. **扩展功能**：添加更多股票分析功能

恭喜你完成了第一个云应用的部署！这是一个很好的开始，继续加油！ 🚀
