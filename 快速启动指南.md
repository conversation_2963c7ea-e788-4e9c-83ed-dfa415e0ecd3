# 批量股票分析 - 快速启动指南

## 🚀 一键运行

### Windows用户
双击运行 `run_analysis.bat` 文件

### 命令行用户
```bash
python final_batch_analyzer.py
```

## 📋 程序功能

✅ 自动读取 `list3.csv` 中的股票代码  
✅ 转换股票代码格式 (XSHG→SH, XSHE→SZ)  
✅ 批量调用股票分析API  
✅ 提取关键分析指标  
✅ 保存结果到CSV文件  

## 📊 输入文件格式

确保 `list3.csv` 文件包含 `secID` 列，格式如下：

```csv
secID,名称,closePrice,...
603316.XSHG,诚邦股份,11.97,...
601218.XSHG,吉鑫科技,5.47,...
```

## 📈 输出文件

程序完成后会生成：

- `batch_analysis_results_YYYYMMDD_HHMMSS.csv` - 主要分析结果
- `batch_analysis_failed_YYYYMMDD_HHMMSS.csv` - 失败记录 (如果有)

## 📋 输出字段说明

| 字段 | 说明 |
|------|------|
| original_code | 原始股票代码 |
| converted_code | 转换后代码 |
| stock_name | 股票名称 |
| current_price | 当前价格 |
| overall_score | 综合评分 |
| technical_score | 技术分析评分 |
| fundamental_score | 基本面评分 |
| risk_score | 风险评分 |
| risk_level | 风险等级 |

## ⚙️ 配置说明

程序使用以下默认配置：

- **API地址**: `https://fromozu-stock-analysis.hf.space:8888/api/v1/stock/analyze`
- **API密钥**: `UZXJfw3YNX80DLfN`
- **输入文件**: `list3.csv`
- **请求间隔**: 1秒 (避免API限流)
- **超时时间**: 30秒

## 🔧 故障排除

### 问题1: Python未找到
**解决方案**: 确保已安装Python并添加到系统PATH

### 问题2: 依赖包缺失
**解决方案**: 运行 `pip install pandas requests`

### 问题3: CSV文件读取失败
**解决方案**: 检查 `list3.csv` 文件是否存在且格式正确

### 问题4: API调用失败
**解决方案**: 检查网络连接和API服务状态

## 📞 技术支持

如遇问题，请查看：
1. 程序运行时的控制台输出
2. 生成的失败记录文件
3. 详细的使用说明文档

## 🎯 预期结果

- 成功率通常在80%以上
- 每只股票分析时间约1-2秒
- 总处理时间取决于股票数量

---

**提示**: 首次运行建议先用少量股票测试，确认一切正常后再进行大批量分析。
