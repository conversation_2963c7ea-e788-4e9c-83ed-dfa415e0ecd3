#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据预缓存程序
批量下载和缓存股票历史数据，为后续分析提供高速数据访问
"""

import os
import sys
import pandas as pd
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import time
from stock_data_cache import StockDataCache

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('stock_precacher.log', encoding='utf-8')
        ]
    )

def load_stock_list_from_csv(csv_file: str) -> list:
    """从CSV文件加载股票列表"""
    try:
        df = pd.read_csv(csv_file)
        
        # 尝试不同的列名
        stock_code_column = None
        for col in ['secID', 'stock_code', 'code', '股票代码']:
            if col in df.columns:
                stock_code_column = col
                break
        
        if stock_code_column is None:
            raise ValueError(f"未找到股票代码列，可用列: {list(df.columns)}")
        
        stock_codes = df[stock_code_column].dropna().astype(str).tolist()
        logging.info(f"从 {csv_file} 加载了 {len(stock_codes)} 只股票")
        return stock_codes
        
    except Exception as e:
        logging.error(f"加载股票列表失败: {e}")
        return []

def get_all_a_stock_codes():
    """获取所有A股股票代码"""
    try:
        import akshare as ak
        logging.info("正在获取所有A股股票列表...")
        
        # 获取沪深A股列表
        stock_info = ak.stock_info_a_code_name()
        stock_codes = stock_info['code'].tolist()
        
        logging.info(f"获取到 {len(stock_codes)} 只A股股票")
        return stock_codes
        
    except Exception as e:
        logging.error(f"获取A股列表失败: {e}")
        return []

def display_progress(current: int, total: int, start_time: float):
    """显示进度信息"""
    elapsed = time.time() - start_time
    if current > 0:
        eta = (elapsed / current) * (total - current)
        eta_str = f"{int(eta//3600):02d}:{int((eta%3600)//60):02d}:{int(eta%60):02d}"
    else:
        eta_str = "计算中..."
    
    progress = (current / total) * 100
    print(f"\r进度: {current}/{total} ({progress:.1f}%) | 已用时: {int(elapsed//60):02d}:{int(elapsed%60):02d} | 预计剩余: {eta_str}", end="")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="股票数据预缓存程序")
    parser.add_argument("--input", "-i", type=str, help="输入CSV文件路径（包含股票代码）")
    parser.add_argument("--all", "-a", action="store_true", help="下载所有A股数据")
    parser.add_argument("--cache-dir", "-c", type=str, default="stock_cache", help="缓存目录路径")
    parser.add_argument("--workers", "-w", type=int, default=5, help="并发下载线程数")
    parser.add_argument("--days", "-d", type=int, default=365, help="下载天数（默认365天）")
    parser.add_argument("--force", "-f", action="store_true", help="强制刷新所有缓存")
    parser.add_argument("--status", "-s", action="store_true", help="显示缓存状态")
    parser.add_argument("--clean", action="store_true", help="清理过期缓存")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    # 初始化缓存管理器
    cache = StockDataCache(cache_dir=args.cache_dir, max_workers=args.workers)
    
    # 显示缓存状态
    if args.status:
        status = cache.get_cache_status()
        print("=" * 60)
        print("缓存状态信息")
        print("=" * 60)
        print(f"缓存目录: {status['cache_dir']}")
        print(f"总股票数: {status['total_stocks']}")
        print(f"有效缓存: {status['valid_stocks']}")
        print(f"过期缓存: {status['expired_stocks']}")
        print(f"缓存大小: {status['cache_size_mb']} MB")
        print(f"最后更新: {status['last_update']}")
        print("=" * 60)
        return
    
    # 清理过期缓存
    if args.clean:
        print("正在清理过期缓存...")
        cleaned = cache.clean_expired_cache()
        print(f"已清理 {cleaned} 个过期缓存文件")
        return
    
    # 确定股票列表
    stock_codes = []
    
    if args.input:
        if not os.path.exists(args.input):
            logging.error(f"输入文件不存在: {args.input}")
            return
        stock_codes = load_stock_list_from_csv(args.input)
    elif args.all:
        stock_codes = get_all_a_stock_codes()
    else:
        # 尝试自动检测当前目录下的CSV文件
        csv_files = []
        for file_name in ["zhangting_20250601_20250630.csv", "list3.csv"]:
            if os.path.exists(file_name):
                csv_files.append(file_name)
        
        if csv_files:
            print("检测到以下CSV文件:")
            for i, file_name in enumerate(csv_files, 1):
                print(f"  {i}. {file_name}")
            
            try:
                choice = input(f"请选择文件 (1-{len(csv_files)}) 或按回车使用第一个: ").strip()
                if choice == "":
                    choice = "1"
                
                selected_file = csv_files[int(choice) - 1]
                stock_codes = load_stock_list_from_csv(selected_file)
            except (ValueError, IndexError):
                logging.error("无效选择")
                return
        else:
            logging.error("未指定输入文件，请使用 --input 参数或 --all 参数")
            return
    
    if not stock_codes:
        logging.error("未获取到股票代码列表")
        return
    
    # 计算日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y%m%d')
    
    print("=" * 60)
    print("股票数据预缓存程序")
    print("=" * 60)
    print(f"股票数量: {len(stock_codes)}")
    print(f"数据范围: {start_date} 到 {end_date} ({args.days}天)")
    print(f"缓存目录: {args.cache_dir}")
    print(f"并发线程: {args.workers}")
    print(f"强制刷新: {'是' if args.force else '否'}")
    print("=" * 60)
    
    # 确认开始
    if len(stock_codes) > 100:
        confirm = input(f"即将下载 {len(stock_codes)} 只股票数据，是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
    
    # 开始批量下载
    print("开始批量下载...")
    start_time = time.time()
    
    try:
        results = cache.batch_download(
            stock_codes=stock_codes,
            start_date=start_date,
            end_date=end_date,
            force_refresh=args.force
        )
        
        # 统计结果
        success_count = sum(results.values())
        failed_codes = [code for code, success in results.items() if not success]
        
        elapsed_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print("下载完成")
        print("=" * 60)
        print(f"总股票数: {len(stock_codes)}")
        print(f"成功下载: {success_count}")
        print(f"下载失败: {len(failed_codes)}")
        print(f"成功率: {success_count/len(stock_codes)*100:.1f}%")
        print(f"总用时: {int(elapsed_time//60):02d}:{int(elapsed_time%60):02d}")
        
        if failed_codes:
            print(f"\n失败的股票代码:")
            for i, code in enumerate(failed_codes[:10]):  # 只显示前10个
                print(f"  {code}")
            if len(failed_codes) > 10:
                print(f"  ... 还有 {len(failed_codes) - 10} 个")
        
        # 显示最终缓存状态
        print("\n缓存状态:")
        status = cache.get_cache_status()
        print(f"  总缓存股票: {status['total_stocks']}")
        print(f"  有效缓存: {status['valid_stocks']}")
        print(f"  缓存大小: {status['cache_size_mb']} MB")
        
    except KeyboardInterrupt:
        print("\n\n用户中断下载")
    except Exception as e:
        logging.error(f"批量下载过程中发生错误: {e}")

if __name__ == "__main__":
    main()
