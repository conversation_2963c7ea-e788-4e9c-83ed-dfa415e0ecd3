# Cloudflare Pages Redirects Configuration
# https://developers.cloudflare.com/pages/platform/redirects/

# Redirect root to index.html (for SPA behavior)
/index /index.html 200

# Handle missing trailing slashes
/features /index.html#features 301
/screenshots /index.html#screenshots 301
/about /index.html#about 301

# Redirect old paths (if any)
/home /index.html 301
/demo /index.html 301

# Security headers for static assets
/css/* 
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

/js/*
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

/images/*
  X-Content-Type-Options: nosniff
  Cache-Control: public, max-age=31536000, immutable

# Favicon redirects
/favicon.ico /images/favicon.ico 301
/apple-touch-icon.png /images/favicon.svg 301

# Handle 404 errors
/* /index.html 404
