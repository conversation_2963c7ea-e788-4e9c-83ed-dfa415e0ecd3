#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票评分差异对比分析工具
对比独立程序与网页版的评分差异，找出算法实现中的根本问题
"""

import pandas as pd
import numpy as np
from datetime import datetime

def load_and_clean_data():
    """加载并清理两个CSV文件的数据"""
    print("=" * 80)
    print("股票评分差异对比分析")
    print("=" * 80)
    
    # 读取独立程序结果
    try:
        standalone_df = pd.read_csv('results_20250710_155835.csv', encoding='utf-8-sig')
        print(f"✅ 成功读取独立程序结果: {len(standalone_df)} 条记录")
    except Exception as e:
        print(f"❌ 读取独立程序结果失败: {e}")
        return None, None
    
    # 读取网页版结果
    try:
        web_df = pd.read_csv('20250709_20250710_160056.csv', encoding='utf-8-sig')
        print(f"✅ 成功读取网页版结果: {len(web_df)} 条记录")
    except Exception as e:
        print(f"❌ 读取网页版结果失败: {e}")
        return None, None
    
    # 数据清理和标准化
    # 独立程序数据清理
    standalone_clean = standalone_df[standalone_df['状态'] == '成功评分'].copy()
    standalone_clean['股票代码_clean'] = standalone_clean['股票代码'].str.replace('.XSHE', '').str.replace('.XSHG', '')
    
    # 网页版数据清理
    web_clean = web_df.copy()
    web_clean['股票代码_clean'] = web_clean['股票代码'].astype(str)
    web_clean = web_clean.rename(columns={'综合评分': '总评分'})
    
    print(f"📊 独立程序有效评分: {len(standalone_clean)} 只股票")
    print(f"📊 网页版有效评分: {len(web_clean)} 只股票")
    
    return standalone_clean, web_clean

def analyze_score_differences(standalone_df, web_df):
    """分析评分差异"""
    print("\n" + "=" * 60)
    print("📈 评分差异分析")
    print("=" * 60)
    
    # 检查列名
    print(f"独立程序列名: {list(standalone_df.columns)}")
    print(f"网页版列名: {list(web_df.columns)}")

    # 合并数据进行对比
    merged_df = pd.merge(
        standalone_df[['股票代码_clean', '股票名称', '总评分', '趋势评分', '技术指标评分', '成交量评分', '波动率评分', '动量评分']],
        web_df[['股票代码_clean', '股票名称', '总评分', '趋势分析评分', '技术指标评分', '成交量分析评分', '波动率评估评分', '动量指标评分']],
        on='股票代码_clean',
        suffixes=('_独立', '_网页'),
        how='inner'
    )
    
    print(f"🔗 成功匹配 {len(merged_df)} 只股票进行对比")
    
    # 计算各维度差异
    merged_df['总评分差异'] = merged_df['总评分_独立'] - merged_df['总评分_网页']
    merged_df['趋势评分差异'] = merged_df['趋势评分_独立'] - merged_df['趋势分析评分']
    merged_df['技术指标差异'] = merged_df['技术指标评分_独立'] - merged_df['技术指标评分_网页']
    merged_df['成交量差异'] = merged_df['成交量评分_独立'] - merged_df['成交量分析评分']
    
    # 处理网页版中的'-'值
    merged_df['波动率评估评分'] = merged_df['波动率评估评分'].replace('-', 0)
    merged_df['波动率评估评分'] = pd.to_numeric(merged_df['波动率评估评分'], errors='coerce').fillna(0)
    merged_df['波动率差异'] = merged_df['波动率评分_独立'] - merged_df['波动率评估评分']

    merged_df['动量差异'] = merged_df['动量评分_独立'] - merged_df['动量指标评分']
    
    # 统计分析
    print(f"\n📊 总评分统计:")
    print(f"  独立程序平均分: {merged_df['总评分_独立'].mean():.1f}")
    print(f"  网页版平均分: {merged_df['总评分_网页'].mean():.1f}")
    print(f"  平均差异: {merged_df['总评分差异'].mean():.1f}")
    print(f"  差异标准差: {merged_df['总评分差异'].std():.1f}")
    
    # 找出差异较大的股票
    large_diff = merged_df[abs(merged_df['总评分差异']) > 10].copy()
    large_diff = large_diff.sort_values('总评分差异', key=abs, ascending=False)
    
    print(f"\n⚠️ 评分差异>10分的股票 ({len(large_diff)} 只):")
    for _, row in large_diff.head(10).iterrows():
        print(f"  {row['股票代码_clean']} {row['股票名称_独立']:8s} | 独立:{row['总评分_独立']:3.0f} 网页:{row['总评分_网页']:3.0f} 差异:{row['总评分差异']:+3.0f}")
    
    return merged_df

def analyze_dimension_differences(merged_df):
    """分析各维度评分差异"""
    print("\n" + "=" * 60)
    print("🔍 各维度评分差异分析")
    print("=" * 60)
    
    dimensions = [
        ('趋势评分差异', '趋势评分'),
        ('技术指标差异', '技术指标评分'),
        ('成交量差异', '成交量评分'),
        ('波动率差异', '波动率评分'),
        ('动量差异', '动量评分')
    ]
    
    for diff_col, score_col in dimensions:
        print(f"\n📈 {diff_col}:")
        print(f"  平均差异: {merged_df[diff_col].mean():.1f}")
        print(f"  差异范围: {merged_df[diff_col].min():.0f} ~ {merged_df[diff_col].max():.0f}")
        print(f"  零差异股票: {(merged_df[diff_col] == 0).sum()} 只")
        print(f"  有差异股票: {(merged_df[diff_col] != 0).sum()} 只")
        
        # 显示差异最大的股票
        if (merged_df[diff_col] != 0).any():
            max_diff_idx = merged_df[diff_col].abs().idxmax()
            max_diff_row = merged_df.loc[max_diff_idx]
            print(f"  最大差异: {max_diff_row['股票代码_clean']} {max_diff_row['股票名称_独立']} 差异:{max_diff_row[diff_col]:+.0f}")

def identify_problematic_stocks(merged_df):
    """识别问题股票并分析原因"""
    print("\n" + "=" * 60)
    print("🔍 问题股票详细分析")
    print("=" * 60)
    
    # 按差异程度分类
    severe_diff = merged_df[abs(merged_df['总评分差异']) > 20]
    moderate_diff = merged_df[(abs(merged_df['总评分差异']) > 10) & (abs(merged_df['总评分差异']) <= 20)]
    
    print(f"🚨 严重差异(>20分): {len(severe_diff)} 只股票")
    print(f"⚠️ 中等差异(10-20分): {len(moderate_diff)} 只股票")
    
    # 分析严重差异股票
    if len(severe_diff) > 0:
        print(f"\n🚨 严重差异股票详细分析:")
        for _, row in severe_diff.iterrows():
            print(f"\n  📊 {row['股票代码_clean']} {row['股票名称_独立']}")
            print(f"    总评分: 独立{row['总评分_独立']:.0f} vs 网页{row['总评分_网页']:.0f} (差异:{row['总评分差异']:+.0f})")
            print(f"    趋势: 独立{row['趋势评分']:.0f} vs 网页{row['趋势分析评分']:.0f} (差异:{row['趋势评分差异']:+.0f})")
            print(f"    技术: 独立{row['技术指标评分']:.0f} vs 网页{row['技术指标评分']:.0f} (差异:{row['技术指标差异']:+.0f})")
            print(f"    成交量: 独立{row['成交量评分']:.0f} vs 网页{row['成交量分析评分']:.0f} (差异:{row['成交量差异']:+.0f})")
            print(f"    波动率: 独立{row['波动率评分']:.0f} vs 网页{row['波动率评估评分']:.0f} (差异:{row['波动率差异']:+.0f})")
            print(f"    动量: 独立{row['动量评分']:.0f} vs 网页{row['动量指标评分']:.0f} (差异:{row['动量差异']:+.0f})")

def main():
    """主函数"""
    # 加载数据
    standalone_df, web_df = load_and_clean_data()
    if standalone_df is None or web_df is None:
        return
    
    # 分析评分差异
    merged_df = analyze_score_differences(standalone_df, web_df)
    
    # 分析各维度差异
    analyze_dimension_differences(merged_df)
    
    # 识别问题股票
    identify_problematic_stocks(merged_df)
    
    # 保存详细对比结果
    output_file = f"score_comparison_detail_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 详细对比结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
