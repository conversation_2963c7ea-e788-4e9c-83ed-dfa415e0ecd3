{"swagger": "2.0", "info": {"title": "股票智能分析系统 API", "description": "股票智能分析系统的REST API文档", "version": "2.1.0"}, "host": "localhost:8888", "basePath": "/", "schemes": ["http", "https"], "paths": {"/analyze": {"post": {"summary": "分析股票", "description": "分析单只或多只股票", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_codes": {"type": "array", "items": {"type": "string"}, "example": ["600519", "000858"]}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功分析股票"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/start_stock_analysis": {"post": {"summary": "启动股票分析任务", "description": "启动异步股票分析任务", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功启动分析任务"}}}}, "/api/analysis_status/{task_id}": {"get": {"summary": "获取分析任务状态", "description": "获取异步分析任务的状态和结果", "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "成功获取任务状态和结果"}, "404": {"description": "找不到指定的任务"}}}}, "/api/stock_data": {"get": {"summary": "获取股票数据", "description": "获取股票历史数据和技术指标", "parameters": [{"name": "stock_code", "in": "query", "required": true, "type": "string"}, {"name": "market_type", "in": "query", "required": false, "type": "string", "default": "A"}, {"name": "period", "in": "query", "required": false, "type": "string", "enum": ["1m", "3m", "6m", "1y"], "default": "1y"}], "responses": {"200": {"description": "成功获取股票数据"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/start_market_scan": {"post": {"summary": "启动市场扫描任务", "description": "启动异步市场扫描任务", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_list": {"type": "array", "items": {"type": "string"}, "example": ["600519", "000858"]}, "min_score": {"type": "integer", "example": 60}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功启动扫描任务"}}}}, "/api/scan_status/{task_id}": {"get": {"summary": "获取扫描任务状态", "description": "获取异步扫描任务的状态和结果", "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "成功获取任务状态和结果"}, "404": {"description": "找不到指定的任务"}}}}, "/api/index_stocks": {"get": {"summary": "获取指数成分股", "description": "获取指定指数的成分股列表", "parameters": [{"name": "index_code", "in": "query", "required": true, "type": "string", "example": "000300"}], "responses": {"200": {"description": "成功获取指数成分股"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/industry_stocks": {"get": {"summary": "获取行业成分股", "description": "获取指定行业的成分股列表", "parameters": [{"name": "industry", "in": "query", "required": true, "type": "string", "example": "银行"}], "responses": {"200": {"description": "成功获取行业成分股"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/fundamental_analysis": {"post": {"summary": "基本面分析", "description": "获取股票的基本面分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}}}}], "responses": {"200": {"description": "成功获取基本面分析结果"}}}}, "/api/capital_flow": {"post": {"summary": "资金流向分析", "description": "获取股票的资金流向分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "days": {"type": "integer", "example": 10}}}}], "responses": {"200": {"description": "成功获取资金流向分析结果"}}}}, "/api/scenario_predict": {"post": {"summary": "情景预测", "description": "获取股票的多情景预测结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}, "days": {"type": "integer", "example": 60}}}}], "responses": {"200": {"description": "成功获取情景预测结果"}}}}, "/api/qa": {"post": {"summary": "智能问答", "description": "获取股票相关问题的智能回答", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "question": {"type": "string", "example": "这只股票的主要支撑位是多少？"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功获取智能回答"}}}}, "/api/risk_analysis": {"post": {"summary": "风险分析", "description": "获取股票的风险分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功获取风险分析结果"}}}}, "/api/portfolio_risk": {"post": {"summary": "投资组合风险分析", "description": "获取投资组合的整体风险分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"portfolio": {"type": "array", "items": {"type": "object", "properties": {"stock_code": {"type": "string"}, "weight": {"type": "number"}, "market_type": {"type": "string"}}}, "example": [{"stock_code": "600519", "weight": 30, "market_type": "A"}, {"stock_code": "000858", "weight": 20, "market_type": "A"}]}}}}], "responses": {"200": {"description": "成功获取投资组合风险分析结果"}}}}, "/api/index_analysis": {"get": {"summary": "指数分析", "description": "获取指数的整体分析结果", "parameters": [{"name": "index_code", "in": "query", "required": true, "type": "string", "example": "000300"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "example": 30}], "responses": {"200": {"description": "成功获取指数分析结果"}}}}, "/api/industry_analysis": {"get": {"summary": "行业分析", "description": "获取行业的整体分析结果", "parameters": [{"name": "industry", "in": "query", "required": true, "type": "string", "example": "银行"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "example": 30}], "responses": {"200": {"description": "成功获取行业分析结果"}}}}, "/api/industry_compare": {"get": {"summary": "行业比较", "description": "比较不同行业的表现", "parameters": [{"name": "limit", "in": "query", "required": false, "type": "integer", "example": 10}], "responses": {"200": {"description": "成功获取行业比较结果"}}}}}, "definitions": {"Stock": {"type": "object", "properties": {"stock_code": {"type": "string"}, "stock_name": {"type": "string"}, "price": {"type": "number"}, "price_change": {"type": "number"}}}, "AnalysisResult": {"type": "object", "properties": {"score": {"type": "number"}, "recommendation": {"type": "string"}}}}}