#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的缓存系统测试
"""

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import pandas as pd
        print("✅ pandas 导入成功")
    except Exception as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import akshare as ak
        print("✅ akshare 导入成功")
    except Exception as e:
        print(f"❌ akshare 导入失败: {e}")
        return False
    
    try:
        from stock_data_cache import StockDataCache
        print("✅ StockDataCache 导入成功")
    except Exception as e:
        print(f"❌ StockDataCache 导入失败: {e}")
        return False
    
    return True

def test_cache_creation():
    """测试缓存创建"""
    print("\n测试缓存创建...")
    
    try:
        from stock_data_cache import StockDataCache
        cache = StockDataCache(cache_dir="test_cache_simple")
        print("✅ 缓存实例创建成功")
        
        status = cache.get_cache_status()
        print(f"✅ 缓存状态: {status['total_stocks']} 只股票")
        return True
        
    except Exception as e:
        print(f"❌ 缓存创建失败: {e}")
        return False

def test_scorer_with_cache():
    """测试评分器缓存集成"""
    print("\n测试评分器缓存集成...")
    
    try:
        from standalone_stock_scorer import RealDataService
        
        # 测试启用缓存
        service_with_cache = RealDataService(use_cache=True)
        print("✅ 启用缓存的数据服务创建成功")
        
        # 测试禁用缓存
        service_no_cache = RealDataService(use_cache=False)
        print("✅ 禁用缓存的数据服务创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 评分器缓存集成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("简单缓存系统测试")
    print("=" * 50)
    
    all_passed = True
    
    if not test_basic_imports():
        all_passed = False
    
    if not test_cache_creation():
        all_passed = False
    
    if not test_scorer_with_cache():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 缓存系统测试通过！")
        print("\n可以开始使用缓存系统:")
        print("1. 预缓存数据: python stock_data_precacher.py -i zhangting_20250601_20250630.csv")
        print("2. 运行评分: python standalone_stock_scorer.py")
    else:
        print("❌ 缓存系统测试失败")
    print("=" * 50)

if __name__ == "__main__":
    main()
