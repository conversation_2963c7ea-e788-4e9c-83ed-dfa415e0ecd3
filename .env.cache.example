# 股票分析系统数据缓存配置模板
# 复制此文件为 .env 并根据您的环境修改配置

# ==================== 数据库配置 ====================

# 是否启用数据库缓存 (true/false)
USE_DATABASE=true

# 数据库连接URL - 选择适合您的数据库
# SQLite (本地开发)
# DATABASE_URL=sqlite:///data/stock_analyzer.db

# Aiven MySQL (免费 - 推荐)
DATABASE_URL=mysql://[USER]:[PASS]@[HOST]:[PORT]/[DB]?ssl-mode=REQUIRED

# PlanetScale MySQL (免费)
# DATABASE_URL=mysql://[USER]:[PASS]@[HOST]:[PORT]/[DB]?ssl-mode=REQUIRED

# Railway MySQL
# DATABASE_URL=mysql://[USER]:[PASS]@[HOST]:[PORT]/[DB]

# 数据库连接池配置
DATABASE_POOL_SIZE=10
DATABASE_POOL_RECYCLE=3600
DATABASE_POOL_TIMEOUT=30

# ==================== 缓存配置 ====================

# 默认缓存时间 (秒)
CACHE_DEFAULT_TTL=900

# 不同数据类型的缓存时间
REALTIME_DATA_TTL=300          # 5分钟 - 实时数据
BASIC_INFO_TTL=604800          # 7天 - 股票基本信息
FINANCIAL_DATA_TTL=7776000     # 90天 - 财务数据
CAPITAL_FLOW_TTL=86400         # 1天 - 资金流向数据
PRICE_HISTORY_TTL=3600         # 1小时 - 历史价格数据

# 内存缓存配置
MEMORY_CACHE_SIZE=1000         # 最大缓存条目数
CACHE_CLEANUP_INTERVAL=3600    # 缓存清理间隔 (秒)

# ==================== API配置 ====================

# AKShare API配置
AKSHARE_TIMEOUT=30             # API调用超时时间 (秒)
AKSHARE_MAX_RETRIES=3          # 最大重试次数
AKSHARE_RETRY_DELAY=1          # 重试延迟 (秒)

# OpenAI API配置
OPENAI_API_KEY=[YOUR_OPENAI_API_KEY]
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_API_MODEL=gpt-4o
FUNCTION_CALL_MODEL=gpt-4o
NEWS_MODEL=gpt-4o

# ==================== Redis配置 (可选) ====================

# 是否启用Redis缓存 (true/false)
USE_REDIS_CACHE=false

# Redis连接配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_DEFAULT_TTL=300

# ==================== 应用配置 ====================

# Flask应用配置
SECRET_KEY=[YOUR_SECRET_KEY]
FLASK_DEBUG=false
FLASK_ENV=production

# 服务器配置
HOST=0.0.0.0
PORT=7860

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=flask_app.log
LOG_MAX_BYTES=10000000         # 10MB
LOG_BACKUP_COUNT=5

# ==================== 部署平台配置示例 ====================

# Railway 部署示例:
# USE_DATABASE=true
# DATABASE_URL=${{MySQL.DATABASE_URL}}
# SECRET_KEY=[YOUR_GENERATED_SECRET_KEY]

# Hugging Face Spaces 部署示例:
# USE_DATABASE=true
# DATABASE_URL=mysql://[USER]:[PASS]@[HOST]:[PORT]/[DB]?ssl-mode=REQUIRED
# OPENAI_API_KEY=[YOUR_API_KEY]

# Render 部署示例:
# USE_DATABASE=true
# DATABASE_URL=mysql://[USER]:[PASS]@[HOST]:[PORT]/[DB]
# SECRET_KEY=[YOUR_SECRET_KEY]

# ==================== 安全提示 ====================

# 1. 生成安全的SECRET_KEY:
#    python -c "import secrets; print(secrets.token_hex(32))"

# 2. 数据库连接安全:
#    - 生产环境使用SSL连接
#    - 定期轮换数据库密码
#    - 限制数据库访问IP

# 3. API密钥安全:
#    - 不要在代码中硬编码API密钥
#    - 使用环境变量管理敏感信息
#    - 定期检查和轮换API密钥

# ==================== 性能优化建议 ====================

# 1. 缓存配置:
#    - 根据数据更新频率调整TTL
#    - 监控缓存命中率
#    - 根据内存大小调整MEMORY_CACHE_SIZE

# 2. 数据库配置:
#    - 根据并发用户数调整连接池大小
#    - 监控数据库连接数和性能
#    - 定期清理过期缓存数据

# 3. API调用优化:
#    - 合理设置重试次数和延迟
#    - 监控API调用频率和成功率
#    - 使用缓存减少不必要的API调用
