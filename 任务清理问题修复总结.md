# 任务清理问题修复总结

## 问题描述
用户反馈：市场扫描功能前端和后端都正常开始运行，后端显示扫描任务完成，但前端出现404错误，提示"找不到指定的扫描任务"。

## 问题分析

### 根本原因
通过分析日志发现，问题出现在任务清理机制上：

```
2025-06-08 03:11:35,729 - WARNING - 任务 d23134db-fdd2-4c19-a44f-7ded557ad6ac 不存在，当前任务列表: []
```

这说明正在运行的扫描任务被清理线程意外删除了。

### 问题定位

#### 1. 清理条件过于激进
原始的清理条件：
```python
if ((task['status'] in [TASK_COMPLETED, TASK_FAILED] and
     (now - updated_at).total_seconds() > 3600) or
        ((now - updated_at).total_seconds() > 10800)):
```

**问题**：第二个条件 `(now - updated_at).total_seconds() > 10800` 会删除所有超过3小时的任务，无论状态如何。但扫描任务可能需要几分钟才能完成，在这期间如果清理线程运行，就会误删正在运行的任务。

#### 2. 清理频率过高
原始清理频率：每5分钟运行一次
```python
time.sleep(600)  # 5分钟
```

**问题**：过于频繁的清理增加了误删正在运行任务的概率。

## 修复措施

### ✅ 1. 重新设计清理条件

**修复前**：简单的时间判断，容易误删正在运行的任务
**修复后**：根据任务状态设置不同的清理时间

```python
def clean_old_tasks():
    """清理旧的扫描任务"""
    with task_lock:
        now = datetime.now()
        to_delete = []

        for task_id, task in scan_tasks.items():
            try:
                updated_at = datetime.strptime(task['updated_at'], '%Y-%m-%d %H:%M:%S')
                time_diff = (now - updated_at).total_seconds()
                
                # 清理条件：
                # 1. 已完成或失败的任务超过1小时
                # 2. 正在运行的任务超过2小时（防止卡死）
                # 3. 等待中的任务超过30分钟（可能是孤儿任务）
                should_delete = False
                
                if task['status'] in [TASK_COMPLETED, TASK_FAILED]:
                    # 完成或失败的任务，1小时后清理
                    should_delete = time_diff > 3600
                elif task['status'] == TASK_RUNNING:
                    # 运行中的任务，2小时后清理（防止卡死）
                    should_delete = time_diff > 7200
                elif task['status'] == TASK_PENDING:
                    # 等待中的任务，30分钟后清理（可能是孤儿任务）
                    should_delete = time_diff > 1800
                else:
                    # 其他状态的任务，1小时后清理
                    should_delete = time_diff > 3600
                
                if should_delete:
                    to_delete.append(task_id)
                    app.logger.info(f"准备清理任务 {task_id}，状态: {task['status']}, 已存在: {time_diff/60:.1f}分钟")
                    
            except Exception as e:
                app.logger.warning(f"任务 {task_id} 日期解析错误: {str(e)}")
                to_delete.append(task_id)

        # 删除旧任务
        for task_id in to_delete:
            del scan_tasks[task_id]

        return len(to_delete)
```

### ✅ 2. 调整清理频率

**修复前**：每5分钟清理一次
**修复后**：每30分钟清理一次

```python
# 每 30 分钟运行一次，减少对正在运行任务的干扰
time.sleep(1800)
```

### ✅ 3. 增强日志记录

添加了详细的清理日志，便于调试：
- 记录准备清理的任务信息
- 记录任务状态和存在时间
- 记录日期解析错误

## 修复后的清理策略

### 任务状态分类处理

| 任务状态 | 清理时间 | 说明 |
|---------|---------|------|
| COMPLETED | 1小时 | 已完成的任务，保留1小时供查看结果 |
| FAILED | 1小时 | 失败的任务，保留1小时供查看错误信息 |
| RUNNING | 2小时 | 运行中的任务，2小时后清理防止卡死 |
| PENDING | 30分钟 | 等待中的任务，30分钟后清理孤儿任务 |
| 其他状态 | 1小时 | 未知状态的任务，1小时后清理 |

### 清理频率优化

- **修复前**：每5分钟清理一次，过于频繁
- **修复后**：每30分钟清理一次，减少干扰

## 预期效果

修复后，市场扫描功能应该：

1. ✅ **任务不会被意外删除**：正在运行的任务在完成前不会被清理
2. ✅ **前端轮询正常**：不会出现404错误
3. ✅ **扫描结果正常显示**：任务完成后结果能正确显示
4. ✅ **内存管理优化**：旧任务仍会被适时清理，避免内存泄漏

## 测试验证

### 测试场景
1. 启动保险行业扫描任务
2. 持续轮询任务状态
3. 验证任务在执行过程中不会被删除
4. 验证任务完成后结果正确显示

### 预期结果
- 任务在执行过程中状态查询始终返回200
- 不会出现"找不到指定的扫描任务"错误
- 扫描完成后前端正确显示结果

## 用户操作指南

### 1. 重启服务器
修复已应用，服务器已重启。

### 2. 测试扫描功能
1. 访问 http://localhost:8888/market_scan
2. 选择"保险"行业
3. 点击"开始扫描"
4. 观察扫描进度和最终结果

### 3. 如果仍有问题
请提供：
- 浏览器控制台的完整错误信息
- 服务器日志中的相关输出
- 具体的操作步骤和时间点

## 技术改进点

1. **状态感知清理**：根据任务状态设置不同的清理策略
2. **时间窗口优化**：给正在运行的任务足够的执行时间
3. **日志增强**：详细记录清理过程，便于问题诊断
4. **频率优化**：减少清理频率，降低对正常任务的影响

## 总结

这次修复解决了任务被意外删除的核心问题，通过：
- 重新设计清理条件，区分不同任务状态
- 调整清理频率，减少对正在运行任务的干扰
- 增强日志记录，便于问题诊断

修复后，用户应该能够正常使用市场扫描功能，不再出现"找不到指定的扫描任务"的错误。
