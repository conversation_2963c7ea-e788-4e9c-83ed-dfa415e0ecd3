{% extends "layout.html" %}

{% block title %}基本面分析 - {{ stock_code }} - 智能分析系统{% endblock %}

{% block content %}
<div class="page-transition">
    <!-- Enhanced Material Design 3 分析表单 -->
    <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
        <div class="md3-card-header">
            <h2 class="md3-card-title">
                <i class="material-icons">assessment</i> 基本面分析
            </h2>
            <p class="md3-card-subtitle">深度分析股票财务指标与估值水平</p>
        </div>
        <div class="md3-card-body">
            <form id="fundamental-form" style="display: grid; grid-template-columns: 2fr 1fr auto; gap: 20px; align-items: end;">
                <div class="md3-text-field md3-text-field-outlined">
                    <input type="text" class="md3-text-field-input" id="stock-code" placeholder=" " required>
                    <label class="md3-text-field-label">股票代码</label>
                    <div class="md3-text-field-supporting-text">例如：600519、0700.HK、AAPL</div>
                </div>

                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="market-type">
                        <option value="A" selected>A股</option>
                        <option value="HK">港股</option>
                        <option value="US">美股</option>
                    </select>
                    <label class="md3-text-field-label">市场类型</label>
                </div>

                <button type="submit" class="md3-button md3-button-filled md3-button-large">
                    <i class="material-icons">analytics</i> 开始分析
                </button>
            </form>
        </div>
    </div>

    <!-- Enhanced Material Design 3 分析结果区域 -->
    <div id="fundamental-result" style="display: none;">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 财务概况卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">account_balance</i> 财务概况
                    </h3>
                    <p class="md3-card-subtitle">核心财务指标与估值分析</p>
                </div>
                <div class="md3-card-body">
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 32px;">
                        <div style="flex: 1;">
                            <h2 id="stock-name" style="margin: 0 0 8px 0; font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-on-surface);"></h2>
                            <p id="stock-info" style="margin: 0; color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size);"></p>
                        </div>
                        <div>
                            <span id="fundamental-score" class="md3-score-indicator md3-score-good" style="font-size: 18px; padding: 12px 20px;"></span>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px;">
                        <div>
                            <div style="margin-bottom: 24px;">
                                <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500; margin-bottom: 16px; display: block;">估值指标</span>
                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                        <span style="color: var(--md-sys-color-on-surface-variant);">PE(TTM)</span>
                                        <span id="pe-ttm" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                        <span style="color: var(--md-sys-color-on-surface-variant);">PB</span>
                                        <span id="pb" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                        <span style="color: var(--md-sys-color-on-surface-variant);">PS(TTM)</span>
                                        <span id="ps-ttm" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500; margin-bottom: 16px; display: block;">盈利能力</span>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">ROE</span>
                                    <span id="roe" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">毛利率</span>
                                    <span id="gross-margin" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">净利润率</span>
                                    <span id="net-profit-margin" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成长性分析卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">trending_up</i> 成长性分析
                    </h3>
                    <p class="md3-card-subtitle">营收与利润增长趋势</p>
                </div>
                <div class="md3-card-body">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 24px;">
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500; margin-bottom: 16px; display: block;">营收增长</span>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">3年CAGR</span>
                                    <span id="revenue-growth-3y" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">5年CAGR</span>
                                    <span id="revenue-growth-5y" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500; margin-bottom: 16px; display: block;">利润增长</span>
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">3年CAGR</span>
                                    <span id="profit-growth-3y" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                    <span style="color: var(--md-sys-color-on-surface-variant);">5年CAGR</span>
                                    <span id="profit-growth-5y" class="md3-financial-value" style="color: var(--md-sys-color-on-surface);"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="growth-chart" style="height: 200px;"></div>
                </div>
            </div>
        </div>

        <!-- 评分卡片组 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 32px;">
            <!-- 估值评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-fade-in">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">price_check</i> 估值评分
                    </h3>
                    <p class="md3-card-subtitle">相对估值水平分析</p>
                </div>
                <div class="md3-card-body">
                    <div id="valuation-chart" style="height: 240px;"></div>
                    <p id="valuation-comment" style="margin-top: 16px; color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5;"></p>
                </div>
            </div>

            <!-- 财务健康评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-fade-in" style="animation-delay: 0.1s;">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">health_and_safety</i> 财务健康评分
                    </h3>
                    <p class="md3-card-subtitle">财务稳健性评估</p>
                </div>
                <div class="md3-card-body">
                    <div id="financial-chart" style="height: 240px;"></div>
                    <p id="financial-comment" style="margin-top: 16px; color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5;"></p>
                </div>
            </div>

            <!-- 成长性评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-fade-in" style="animation-delay: 0.2s;">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">growth</i> 成长性评分
                    </h3>
                    <p class="md3-card-subtitle">增长潜力评估</p>
                </div>
                <div class="md3-card-body">
                    <div id="growth-score-chart" style="height: 240px;"></div>
                    <p id="growth-comment" style="margin-top: 16px; color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5;"></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#fundamental-form').submit(function(e) {
            e.preventDefault();
            const stockCode = $('#stock-code').val().trim();
            const marketType = $('#market-type').val();

            if (!stockCode) {
                showError('请输入股票代码！');
                return;
            }

            fetchFundamentalAnalysis(stockCode);
        });
    });

    function fetchFundamentalAnalysis(stockCode) {
        showLoading();

        $.ajax({
            url: '/api/fundamental_analysis',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode
            }),
            success: function(response) {
                hideLoading();
                renderFundamentalAnalysis(response, stockCode);
                $('#fundamental-result').show();
            },
            error: function(xhr, status, error) {
                hideLoading();
                let errorMsg = '获取基本面分析失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }
                showError(errorMsg);
            }
        });
    }

    function renderFundamentalAnalysis(data, stockCode) {
        // 设置基本信息
        $('#stock-name').text(data.details.indicators.stock_name || stockCode);
        $('#stock-info').text(data.details.indicators.industry || '未知行业');

        // 设置评分
        const scoreClass = getMD3ScoreColorClass(data.total);
        $('#fundamental-score').text(data.total).removeClass().addClass(`md3-score-indicator ${scoreClass}`);

        // 设置估值指标
        $('#pe-ttm').text(formatNumber(data.details.indicators.pe_ttm, 2));
        $('#pb').text(formatNumber(data.details.indicators.pb, 2));
        $('#ps-ttm').text(formatNumber(data.details.indicators.ps_ttm, 2));

        // 设置盈利能力
        $('#roe').text(formatPercent(data.details.indicators.roe, 2));
        $('#gross-margin').text(formatPercent(data.details.indicators.gross_margin, 2));
        $('#net-profit-margin').text(formatPercent(data.details.indicators.net_profit_margin, 2));

        // 设置成长率
        $('#revenue-growth-3y').text(formatPercent(data.details.growth.revenue_growth_3y, 2));
        $('#revenue-growth-5y').text(formatPercent(data.details.growth.revenue_growth_5y, 2));
        $('#profit-growth-3y').text(formatPercent(data.details.growth.profit_growth_3y, 2));
        $('#profit-growth-5y').text(formatPercent(data.details.growth.profit_growth_5y, 2));

        // 评论
        $('#valuation-comment').text("估值处于行业" + (data.valuation > 20 ? "合理水平" : "偏高水平"));
        $('#financial-comment').text("财务状况" + (data.financial_health > 30 ? "良好" : "一般"));
        $('#growth-comment').text("成长性" + (data.growth > 20 ? "较好" : "一般"));

        // 渲染图表
        renderValuationChart(data.valuation);
        renderFinancialChart(data.financial_health);
        renderGrowthScoreChart(data.growth);
        renderGrowthChart(data.details.growth);
    }

    function renderValuationChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 200,
                type: 'radialBar',
            },
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '70%',
                    },
                    dataLabels: {
                        name: {
                            fontSize: '22px',
                        },
                        value: {
                            fontSize: '16px',
                        },
                        total: {
                            show: true,
                            label: '估值',
                            formatter: function() {
                                return score;
                            }
                        }
                    }
                }
            },
            colors: ['#1ab7ea'],
            labels: ['估值'],
        };

        const chart = new ApexCharts(document.querySelector("#valuation-chart"), options);
        chart.render();
    }

    function renderFinancialChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 200,
                type: 'radialBar',
            },
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '70%',
                    },
                    dataLabels: {
                        name: {
                            fontSize: '22px',
                        },
                        value: {
                            fontSize: '16px',
                        },
                        total: {
                            show: true,
                            label: '财务',
                            formatter: function() {
                                return score;
                            }
                        }
                    }
                }
            },
            colors: ['#20E647'],
            labels: ['财务'],
        };

        const chart = new ApexCharts(document.querySelector("#financial-chart"), options);
        chart.render();
    }

    function renderGrowthScoreChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 200,
                type: 'radialBar',
            },
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '70%',
                    },
                    dataLabels: {
                        name: {
                            fontSize: '22px',
                        },
                        value: {
                            fontSize: '16px',
                        },
                        total: {
                            show: true,
                            label: '成长',
                            formatter: function() {
                                return score;
                            }
                        }
                    }
                }
            },
            colors: ['#F9CE1D'],
            labels: ['成长'],
        };

        const chart = new ApexCharts(document.querySelector("#growth-score-chart"), options);
        chart.render();
    }

    function renderGrowthChart(growthData) {
        const options = {
            series: [{
                name: '营收增长率',
                data: [
                    growthData.revenue_growth_3y || 0,
                    growthData.revenue_growth_5y || 0
                ]
            }, {
                name: '利润增长率',
                data: [
                    growthData.profit_growth_3y || 0,
                    growthData.profit_growth_5y || 0
                ]
            }],
            chart: {
                type: 'bar',
                height: 150,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: ['3年CAGR', '5年CAGR'],
            },
            yaxis: {
                title: {
                    text: '百分比 (%)'
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + "%"
                    }
                }
            }
        };

        const chart = new ApexCharts(document.querySelector("#growth-chart"), options);
        chart.render();
    }
</script>
{% endblock %}