{"swagger": "2.0", "info": {"title": "股票智能分析系统 API", "description": "股票智能分析系统的REST API文档，提供投资组合分析、个股分析、批量评分等功能", "version": "3.0.0", "contact": {"name": "API支持", "email": "<EMAIL>"}}, "host": "localhost:8888", "basePath": "/", "schemes": ["http", "https"], "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key", "description": "API密钥认证"}, "HmacAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-HMAC-Signature", "description": "HMAC签名认证"}}, "paths": {"/api/v1/portfolio/analyze": {"post": {"summary": "投资组合分析", "description": "分析投资组合的整体表现和风险", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["stocks"], "properties": {"stocks": {"type": "array", "items": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "000001.SZ"}, "weight": {"type": "number", "example": 0.3}, "market_type": {"type": "string", "example": "A"}}}}, "analysis_params": {"type": "object", "properties": {"risk_preference": {"type": "string", "example": "moderate"}, "time_horizon": {"type": "string", "example": "medium"}}}}}}], "responses": {"200": {"description": "投资组合分析成功", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"portfolio_score": {"type": "number"}, "risk_level": {"type": "string"}, "risk_analysis": {"type": "object"}, "recommendations": {"type": "array", "items": {"type": "string"}}, "individual_stocks": {"type": "array"}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "认证失败"}, "403": {"description": "权限不足"}, "500": {"description": "服务器内部错误"}}}}, "/api/v1/stock/analyze": {"post": {"summary": "个股分析", "description": "分析单只股票的详细信息", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["stock_code"], "properties": {"stock_code": {"type": "string", "example": "000001.SZ"}, "market_type": {"type": "string", "example": "A"}, "analysis_depth": {"type": "string", "example": "full"}, "include_ai_analysis": {"type": "boolean", "example": true}, "time_range": {"type": "integer", "example": 60}}}}], "responses": {"200": {"description": "个股分析成功", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"stock_info": {"type": "object"}, "analysis_result": {"type": "object"}, "technical_analysis": {"type": "object"}, "fundamental_analysis": {"type": "object"}, "risk_assessment": {"type": "object"}, "ai_analysis": {"type": "object"}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "认证失败"}, "500": {"description": "服务器内部错误"}}}}, "/api/v1/stocks/batch-score": {"post": {"summary": "批量股票评分", "description": "批量分析多只股票的评分", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["stock_codes"], "properties": {"stock_codes": {"type": "array", "items": {"type": "string"}, "example": ["000001.SZ", "600000.SH"]}, "market_type": {"type": "string", "example": "A"}, "min_score": {"type": "number", "example": 60}, "sort_by": {"type": "string", "example": "score"}, "sort_order": {"type": "string", "example": "desc"}}}}], "responses": {"200": {"description": "批量评分成功", "schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"total_analyzed": {"type": "integer"}, "qualified_count": {"type": "integer"}, "results": {"type": "array"}}}}}}}}}, "/analyze": {"post": {"summary": "分析股票", "description": "分析单只或多只股票", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_codes": {"type": "array", "items": {"type": "string"}, "example": ["600519", "000858"]}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功分析股票"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/start_stock_analysis": {"post": {"summary": "启动股票分析任务", "description": "启动异步股票分析任务", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功启动分析任务"}}}}, "/api/analysis_status/{task_id}": {"get": {"summary": "获取分析任务状态", "description": "获取异步分析任务的状态和结果", "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "成功获取任务状态和结果"}, "404": {"description": "找不到指定的任务"}}}}, "/api/stock_data": {"get": {"summary": "获取股票数据", "description": "获取股票历史数据和技术指标", "parameters": [{"name": "stock_code", "in": "query", "required": true, "type": "string"}, {"name": "market_type", "in": "query", "required": false, "type": "string", "default": "A"}, {"name": "period", "in": "query", "required": false, "type": "string", "enum": ["1m", "3m", "6m", "1y"], "default": "1y"}], "responses": {"200": {"description": "成功获取股票数据"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/start_market_scan": {"post": {"summary": "启动市场扫描任务", "description": "启动异步市场扫描任务", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_list": {"type": "array", "items": {"type": "string"}, "example": ["600519", "000858"]}, "min_score": {"type": "integer", "example": 60}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功启动扫描任务"}}}}, "/api/scan_status/{task_id}": {"get": {"summary": "获取扫描任务状态", "description": "获取异步扫描任务的状态和结果", "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "成功获取任务状态和结果"}, "404": {"description": "找不到指定的任务"}}}}, "/api/index_stocks": {"get": {"summary": "获取指数成分股", "description": "获取指定指数的成分股列表", "parameters": [{"name": "index_code", "in": "query", "required": true, "type": "string", "example": "000300"}], "responses": {"200": {"description": "成功获取指数成分股"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/industry_stocks": {"get": {"summary": "获取行业成分股", "description": "获取指定行业的成分股列表", "parameters": [{"name": "industry", "in": "query", "required": true, "type": "string", "example": "银行"}], "responses": {"200": {"description": "成功获取行业成分股"}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/api/fundamental_analysis": {"post": {"summary": "基本面分析", "description": "获取股票的基本面分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}}}}], "responses": {"200": {"description": "成功获取基本面分析结果"}}}}, "/api/capital_flow": {"post": {"summary": "资金流向分析", "description": "获取股票的资金流向分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "days": {"type": "integer", "example": 10}}}}], "responses": {"200": {"description": "成功获取资金流向分析结果"}}}}, "/api/scenario_predict": {"post": {"summary": "情景预测", "description": "获取股票的多情景预测结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}, "days": {"type": "integer", "example": 60}}}}], "responses": {"200": {"description": "成功获取情景预测结果"}}}}, "/api/qa": {"post": {"summary": "智能问答", "description": "获取股票相关问题的智能回答", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "question": {"type": "string", "example": "这只股票的主要支撑位是多少？"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功获取智能回答"}}}}, "/api/risk_analysis": {"post": {"summary": "风险分析", "description": "获取股票的风险分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"stock_code": {"type": "string", "example": "600519"}, "market_type": {"type": "string", "example": "A"}}}}], "responses": {"200": {"description": "成功获取风险分析结果"}}}}, "/api/portfolio_risk": {"post": {"summary": "投资组合风险分析", "description": "获取投资组合的整体风险分析结果", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"portfolio": {"type": "array", "items": {"type": "object", "properties": {"stock_code": {"type": "string"}, "weight": {"type": "number"}, "market_type": {"type": "string"}}}, "example": [{"stock_code": "600519", "weight": 30, "market_type": "A"}, {"stock_code": "000858", "weight": 20, "market_type": "A"}]}}}}], "responses": {"200": {"description": "成功获取投资组合风险分析结果"}}}}, "/api/index_analysis": {"get": {"summary": "指数分析", "description": "获取指数的整体分析结果", "parameters": [{"name": "index_code", "in": "query", "required": true, "type": "string", "example": "000300"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "example": 30}], "responses": {"200": {"description": "成功获取指数分析结果"}}}}, "/api/industry_analysis": {"get": {"summary": "行业分析", "description": "获取行业的整体分析结果", "parameters": [{"name": "industry", "in": "query", "required": true, "type": "string", "example": "银行"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "example": 30}], "responses": {"200": {"description": "成功获取行业分析结果"}}}}, "/api/industry_compare": {"get": {"summary": "行业比较", "description": "比较不同行业的表现", "parameters": [{"name": "limit", "in": "query", "required": false, "type": "integer", "example": 10}], "responses": {"200": {"description": "成功获取行业比较结果"}}}}}, "definitions": {"Stock": {"type": "object", "properties": {"stock_code": {"type": "string"}, "stock_name": {"type": "string"}, "price": {"type": "number"}, "price_change": {"type": "number"}}}, "AnalysisResult": {"type": "object", "properties": {"score": {"type": "number"}, "recommendation": {"type": "string"}}}}}