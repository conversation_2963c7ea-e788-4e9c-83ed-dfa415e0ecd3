# 🔧 故障排除指南

## 🚨 常见部署错误及解决方案

### 错误类型1：构建失败 (Build Failed)

#### 症状：
- 部署日志显示 "Build failed"
- 看到类似错误：`ERROR: Could not find a version that satisfies the requirement`

#### 可能原因：
1. `requirements.txt` 文件有问题
2. Python 版本不兼容
3. 依赖包版本冲突

#### 解决步骤：
```bash
# 1. 检查 requirements.txt 文件
# 确保文件存在且格式正确

# 2. 简化依赖包版本
# 将 requirements.txt 中的版本号去掉，如：
# 从：pandas==2.2.2
# 改为：pandas

# 3. 重新部署
# 在平台上触发重新部署
```

#### Railway 解决方法：
1. 进入项目 → Deployments 标签
2. 点击 "Redeploy" 按钮
3. 查看构建日志找到具体错误

#### Render 解决方法：
1. 进入服务页面 → Logs 标签
2. 查看构建日志
3. 修改代码后自动重新部署

---

### 错误类型2：应用启动失败 (Application Failed to Start)

#### 症状：
- 构建成功但应用无法启动
- 错误信息：`ModuleNotFoundError: No module named 'web_server'`

#### 可能原因：
1. 启动命令错误
2. 主文件名不正确
3. 文件路径问题

#### 解决步骤：

**检查启动命令：**
- Railway：自动检测，通常不需要修改
- Render：确保启动命令为：
  ```
  gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 300 web_server:app
  ```

**检查文件结构：**
```
你的项目/
├── web_server.py  ← 主文件必须存在
├── requirements.txt
├── railway.toml
└── 其他文件...
```

---

### 错误类型3：环境变量问题

#### 症状：
- 应用启动但功能不工作
- 错误信息：`KeyError: 'OPENAI_API_KEY'`

#### 解决步骤：

**Railway 环境变量设置：**
1. 进入项目 → 选择 Web 服务
2. 点击 "Variables" 标签
3. 添加必需的环境变量：
   ```
   OPENAI_API_KEY = sk-你的密钥
   OPENAI_API_MODEL = gpt-4o
   USE_DATABASE = true
   ```

**Render 环境变量设置：**
1. 进入 Web Service → Environment 标签
2. 添加环境变量
3. 点击 "Save Changes"

**验证环境变量：**
- 检查变量名是否正确（区分大小写）
- 检查变量值是否完整
- 确认没有多余的空格

---

### 错误类型4：数据库连接失败

#### 症状：
- 错误信息：`sqlalchemy.exc.OperationalError: could not connect to server`

#### Railway 解决方法：
1. 确认已添加 PostgreSQL 数据库
2. 数据库状态显示为 "Running"
3. 环境变量会自动设置，无需手动配置

#### Render 解决方法：
1. 确认 PostgreSQL 数据库已创建
2. 复制正确的 "External Database URL"
3. 在 Web Service 中设置 `DATABASE_URL` 环境变量

**数据库 URL 格式检查：**
```
正确格式：postgresql://username:password@hostname:port/database_name
错误格式：postgres://... (注意是 postgresql 不是 postgres)
```

---

### 错误类型5：API 调用失败

#### 症状：
- 股票分析功能不工作
- 错误信息：`OpenAI API error` 或 `Invalid API key`

#### 解决步骤：

**检查 API 密钥：**
1. 登录 [OpenAI 官网](https://platform.openai.com/)
2. 检查 API 密钥是否有效
3. 确认账户有足够余额

**检查 API 密钥格式：**
```
正确格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
长度：51 个字符
开头：sk-
```

**重新设置 API 密钥：**
1. 在部署平台删除旧的 `OPENAI_API_KEY`
2. 添加新的 API 密钥
3. 重新部署应用

---

## 🔍 调试技巧

### 1. 查看日志

**Railway 日志查看：**
1. 项目页面 → Deployments 标签
2. 点击最新的部署记录
3. 查看 "Build Logs" 和 "Deploy Logs"

**Render 日志查看：**
1. Web Service 页面 → Logs 标签
2. 实时查看应用日志
3. 可以按时间筛选日志

### 2. 本地测试

在部署前先在本地测试：
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 设置环境变量
export OPENAI_API_KEY=你的密钥
export USE_DATABASE=false

# 3. 运行应用
python web_server.py

# 4. 访问 http://localhost:8888 测试
```

### 3. 分步排查

**第一步：检查基础配置**
- [ ] GitHub 仓库是否公开
- [ ] 所有必需文件是否存在
- [ ] 环境变量是否正确设置

**第二步：检查构建过程**
- [ ] 构建日志是否有错误
- [ ] 依赖包是否安装成功
- [ ] Python 版本是否兼容

**第三步：检查运行时**
- [ ] 应用是否成功启动
- [ ] 端口是否正确绑定
- [ ] 数据库连接是否正常

---

## 📞 获取帮助的渠道

### 1. 官方文档
- **Railway**: [docs.railway.app](https://docs.railway.app)
- **Render**: [render.com/docs](https://render.com/docs)
- **OpenAI**: [platform.openai.com/docs](https://platform.openai.com/docs)

### 2. 社区支持
- **Railway Discord**: 官方 Discord 社区
- **Render Community**: 官方论坛
- **Stack Overflow**: 搜索相关错误信息

### 3. 错误信息搜索
当遇到错误时：
1. 复制完整的错误信息
2. 在 Google 搜索错误信息
3. 查看 Stack Overflow 的解决方案
4. 参考 GitHub Issues

---

## 🎯 预防措施

### 1. 部署前检查
- 在本地完整测试所有功能
- 确认所有环境变量都已准备
- 检查代码是否有语法错误

### 2. 版本管理
- 使用 Git 管理代码版本
- 在部署前创建备份分支
- 记录每次部署的更改

### 3. 监控和维护
- 定期检查应用运行状态
- 监控 API 使用量和费用
- 及时更新依赖包版本

---

## 🆘 紧急恢复

如果应用完全无法访问：

### 快速恢复步骤：
1. **回滚到上一个版本**
   - Railway: 在 Deployments 中选择之前的成功部署
   - Render: 重新部署之前的 Git 提交

2. **重新创建服务**
   - 删除当前服务
   - 使用相同配置重新创建
   - 重新设置环境变量

3. **联系技术支持**
   - 如果问题持续存在
   - 提供详细的错误信息
   - 说明已尝试的解决方案

记住：大多数问题都有解决方案，保持耐心和细心！ 💪
