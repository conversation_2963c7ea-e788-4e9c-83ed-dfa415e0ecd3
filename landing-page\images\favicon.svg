<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#1565c0" stroke-width="1"/>
  
  <!-- Stock chart icon -->
  <g fill="#ffffff" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
    <!-- Chart lines -->
    <polyline points="6,22 10,18 14,20 18,14 22,16 26,10" fill="none"/>
    
    <!-- Data points -->
    <circle cx="6" cy="22" r="1.5" fill="#ffffff"/>
    <circle cx="10" cy="18" r="1.5" fill="#ffffff"/>
    <circle cx="14" cy="20" r="1.5" fill="#ffffff"/>
    <circle cx="18" cy="14" r="1.5" fill="#ffffff"/>
    <circle cx="22" cy="16" r="1.5" fill="#ffffff"/>
    <circle cx="26" cy="10" r="1.5" fill="#ffffff"/>
  </g>
  
  <!-- Trend arrow -->
  <g fill="#ffeb3b" stroke="#ffeb3b" stroke-width="1.5">
    <polyline points="20,8 26,8 26,14" fill="none"/>
    <polyline points="21,9 26,8 25,13" fill="none"/>
  </g>
</svg>
