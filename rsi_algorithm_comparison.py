#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI算法对比分析 - 找出网页版和独立程序RSI计算的差异
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from standalone_stock_scorer import StockScorer, RealDataService

def calculate_rsi_sma_method(prices, period=14):
    """使用简单移动平均计算RSI（原项目方法）"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_rsi_wilder_method(prices, period=14):
    """使用Wilder平滑方法计算RSI（标准方法）"""
    delta = prices.diff().dropna()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    
    # 初始化数组
    avg_gain = np.zeros(len(gain))
    avg_loss = np.zeros(len(loss))
    
    # 计算初始平均值
    if len(gain) >= period:
        avg_gain[period-1] = np.mean(gain[:period])
        avg_loss[period-1] = np.mean(loss[:period])
        
        # Wilder平滑
        for i in range(period, len(gain)):
            avg_gain[i] = (avg_gain[i-1] * (period-1) + gain[i]) / period
            avg_loss[i] = (avg_loss[i-1] * (period-1) + loss[i]) / period
    
    # 计算RSI
    rs = np.divide(avg_gain, avg_loss, out=np.zeros_like(avg_gain), where=avg_loss!=0)
    rsi = 100 - (100 / (1 + rs))
    
    # 转换为pandas Series
    rsi_series = pd.Series(rsi, index=prices.index[1:])
    return rsi_series

def calculate_rsi_ema_method(prices, period=14):
    """使用EMA方法计算RSI（另一种常见方法）"""
    delta = prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 使用EMA计算平均收益和损失
    avg_gain = gain.ewm(alpha=1/period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/period, adjust=False).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def analyze_rsi_methods():
    """对比不同RSI计算方法"""
    print("=" * 80)
    print("RSI算法对比分析 - 002295精艺股份")
    print("=" * 80)
    
    # 获取数据
    data_service = RealDataService()
    df = data_service.get_stock_price_history("002295.XSHE")
    
    if df is None:
        print("❌ 无法获取数据")
        return
    
    prices = df['close']
    
    # 使用不同方法计算RSI
    rsi_sma = calculate_rsi_sma_method(prices)
    rsi_wilder = calculate_rsi_wilder_method(prices)
    rsi_ema = calculate_rsi_ema_method(prices)
    
    # 获取独立程序的RSI
    scorer = StockScorer()
    df_with_indicators = scorer.calculate_technical_indicators(df)
    rsi_program = df_with_indicators['RSI']
    
    print(f"📊 最近10天RSI对比:")
    print(f"{'日期':<12} {'SMA方法':<8} {'Wilder方法':<10} {'EMA方法':<8} {'程序计算':<8} {'价格':<8}")
    print("-" * 70)
    
    recent_data = df.tail(10)
    for i, (_, row) in enumerate(recent_data.iterrows()):
        idx = len(df) - 10 + i
        date_str = row['date'].strftime('%m-%d')
        price = row['close']
        
        sma_val = rsi_sma.iloc[idx] if idx < len(rsi_sma) else np.nan
        wilder_val = rsi_wilder.iloc[idx-1] if idx-1 < len(rsi_wilder) and idx > 0 else np.nan
        ema_val = rsi_ema.iloc[idx] if idx < len(rsi_ema) else np.nan
        program_val = rsi_program.iloc[idx] if idx < len(rsi_program) else np.nan
        
        print(f"{date_str:<12} {sma_val:<8.2f} {wilder_val:<10.2f} {ema_val:<8.2f} {program_val:<8.2f} {price:<8.2f}")
    
    # 分析最新RSI值
    latest_idx = len(df) - 1
    latest_sma = rsi_sma.iloc[latest_idx]
    latest_wilder = rsi_wilder.iloc[latest_idx-1] if latest_idx > 0 else np.nan
    latest_ema = rsi_ema.iloc[latest_idx]
    latest_program = rsi_program.iloc[latest_idx]
    
    print(f"\n🎯 最新RSI值分析 (2025-07-10):")
    print(f"  SMA方法: {latest_sma:.2f}")
    print(f"  Wilder方法: {latest_wilder:.2f}")
    print(f"  EMA方法: {latest_ema:.2f}")
    print(f"  程序计算: {latest_program:.2f}")
    
    # 判断每种方法的评分区间
    methods = [
        ("SMA方法", latest_sma),
        ("Wilder方法", latest_wilder),
        ("EMA方法", latest_ema),
        ("程序计算", latest_program)
    ]
    
    print(f"\n📈 各方法RSI评分分析:")
    for method_name, rsi_val in methods:
        if np.isnan(rsi_val):
            continue
            
        if 40 <= rsi_val <= 60:
            score = 7
            zone = "中性区域 (40-60)"
        elif 30 <= rsi_val < 40 or 60 < rsi_val <= 70:
            score = 10
            zone = "阈值区域 (30-40或60-70)"
        elif rsi_val < 30:
            score = 8
            zone = "超卖区域 (<30)"
        elif rsi_val > 70:
            score = 2
            zone = "超买区域 (>70)"
        
        print(f"  {method_name}: RSI={rsi_val:.2f}, {zone}, 评分={score}分")
    
    return latest_sma, latest_wilder, latest_ema, latest_program

def analyze_potential_web_version_rsi():
    """分析网页版可能使用的RSI计算方法"""
    print(f"\n" + "=" * 80)
    print("网页版RSI推测分析")
    print("=" * 80)
    
    # 已知信息
    web_technical_score = 13
    standalone_technical_score = 21
    difference = 8
    
    print(f"📊 已知信息:")
    print(f"  网页版技术指标评分: {web_technical_score}分")
    print(f"  独立程序技术指标评分: {standalone_technical_score}分")
    print(f"  差异: {difference}分")
    
    print(f"\n🔍 技术指标评分构成分析:")
    print(f"  独立程序: RSI(10分) + MACD(10分) + 布林带(1分) = 21分")
    print(f"  网页版推测: RSI(?分) + MACD(?分) + 布林带(?分) = 13分")
    
    print(f"\n💡 可能的差异组合:")
    print(f"  情况1: RSI差异8分")
    print(f"    独立程序RSI=10分(阈值区域), 网页版RSI=2分(超买区域)")
    print(f"    MACD和布林带评分相同")
    
    print(f"  情况2: MACD差异8分")
    print(f"    独立程序MACD=10分(金叉且柱状图为正), 网页版MACD=2分或0分")
    print(f"    RSI和布林带评分相同")
    
    print(f"  情况3: 多个指标组合差异")
    print(f"    RSI差异3-5分 + MACD差异3-5分")
    
    print(f"\n🎯 最可能的原因:")
    print(f"  基于RSI=67.36的分析，独立程序正确识别为阈值区域(10分)")
    print(f"  网页版可能:")
    print(f"    1. 使用不同的RSI计算方法，得到>70的值(2分)")
    print(f"    2. 使用不同的评分区间划分")
    print(f"    3. 数据获取时间差异导致RSI值不同")

def main():
    """主函数"""
    # 对比不同RSI计算方法
    rsi_values = analyze_rsi_methods()
    
    # 分析网页版可能的RSI计算
    analyze_potential_web_version_rsi()
    
    print(f"\n" + "=" * 80)
    print("结论")
    print("=" * 80)
    
    print(f"\n🎯 关键发现:")
    print(f"  1. 独立程序使用SMA方法计算RSI，结果为67.36")
    print(f"  2. 不同RSI计算方法会产生不同的结果")
    print(f"  3. RSI=67.36落在阈值区域(60-70)，应得10分")
    print(f"  4. 8分差异最可能来源于RSI评分差异")
    
    print(f"\n✅ 结论:")
    print(f"  独立程序的RSI计算和评分是正确的")
    print(f"  网页版可能使用了不同的RSI计算方法或评分标准")
    print(f"  需要进一步验证网页版的具体实现")

if __name__ == "__main__":
    main()
