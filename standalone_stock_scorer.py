#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立股票评分程序 - 真实市场数据版
基于现有股票分析系统的五维度评分算法，使用与网页版完全相同的数据源
支持批量处理CSV格式的股票数据，自动读取list3.csv文件

作者: 股票分析系统
版本: 3.1.0 (纯真实数据版)
日期: 2025-07-10
"""

import pandas as pd
import numpy as np
import logging
import json
import os
import sys
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 检查AKShare库可用性
try:
    import akshare as ak
    print("✅ AKShare库已导入，程序将使用真实市场数据")
except ImportError:
    print("❌ 错误：AKShare库未安装！")
    print("   请先安装AKShare库：pip install akshare")
    print("   程序无法在没有真实数据的情况下运行")
    sys.exit(1)

class RealDataService:
    """真实数据服务类 - 复制原项目的数据获取逻辑"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.max_retries = 5  # 增加到5次重试
        self.retry_delay = 10  # 重试间隔10秒
        self.setup_session()

    def setup_session(self):
        """设置请求会话"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def _convert_stock_code_for_akshare(self, stock_code: str) -> str:
        """将股票代码转换为AKShare API所需的格式"""
        if not stock_code:
            return stock_code

        # 移除后缀，只保留数字部分
        if '.' in stock_code:
            code_part = stock_code.split('.')[0]
            if code_part.isdigit() and len(code_part) == 6:
                return code_part
            else:
                self.logger.warning(f"股票代码格式异常: {stock_code}")
                return stock_code

        # 如果已经是纯数字格式，直接返回
        if stock_code.isdigit() and len(stock_code) == 6:
            return stock_code

        self.logger.warning(f"无法识别的股票代码格式: {stock_code}")
        return stock_code

    def _retry_api_call(self, api_func, *args, **kwargs):
        """带增强重试机制的API调用"""
        last_exception = None

        # 提取用于日志记录的股票代码，但不传递给api_func
        stock_code = kwargs.pop('_stock_code_for_log', 'unknown')

        for attempt in range(self.max_retries):
            try:
                result = api_func(*args, **kwargs)

                # 检查返回的数据是否足够
                if isinstance(result, pd.DataFrame) and len(result) < 60:
                    raise Exception(f"历史数据不足（需要至少60条记录，实际获取{len(result)}条）")

                return result
            except Exception as e:
                last_exception = e
                self.logger.warning(f"股票 {stock_code} API调用第 {attempt + 1}/{self.max_retries} 次尝试失败: {str(e)}")

                if attempt < self.max_retries - 1:
                    print(f"⚠️ 股票 {stock_code} 数据获取重试中 ({attempt + 1}/{self.max_retries})...")
                    self.logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)

        # 所有重试都失败
        self.logger.error(f"股票 {stock_code} 在 {self.max_retries} 次尝试后仍然失败: {str(last_exception)}")
        raise last_exception

    def get_stock_price_history(self, stock_code: str, market_type: str = 'A',
                               start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """获取股票历史价格数据 - 仅使用真实数据"""

        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')

        try:
            # 转换股票代码为AKShare API所需格式
            original_code = stock_code
            if market_type == 'A':
                akshare_code = self._convert_stock_code_for_akshare(stock_code)
                self.logger.info(f"股票代码转换: {original_code} -> {akshare_code}")
            else:
                akshare_code = stock_code

            def fetch_price_data():
                try:
                    if market_type == 'A':
                        result = ak.stock_zh_a_hist(
                            symbol=akshare_code,
                            start_date=start_date.replace('-', ''),
                            end_date=end_date.replace('-', ''),
                            adjust="qfq"
                        )
                    elif market_type == 'HK':
                        result = ak.stock_hk_daily(symbol=akshare_code, adjust="qfq")
                    elif market_type == 'US':
                        result = ak.stock_us_hist(
                            symbol=akshare_code,
                            start_date=start_date.replace('-', ''),
                            end_date=end_date.replace('-', ''),
                            adjust="qfq"
                        )
                    else:
                        raise ValueError(f"不支持的市场类型: {market_type}")

                    # 验证返回数据
                    if result is None:
                        raise Exception(f"AKShare API返回None，原始代码: {original_code}, AKShare代码: {akshare_code}")

                    if not isinstance(result, pd.DataFrame):
                        raise Exception(f"AKShare API返回数据类型错误，期望DataFrame，实际: {type(result)}")

                    if len(result) == 0:
                        raise Exception(f"AKShare API返回空DataFrame，代码: {akshare_code}, 日期范围: {start_date} 到 {end_date}")

                    self.logger.info(f"成功获取股票 {original_code} 的 {len(result)} 条价格数据")
                    return result

                except Exception as api_error:
                    self.logger.error(f"AKShare API调用失败 - 代码: {akshare_code}, 错误: {api_error}")
                    raise

            # 调用重试函数，但不传递额外参数给fetch_price_data
            # 而是在_retry_api_call中单独保存股票代码用于日志记录
            df = self._retry_api_call(fetch_price_data, _stock_code_for_log=original_code)

            if df is None or len(df) == 0:
                raise Exception(f"获取股票 {original_code} 价格数据失败：API返回空数据")

            # 重命名列名 - 与原项目保持一致
            df = df.rename(columns={
                "日期": "date",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "amount"
            })

            # 确保日期格式正确
            df['date'] = pd.to_datetime(df['date'])

            # 数据类型转换
            numeric_columns = ['open', 'close', 'high', 'low', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 删除空值并排序
            df = df.dropna()
            df = df.sort_values('date')

            # 检查数据是否足够
            if len(df) < 60:
                raise Exception(f"股票 {original_code} 历史数据不足（需要至少60条记录，实际获取{len(df)}条）")

            return df

        except Exception as e:
            self.logger.error(f"获取历史价格数据失败: {e}")
            # 不再抛出异常，而是返回None，让调用者处理
            return None


class StockScorer:
    """独立股票评分器 - 集成真实数据API"""

    def __init__(self, config_file: str = None):
        """初始化评分器"""
        self.setup_logging()
        self.load_config(config_file)
        self.data_service = RealDataService()
        self.logger.info("股票评分器初始化完成")
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('stock_scorer.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self, config_file: str = None):
        """加载配置参数"""
        # 默认配置参数（基于原系统）
        self.params = {
            'ma_periods': {'short': 5, 'medium': 20, 'long': 60},
            'rsi_period': 14,
            'bollinger_period': 20,
            'bollinger_std': 2,
            'volume_ma_period': 20,
            'atr_period': 14
        }
        
        # 评分权重配置
        self.weights = {
            'trend': 0.30,      # 趋势因子权重（日线级别）
            'volatility': 0.15, # 波动率因子权重
            'technical': 0.25,  # 技术指标因子权重
            'volume': 0.20,     # 成交量因子权重（能量守恒维度）
            'momentum': 0.10    # 动量因子权重（周线级别）
        }
        
        # 如果提供了配置文件，尝试加载
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.params.update(config.get('params', {}))
                    self.weights.update(config.get('weights', {}))
                self.logger.info(f"已加载配置文件: {config_file}")
            except Exception as e:
                self.logger.warning(f"加载配置文件失败: {e}，使用默认配置")
    
    def calculate_ema(self, series: pd.Series, period: int) -> pd.Series:
        """计算指数移动平均线"""
        return series.ewm(span=period, adjust=False).mean()
    
    def calculate_rsi(self, series: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def calculate_macd(self, series: pd.Series) -> tuple:
        """计算MACD指标"""
        exp1 = series.ewm(span=12, adjust=False).mean()
        exp2 = series.ewm(span=26, adjust=False).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9, adjust=False).mean()
        hist = macd - signal
        return macd, signal, hist
    
    def calculate_bollinger_bands(self, series: pd.Series, period: int, std_dev: float) -> tuple:
        """计算布林带"""
        middle = series.rolling(window=period).mean()
        std = series.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    def calculate_atr(self, df: pd.DataFrame, period: int) -> pd.Series:
        """计算ATR指标"""
        high = df['high']
        low = df['low']
        close = df['close'].shift(1)
        
        tr1 = high - low
        tr2 = abs(high - close)
        tr3 = abs(low - close)
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            # 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必要的数据列: {missing_columns}")
            
            # 计算移动平均线
            df['MA5'] = self.calculate_ema(df['close'], self.params['ma_periods']['short'])
            df['MA20'] = self.calculate_ema(df['close'], self.params['ma_periods']['medium'])
            df['MA60'] = self.calculate_ema(df['close'], self.params['ma_periods']['long'])
            
            # 计算RSI
            df['RSI'] = self.calculate_rsi(df['close'], self.params['rsi_period'])
            
            # 计算MACD
            df['MACD'], df['Signal'], df['MACD_hist'] = self.calculate_macd(df['close'])
            
            # 计算布林带
            df['BB_upper'], df['BB_middle'], df['BB_lower'] = self.calculate_bollinger_bands(
                df['close'],
                self.params['bollinger_period'],
                self.params['bollinger_std']
            )
            
            # 成交量分析
            df['Volume_MA'] = df['volume'].rolling(window=self.params['volume_ma_period']).mean()
            df['Volume_Ratio'] = df['volume'] / df['Volume_MA']
            
            # 计算ATR和波动率
            df['ATR'] = self.calculate_atr(df, self.params['atr_period'])
            df['Volatility'] = df['ATR'] / df['close'] * 100
            
            # 动量指标
            df['ROC'] = df['close'].pct_change(periods=10) * 100
            
            # 填充NaN值
            df = df.bfill().fillna(0)
            
            return df
            
        except Exception as e:
            self.logger.error(f"计算技术指标时出错: {str(e)}")
            raise
    
    def calculate_score(self, df: pd.DataFrame, market_type: str = 'A') -> Dict[str, Any]:
        """
        计算股票评分 - 使用时空共振交易系统增强
        根据不同的市场特征调整评分权重和标准
        """
        try:
            if len(df) < 2:
                raise ValueError("数据不足，至少需要2行数据进行评分")
            
            latest = df.iloc[-1]
            weights = self.weights.copy()
            
            # 根据市场类型调整权重
            if market_type == 'US':
                weights['trend'] = 0.35
                weights['volatility'] = 0.10
                weights['momentum'] = 0.15
            elif market_type == 'HK':
                weights['volatility'] = 0.20
                weights['volume'] = 0.25
            
            # 1. 趋势评分（最高30分）
            trend_score = self._calculate_trend_score(latest)
            
            # 2. 波动率评分（最高15分）
            volatility_score = self._calculate_volatility_score(latest)
            
            # 3. 技术指标评分（最高25分）
            technical_score = self._calculate_technical_score(latest, df)
            
            # 4. 成交量评分（最高20分）
            volume_score = self._calculate_volume_score(latest, df)
            
            # 5. 动量评分（最高10分）
            momentum_score = self._calculate_momentum_score(latest)
            
            # 根据加权因子计算总分
            final_score = (
                trend_score * weights['trend'] / 0.30 +
                volatility_score * weights['volatility'] / 0.15 +
                technical_score * weights['technical'] / 0.25 +
                volume_score * weights['volume'] / 0.20 +
                momentum_score * weights['momentum'] / 0.10
            )
            
            # 确保评分在0-100范围内
            final_score = max(0, min(100, round(final_score)))
            
            # 生成评级等级
            grade = self._get_grade(final_score)
            
            return {
                'total_score': final_score,
                'grade': grade,
                'dimension_scores': {
                    'trend': trend_score,
                    'volatility': volatility_score,
                    'technical': technical_score,
                    'volume': volume_score,
                    'momentum': momentum_score
                },
                'weights': weights,
                'scoring_details': self._get_scoring_details(latest, df, weights)
            }
            
        except Exception as e:
            self.logger.error(f"计算评分时出错: {str(e)}")
            raise

    def _calculate_trend_score(self, latest: pd.Series) -> int:
        """计算趋势评分（最高30分）"""
        trend_score = 0

        # 均线评估 - "三线形态"分析
        if latest['MA5'] > latest['MA20'] and latest['MA20'] > latest['MA60']:
            # 完美多头排列
            trend_score += 15
        elif latest['MA5'] > latest['MA20']:
            # 短期上升趋势
            trend_score += 10
        elif latest['MA20'] > latest['MA60']:
            # 中期上升趋势
            trend_score += 5

        # 价格位置评估
        if latest['close'] > latest['MA5']:
            trend_score += 5
        if latest['close'] > latest['MA20']:
            trend_score += 5
        if latest['close'] > latest['MA60']:
            trend_score += 5

        return min(30, trend_score)

    def _calculate_volatility_score(self, latest: pd.Series) -> int:
        """计算波动率评分（最高15分）"""
        volatility = latest['Volatility']

        if 1.0 <= volatility <= 2.5:
            # 最佳波动率范围
            return 15
        elif 2.5 < volatility <= 4.0:
            # 较高波动率，次优选择
            return 10
        elif volatility < 1.0:
            # 波动率过低，缺乏能量
            return 5
        else:
            # 波动率过高，风险较大
            return 0

    def _calculate_technical_score(self, latest: pd.Series, df: pd.DataFrame) -> int:
        """计算技术指标评分（最高25分）"""
        technical_score = 0

        # RSI指标评估（10分）
        rsi = latest['RSI']
        if 40 <= rsi <= 60:
            # 中性区域，趋势稳定
            technical_score += 7
        elif 30 <= rsi < 40 or 60 < rsi <= 70:
            # 阈值区域，可能出现反转信号
            technical_score += 10
        elif rsi < 30:
            # 超卖区域，可能出现买入机会
            technical_score += 8
        elif rsi > 70:
            # 超买区域，可能存在卖出风险
            technical_score += 2

        # MACD指标评估（10分）- 与原系统完全一致
        if latest['MACD'] > latest['Signal'] and latest['MACD_hist'] > 0:
            # MACD金叉且柱状图为正
            technical_score += 10
        elif latest['MACD'] > latest['Signal']:
            # MACD金叉
            technical_score += 8
        elif latest['MACD'] < latest['Signal'] and latest['MACD_hist'] < 0:
            # MACD死叉且柱状图为负
            technical_score += 0
        elif latest['MACD_hist'] > df.iloc[-2]['MACD_hist']:
            # MACD柱状图增长，可能出现反转信号
            technical_score += 5

        # 布林带位置评估（5分）- 与原系统完全一致
        bb_position = (latest['close'] - latest['BB_lower']) / (latest['BB_upper'] - latest['BB_lower'])
        if 0.3 <= bb_position <= 0.7:
            # 价格在布林带中间区域，趋势稳定
            technical_score += 3
        elif bb_position < 0.2:
            # 价格接近下轨，可能超卖
            technical_score += 5
        elif bb_position > 0.8:
            # 价格接近上轨，可能超买
            technical_score += 1

        return min(25, technical_score)

    def _calculate_volume_score(self, latest: pd.Series, df: pd.DataFrame) -> int:
        """计算成交量评分（最高20分）- 完全复制原系统逻辑"""
        volume_score = 0

        # 成交量趋势分析 - 与原系统完全一致
        recent_vol_ratio = [df.iloc[-i]['Volume_Ratio'] for i in range(1, min(6, len(df)))]
        avg_vol_ratio = sum(recent_vol_ratio) / len(recent_vol_ratio)

        if avg_vol_ratio > 1.5 and latest['close'] > df.iloc[-2]['close']:
            # 成交量放大且价格上涨 - "成交量能量阈值突破"
            volume_score += 20
        elif avg_vol_ratio > 1.2 and latest['close'] > df.iloc[-2]['close']:
            # 成交量和价格同步上涨
            volume_score += 15
        elif avg_vol_ratio < 0.8 and latest['close'] < df.iloc[-2]['close']:
            # 成交量和价格同步下跌，可能是健康回调
            volume_score += 10
        elif avg_vol_ratio > 1.2 and latest['close'] < df.iloc[-2]['close']:
            # 成交量增加但价格下跌，可能存在较大卖压
            volume_score += 0
        else:
            # 其他情况
            volume_score += 8

        return volume_score

    def _calculate_momentum_score(self, latest: pd.Series) -> int:
        """计算动量评分（最高10分）"""
        roc = latest['ROC']

        if roc > 5:
            # 强劲上升动量
            return 10
        elif 2 <= roc <= 5:
            # 适度上升动量
            return 8
        elif 0 <= roc < 2:
            # 弱上升动量
            return 5
        elif -2 <= roc < 0:
            # 弱下降动量
            return 3
        else:
            # 强劲下降动量
            return 0

    def _get_grade(self, score: float) -> str:
        """根据评分获取评级等级"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B+'
        elif score >= 60:
            return 'B'
        elif score >= 50:
            return 'C+'
        elif score >= 40:
            return 'C'
        elif score >= 30:
            return 'D+'
        elif score >= 20:
            return 'D'
        else:
            return 'F'

    def _get_scoring_details(self, latest: pd.Series, df: pd.DataFrame, weights: Dict) -> Dict[str, Any]:
        """获取详细的评分依据"""
        details = {
            'trend': {
                'indicators': {
                    'MA5': round(latest['MA5'], 2),
                    'MA20': round(latest['MA20'], 2),
                    'MA60': round(latest['MA60'], 2),
                    'current_price': round(latest['close'], 2)
                },
                'logic': self._get_trend_logic(latest),
                'weight_percent': round(weights['trend'] * 100, 1)
            },
            'technical': {
                'indicators': {
                    'RSI': round(latest['RSI'], 1),
                    'MACD': round(latest['MACD'], 4),
                    'Signal': round(latest['Signal'], 4),
                    'MACD_hist': round(latest['MACD_hist'], 4),
                    'BB_position': self._get_bb_position(latest)
                },
                'logic': self._get_technical_logic(latest, df),
                'weight_percent': round(weights['technical'] * 100, 1)
            },
            'volume': {
                'indicators': {
                    'current_volume_ratio': round(latest['Volume_Ratio'], 2),
                    'avg_volume_ratio': self._get_avg_volume_ratio(df),
                    'price_change': round(latest['close'] - df.iloc[-2]['close'], 2) if len(df) > 1 else 0
                },
                'logic': self._get_volume_logic(latest, df),
                'weight_percent': round(weights['volume'] * 100, 1)
            },
            'volatility': {
                'indicators': {
                    'volatility': round(latest['Volatility'], 2)
                },
                'logic': self._get_volatility_logic(latest),
                'weight_percent': round(weights['volatility'] * 100, 1)
            },
            'momentum': {
                'indicators': {
                    'ROC': round(latest['ROC'], 2)
                },
                'logic': self._get_momentum_logic(latest),
                'weight_percent': round(weights['momentum'] * 100, 1)
            }
        }
        return details

    def _get_trend_logic(self, latest: pd.Series) -> List[str]:
        """生成趋势评分逻辑说明"""
        logic = []

        if latest['MA5'] > latest['MA20'] and latest['MA20'] > latest['MA60']:
            logic.append("✓ 完美多头排列(MA5>MA20>MA60): +15分")
        elif latest['MA5'] > latest['MA20']:
            logic.append("✓ 短期上升趋势(MA5>MA20): +10分")
        elif latest['MA20'] > latest['MA60']:
            logic.append("✓ 中期上升趋势(MA20>MA60): +5分")

        if latest['close'] > latest['MA5']:
            logic.append("✓ 价格高于MA5: +5分")
        if latest['close'] > latest['MA20']:
            logic.append("✓ 价格高于MA20: +5分")
        if latest['close'] > latest['MA60']:
            logic.append("✓ 价格高于MA60: +5分")

        return logic

    def _get_technical_logic(self, latest: pd.Series, df: pd.DataFrame) -> List[str]:
        """生成技术指标评分逻辑说明"""
        logic = []

        # RSI分析
        rsi = latest['RSI']
        if 40 <= rsi <= 60:
            logic.append(f"✓ RSI中性区域({rsi:.1f}): +7分")
        elif 30 <= rsi < 40 or 60 < rsi <= 70:
            logic.append(f"✓ RSI阈值区域({rsi:.1f}): +10分")
        elif rsi < 30:
            logic.append(f"✓ RSI超卖区域({rsi:.1f}): +8分")
        elif rsi > 70:
            logic.append(f"⚠ RSI超买区域({rsi:.1f}): +2分")

        # MACD分析
        if latest['MACD'] > latest['Signal'] and latest['MACD_hist'] > 0:
            logic.append("✓ MACD金叉且柱状图为正: +10分")
        elif latest['MACD'] > latest['Signal']:
            logic.append("✓ MACD金叉: +8分")
        elif latest['MACD'] < latest['Signal'] and latest['MACD_hist'] < 0:
            logic.append("✗ MACD死叉且柱状图为负: +0分")
        elif len(df) > 1 and latest['MACD_hist'] > df.iloc[-2]['MACD_hist']:
            logic.append("✓ MACD柱状图增长: +5分")

        # 布林带分析
        bb_position = self._get_bb_position(latest)
        if 0.3 <= bb_position <= 0.7:
            logic.append(f"✓ 布林带中间区域({bb_position:.2f}): +3分")
        elif bb_position < 0.2:
            logic.append(f"✓ 接近布林带下轨({bb_position:.2f}): +5分")
        elif bb_position > 0.8:
            logic.append(f"⚠ 接近布林带上轨({bb_position:.2f}): +1分")

        return logic

    def _get_volume_logic(self, latest: pd.Series, df: pd.DataFrame) -> List[str]:
        """生成成交量评分逻辑说明"""
        logic = []

        avg_vol_ratio = self._get_avg_volume_ratio(df)
        price_change = latest['close'] - df.iloc[-2]['close'] if len(df) > 1 else 0

        if avg_vol_ratio > 1.5 and price_change > 0:
            logic.append(f"✓ 成交量大幅放大({avg_vol_ratio:.2f})且价格上涨: +20分")
        elif avg_vol_ratio > 1.2 and price_change > 0:
            logic.append(f"✓ 成交量放大({avg_vol_ratio:.2f})且价格上涨: +15分")
        elif avg_vol_ratio < 0.8 and price_change < 0:
            logic.append(f"✓ 成交量缩减({avg_vol_ratio:.2f})且价格下跌: +10分")
        elif avg_vol_ratio > 1.2 and price_change < 0:
            logic.append(f"✗ 成交量放大({avg_vol_ratio:.2f})但价格下跌: +0分")
        else:
            logic.append(f"○ 量价关系一般({avg_vol_ratio:.2f}): +8分")

        return logic

    def _get_volatility_logic(self, latest: pd.Series) -> List[str]:
        """生成波动率评分逻辑说明"""
        logic = []
        volatility = latest['Volatility']

        if 1.0 <= volatility <= 2.5:
            logic.append(f"✓ 最佳波动率范围({volatility:.2f}%): +15分")
        elif 2.5 < volatility <= 4.0:
            logic.append(f"○ 较高波动率({volatility:.2f}%): +10分")
        elif volatility < 1.0:
            logic.append(f"⚠ 波动率过低({volatility:.2f}%): +5分")
        else:
            logic.append(f"✗ 波动率过高({volatility:.2f}%): +0分")

        return logic

    def _get_momentum_logic(self, latest: pd.Series) -> List[str]:
        """生成动量评分逻辑说明"""
        logic = []
        roc = latest['ROC']

        if roc > 5:
            logic.append(f"✓ 强劲上升动量({roc:.2f}%): +10分")
        elif 2 <= roc <= 5:
            logic.append(f"✓ 适度上升动量({roc:.2f}%): +8分")
        elif 0 <= roc < 2:
            logic.append(f"○ 弱上升动量({roc:.2f}%): +5分")
        elif -2 <= roc < 0:
            logic.append(f"○ 弱下降动量({roc:.2f}%): +3分")
        else:
            logic.append(f"✗ 强劲下降动量({roc:.2f}%): +0分")

        return logic

    def _get_bb_position(self, latest: pd.Series) -> float:
        """计算布林带位置"""
        if latest['BB_upper'] != latest['BB_lower']:
            return (latest['close'] - latest['BB_lower']) / (latest['BB_upper'] - latest['BB_lower'])
        return 0.5  # 默认中间位置

    def _get_avg_volume_ratio(self, df: pd.DataFrame) -> float:
        """计算平均成交量比率"""
        recent_vol_ratio = []
        for i in range(1, min(6, len(df))):
            if i < len(df):
                recent_vol_ratio.append(df.iloc[-i]['Volume_Ratio'])

        if recent_vol_ratio:
            return sum(recent_vol_ratio) / len(recent_vol_ratio)
        return 1.0  # 默认值

    def load_stock_data(self, file_path: str) -> pd.DataFrame:
        """加载股票数据"""
        try:
            # 支持多种文件格式
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                df = pd.read_excel(file_path)
            else:
                raise ValueError("不支持的文件格式，请使用CSV或Excel文件")

            self.logger.info(f"成功加载数据文件: {file_path}, 共{len(df)}行数据")
            return df

        except Exception as e:
            self.logger.error(f"加载数据文件失败: {e}")
            raise

    def prepare_stock_data(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """准备单只股票的数据 - 仅使用真实数据"""
        try:
            # 标准化列名映射
            column_mapping = {
                'secID': 'stock_code',
                '名称': 'stock_name',
                'closePrice': 'close',
                'openPrice': 'open',
                'highPrice': 'high',
                'lowPrice': 'low',
                'volume': 'volume',
                'turnover': 'turnover'
            }

            # 重命名列
            df_renamed = df.rename(columns=column_mapping)
            current_price = float(df_renamed.iloc[0]['close'])

            # 获取真实历史数据
            self.logger.info(f"获取股票 {stock_code} 的真实历史数据...")

            # 设置数据时间范围（获取足够的数据用于技术指标计算）
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')

            # 确定市场类型
            market_type = 'A'  # 默认A股
            if stock_code.endswith('.HK'):
                market_type = 'HK'
            elif stock_code.endswith('.US'):
                market_type = 'US'

            real_data = self.data_service.get_stock_price_history(
                stock_code, market_type, start_date, end_date
            )

            # 检查数据获取是否成功
            if real_data is None:
                # 数据获取失败，返回None让调用者处理
                return None

            self.logger.info(f"✅ 成功获取股票 {stock_code} 的真实历史数据，共 {len(real_data)} 条记录")

            # 确保最新价格与输入价格一致
            if len(real_data) > 0:
                # 调整最新价格以匹配输入数据
                price_adjustment = current_price / real_data.iloc[-1]['close']
                for col in ['open', 'high', 'low', 'close']:
                    real_data[col] = real_data[col] * price_adjustment

            # 添加股票信息
            real_data['stock_code'] = stock_code
            real_data['stock_name'] = df_renamed.iloc[0].get('stock_name', '')

            return real_data

        except Exception as e:
            self.logger.error(f"准备股票数据失败 {stock_code}: {e}")
            # 返回None而不是抛出异常
            return None



    def score_single_stock(self, stock_data: pd.DataFrame, stock_code: str, stock_name: str = "") -> Dict[str, Any]:
        """对单只股票进行评分"""
        try:
            # 准备数据（仅使用真实数据）
            prepared_data = self.prepare_stock_data(stock_data, stock_code)

            # 检查数据准备是否成功
            if prepared_data is None:
                # 创建跳过记录
                return {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'current_price': float(stock_data.iloc[0].get('closePrice', 0)),
                    'skipped': True,
                    'skip_reason': "数据获取失败",
                    'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

            # 计算技术指标
            data_with_indicators = self.calculate_technical_indicators(prepared_data)

            # 计算评分
            score_result = self.calculate_score(data_with_indicators)

            # 添加股票基本信息
            result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'current_price': round(data_with_indicators.iloc[-1]['close'], 2),
                'data_points': len(data_with_indicators),
                'data_source': "真实数据",
                'skipped': False,
                **score_result,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            self.logger.info(f"股票 {stock_code}({stock_name}) 评分完成: {score_result['total_score']}分 ({score_result['grade']}) [真实数据]")
            return result

        except Exception as e:
            self.logger.error(f"股票 {stock_code} 评分失败: {e}")
            # 创建跳过记录而不是抛出异常
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'current_price': float(stock_data.iloc[0].get('closePrice', 0)),
                'skipped': True,
                'skip_reason': f"评分过程失败: {str(e)}",
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def batch_score_stocks(self, input_file: str, output_file: str = None) -> str:
        """批量评分股票"""
        try:
            # 加载数据
            df = self.load_stock_data(input_file)

            # 检查必要的列
            if 'secID' not in df.columns:
                raise ValueError("数据文件必须包含 'secID' 列")

            results = []
            total_stocks = len(df)

            self.logger.info(f"开始批量评分，共 {total_stocks} 只股票")

            for idx, row in df.iterrows():
                stock_code = row['secID']
                stock_name = row.get('名称', '')

                try:
                    # 为每只股票创建单行数据框进行评分
                    single_stock_df = pd.DataFrame([row])
                    result = self.score_single_stock(single_stock_df, stock_code, stock_name)
                    results.append(result)

                    # 显示进度
                    if (idx + 1) % 10 == 0 or idx + 1 == total_stocks:
                        self.logger.info(f"进度: {idx + 1}/{total_stocks} ({(idx + 1)/total_stocks*100:.1f}%)")

                except Exception as e:
                    # 显示错误信息并直接退出程序
                    self.logger.error(f"股票 {stock_code} 处理失败: {e}")
                    print(f"❌ 股票 {stock_code} 处理失败: {str(e)}")
                    print("程序终止执行")
                    # 确保程序立即退出
                    os._exit(1)

            # 生成输出文件名
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"stock_scores_{timestamp}.csv"

            # 保存结果
            self.save_results(results, output_file)

            # 统计信息
            successful_scores = [r for r in results if 'total_score' in r]
            failed_scores = [r for r in results if 'error' in r]

            self.logger.info(f"批量评分完成！")
            self.logger.info(f"成功评分: {len(successful_scores)} 只")
            self.logger.info(f"失败: {len(failed_scores)} 只")
            self.logger.info(f"结果已保存到: {output_file}")

            return output_file

        except Exception as e:
            self.logger.error(f"批量评分失败: {e}")
            raise

    def save_results(self, results: List[Dict], output_file: str):
        """保存评分结果到CSV文件"""
        try:
            # 准备输出数据
            output_data = []

            for result in results:
                if result.get('skipped', False):
                    # 跳过的股票记录
                    output_data.append({
                        '股票代码': result['stock_code'],
                        '股票名称': result['stock_name'],
                        '当前价格': result.get('current_price', ''),
                        '总评分': '',
                        '评级等级': '',
                        '趋势评分': '',
                        '技术指标评分': '',
                        '成交量评分': '',
                        '波动率评分': '',
                        '动量评分': '',
                        '数据点数': '',
                        '数据源': '',
                        '状态': '已跳过',
                        '跳过原因': result.get('skip_reason', '未知原因'),
                        '分析时间': result['analysis_time']
                    })
                else:
                    # 正常评分记录
                    dimension_scores = result.get('dimension_scores', {})
                    output_data.append({
                        '股票代码': result['stock_code'],
                        '股票名称': result['stock_name'],
                        '当前价格': result.get('current_price', ''),
                        '总评分': result.get('total_score', ''),
                        '评级等级': result.get('grade', ''),
                        '趋势评分': dimension_scores.get('trend', ''),
                        '技术指标评分': dimension_scores.get('technical', ''),
                        '成交量评分': dimension_scores.get('volume', ''),
                        '波动率评分': dimension_scores.get('volatility', ''),
                        '动量评分': dimension_scores.get('momentum', ''),
                        '数据点数': result.get('data_points', ''),
                        '数据源': '真实数据',
                        '状态': '成功评分',
                        '跳过原因': '',
                        '分析时间': result['analysis_time']
                    })

            # 保存到CSV文件
            df_output = pd.DataFrame(output_data)
            df_output.to_csv(output_file, index=False, encoding='utf-8-sig')

            self.logger.info(f"结果已保存到: {output_file}")

        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            raise

    def save_detailed_results(self, results: List[Dict], output_file: str):
        """保存详细评分结果（包含评分依据）"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            with open(output_file, 'w', encoding='utf-8-sig') as f:
                f.write(f"股票评分详细报告\n")
                f.write(f"生成时间: {timestamp}\n")
                f.write(f"评分算法版本: 1.0.0\n")
                f.write("="*80 + "\n\n")

                for result in results:
                    f.write(f"股票代码: {result['stock_code']}\n")
                    f.write(f"股票名称: {result['stock_name']}\n")
                    f.write(f"当前价格: {result.get('current_price', 'N/A')}\n")

                    if result.get('skipped', False):
                        f.write(f"状态: 已跳过\n")
                        f.write(f"跳过原因: {result.get('skip_reason', '未知原因')}\n")
                        f.write(f"分析时间: {result['analysis_time']}\n")
                        f.write("-"*60 + "\n\n")
                        continue
                    f.write(f"总评分: {result.get('total_score', 'N/A')} 分\n")
                    f.write(f"评级等级: {result.get('grade', 'N/A')}\n")
                    f.write(f"数据点数: {result.get('data_points', 'N/A')}\n\n")

                    # 各维度评分
                    dimension_scores = result.get('dimension_scores', {})
                    f.write("各维度评分:\n")
                    f.write(f"  趋势分析: {dimension_scores.get('trend', 'N/A')}/30 分\n")
                    f.write(f"  技术指标: {dimension_scores.get('technical', 'N/A')}/25 分\n")
                    f.write(f"  成交量分析: {dimension_scores.get('volume', 'N/A')}/20 分\n")
                    f.write(f"  波动率评估: {dimension_scores.get('volatility', 'N/A')}/15 分\n")
                    f.write(f"  动量分析: {dimension_scores.get('momentum', 'N/A')}/10 分\n\n")

                    # 权重信息
                    weights = result.get('weights', {})
                    f.write("权重配置:\n")
                    for dim, weight in weights.items():
                        f.write(f"  {dim}: {weight*100:.1f}%\n")
                    f.write("\n")

                    # 详细评分依据
                    scoring_details = result.get('scoring_details', {})
                    if scoring_details:
                        f.write("评分依据详情:\n")
                        for dim_name, details in scoring_details.items():
                            f.write(f"\n  {dim_name.upper()} 维度:\n")

                            # 指标值
                            indicators = details.get('indicators', {})
                            f.write(f"    关键指标: {indicators}\n")

                            # 评分逻辑
                            logic = details.get('logic', [])
                            f.write(f"    评分逻辑:\n")
                            for item in logic:
                                f.write(f"      {item}\n")

                    f.write("-"*60 + "\n\n")

            self.logger.info(f"详细结果已保存到: {output_file}")

        except Exception as e:
            self.logger.error(f"保存详细结果失败: {e}")
            raise


def run_csv_analysis():
    """读取list3.csv文件并进行股票评分分析"""
    # 移除全局try-except块，让错误直接传播
    print("=" * 60)
    print("独立股票评分程序 - 纯真实数据版")
    print("基于原系统算法，使用与网页版相同的数据源")
    print("📊 数据源: AKShare真实市场数据")
    print("=" * 60)

    # 检查list3.csv文件是否存在
    csv_file = "list3.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 错误: 未找到文件 '{csv_file}'")
        print("请确保当前目录下存在 list3.csv 文件")
        print("文件应包含以下列: secID, 名称, closePrice")
        print("\n示例文件格式:")
        print("secID,名称,closePrice")
        print("002636.XSHE,金安国纪,15.09")
        print("600475.XSHG,华光环能,16.73")
        return None

    # 创建评分器
    scorer = StockScorer()

    # 读取CSV文件
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"✅ 成功读取文件: {csv_file}")
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(csv_file, encoding='gbk')
            print(f"✅ 成功读取文件: {csv_file} (GBK编码)")
        except Exception as e:
            print(f"❌ 文件编码错误: {e}")
            return None
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

    # 验证必要的列
    required_columns = ['secID', 'closePrice']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 错误: 文件缺少必要的列: {missing_columns}")
        print(f"当前文件包含的列: {list(df.columns)}")
        print("请确保文件包含: secID, closePrice 列")
        return None

    print(f"📊 加载股票数据，共 {len(df)} 只股票")

    # 显示文件信息
    if '名称' in df.columns:
        print("包含股票名称信息")
    else:
        print("未包含股票名称，将使用股票代码")
        df['名称'] = df['secID']  # 如果没有名称列，使用代码作为名称

    results = []

    print("\n开始评分...")
    print("-" * 60)

    for i, row in df.iterrows():
        stock_code = row['secID']
        stock_name = row.get('名称', stock_code)

        # 为每只股票创建单行数据框进行评分
        single_stock_df = pd.DataFrame([row])
        result = scorer.score_single_stock(single_stock_df, stock_code, stock_name)
        results.append(result)

        # 显示进度和结果
        if result.get('skipped', False):
            # 显示跳过信息
            print(f"{i+1:2d}. {stock_code} {stock_name:8s} | ⚠️ 已跳过: {result.get('skip_reason', '未知原因')} | 价格: {result.get('current_price', 0):.2f}")
        else:
            # 显示评分结果
            print(f"{i+1:2d}. {stock_code} {stock_name:8s} | 评分: {result['total_score']:3d} | 等级: {result['grade']:2s} | 价格: {result['current_price']:6.2f} | 📊")  # 使用sys.exit替代os._exit

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results_{timestamp}.csv"
    scorer.save_results(results, output_file)

    # 统计信息（添加安全检查）
    if not results:
        print("\n❌ 没有处理的股票")
        return None

    # 区分成功评分和跳过的股票
    successful_results = [r for r in results if not r.get('skipped', False)]
    skipped_results = [r for r in results if r.get('skipped', False)]

    # 统计成功评分的股票
    if successful_results:
        try:
            scores = [r['total_score'] for r in successful_results]
            grades = [r['grade'] for r in successful_results]

            print("\n" + "=" * 60)
            print("📈 评分统计:")
            print(f"  ✅ 成功评分: {len(successful_results)} 只")
            print(f"  ⚠️ 跳过股票: {len(skipped_results)} 只")
            print(f"  🔢 总处理数: {len(results)} 只")
            print(f"  📊 平均评分: {np.mean(scores):.1f}")
            print(f"  📈 评分范围: {min(scores)} - {max(scores)}")

            # 评级分布
            grade_counts = {}
            for grade in grades:
                grade_counts[grade] = grade_counts.get(grade, 0) + 1

            print("  🏆 评级分布:")
            for grade in ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D', 'F']:
                if grade in grade_counts:
                    print(f"    {grade}: {grade_counts[grade]} 只")

            # 显示跳过的股票
            if skipped_results:
                print("\n⚠️ 跳过的股票:")
                for i, r in enumerate(skipped_results):
                    print(f"  {i+1}. {r['stock_code']} {r['stock_name']}: {r.get('skip_reason', '未知原因')}")
        except KeyError as e:
            print(f"\n❌ 统计时出现错误: 结果中缺少必要字段 {e}")
            return None
    else:
        print("\n❌ 没有成功评分的股票，所有股票都被跳过")
        print(f"  ⚠️ 跳过股票: {len(skipped_results)} 只")

        # 显示跳过的股票
        print("\n⚠️ 跳过的股票:")
        for i, r in enumerate(skipped_results):
            print(f"  {i+1}. {r['stock_code']} {r['stock_name']}: {r.get('skip_reason', '未知原因')}")

    print(f"\n💾 结果已保存到: {output_file}")
    print("=" * 60)

    return output_file


def main():
    """主程序入口"""
    import argparse

    # 如果没有命令行参数，直接运行CSV分析
    if len(sys.argv) == 1:
        run_csv_analysis()
        return

    parser = argparse.ArgumentParser(description='独立股票评分程序')
    parser.add_argument('input_file', nargs='?', help='输入CSV文件路径（可选，不提供则读取list3.csv）')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-c', '--config', help='配置文件路径')
    parser.add_argument('--detailed', action='store_true', help='生成详细报告')

    args = parser.parse_args()

    try:
        # 如果没有输入文件，运行默认CSV分析
        if not args.input_file:
            run_csv_analysis()
            return

        # 创建评分器
        scorer = StockScorer(args.config)

        # 执行批量评分
        output_file = scorer.batch_score_stocks(args.input_file, args.output)

        # 如果需要详细报告
        if args.detailed:
            # 重新加载结果进行详细报告生成
            df = scorer.load_stock_data(args.input_file)
            results = []

            for i, (_, row) in enumerate(df.iterrows()):
                stock_code = row['secID']
                stock_name = row.get('名称', '')
                single_stock_df = pd.DataFrame([row])

                # 添加进度显示
                print(f"评分进度: {i+1}/{len(df)} - {stock_code} {stock_name}")

                try:
                    # 评分，失败时会抛出异常
                    result = scorer.score_single_stock(single_stock_df, stock_code, stock_name)
                    results.append(result)
                except Exception as e:
                    # 显示错误信息并直接退出程序
                    print(f"❌ 股票 {stock_code} 处理失败: {str(e)}")
                    print("程序终止执行")
                    # 确保程序立即退出
                    os._exit(1)

            detailed_file = output_file.replace('.csv', '_detailed.txt')
            scorer.save_detailed_results(results, detailed_file)

        print(f"\n✅ 评分完成！结果已保存到: {output_file}")
        if args.detailed:
            print(f"✅ 详细报告已保存到: {detailed_file}")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
