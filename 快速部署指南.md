# 🚀 GitHub Actions 自动部署到 Hugging Face Spaces - 快速指南

## 📋 5分钟快速部署

### 步骤 1：创建 Hugging Face Space（2分钟）

1. 访问 [https://huggingface.co](https://huggingface.co) 并登录
2. 点击右上角头像 → "New Space"
3. 填写信息：
   - **Space name**: `stock-analysis-system`
   - **SDK**: 选择 `Gradio`
   - **Hardware**: `CPU basic`（免费）
   - **Visibility**: `Public`
4. 点击 "Create Space"

### 步骤 2：获取访问令牌（1分钟）

1. 点击头像 → "Settings" → "Access Tokens"
2. 点击 "New token"
3. 设置：
   - **Name**: `github-actions-deploy`
   - **Type**: `Write`
4. 点击 "Generate a token"
5. **立即复制并保存令牌**（只显示一次！）

### 步骤 3：配置 GitHub Secrets（1分钟）

1. 打开您的 GitHub 仓库
2. 进入 "Settings" → "Secrets and variables" → "Actions"
3. 点击 "New repository secret" 添加：

   **HF_TOKEN**
   ```
   Name: HF_TOKEN
   Secret: 粘贴您的 Hugging Face 令牌
   ```

   **HF_SPACE**
   ```
   Name: HF_SPACE
   Secret: 您的用户名/space名称（例如：johndoe/stock-analysis-system）
   ```

### 步骤 4：添加工作流文件（1分钟）

在项目根目录创建 `.github/workflows/deploy.yml` 文件，复制以下内容：

```yaml
name: Deploy to Hugging Face Spaces

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

env:
  HF_HUB_ENABLE_HF_TRANSFER: 1

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        lfs: true

    - name: 设置 Python 环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install huggingface_hub

    - name: 验证部署文件
      run: |
        echo "🔍 检查必需文件..."
        if [ ! -f "app.py" ]; then
          echo "❌ 错误: app.py 文件不存在"
          exit 1
        fi
        if [ ! -f "requirements.txt" ]; then
          echo "❌ 错误: requirements.txt 文件不存在"
          exit 1
        fi
        echo "✅ 文件验证完成"

    - name: 部署到 Hugging Face Spaces
      env:
        HF_TOKEN: ${{ secrets.HF_TOKEN }}
        HF_SPACE: ${{ secrets.HF_SPACE }}
      run: |
        echo "🚀 开始部署到 Hugging Face Spaces..."
        
        python -c "
import os
from huggingface_hub import HfApi, upload_folder
import sys

try:
    api = HfApi(token=os.environ['HF_TOKEN'])
    space_id = os.environ['HF_SPACE']
    
    print(f'📤 上传到 Space: {space_id}')
    
    ignore_patterns = [
        '.git*', '__pycache__', '*.pyc', '.env.example',
        'logs/', 'data/news/', '.pytest_cache', 'tests/',
        'docs/', 'images/', '*.log', 'fly.toml', 'render.yaml'
    ]
    
    upload_folder(
        folder_path='.',
        repo_id=space_id,
        repo_type='space',
        token=os.environ['HF_TOKEN'],
        ignore_patterns=ignore_patterns,
        commit_message=f'Auto-deploy from GitHub Actions'
    )
    
    print('✅ 部署完成!')
    print(f'🌐 访问地址: https://huggingface.co/spaces/{space_id}')
    
except Exception as e:
    print(f'❌ 部署失败: {e}')
    sys.exit(1)
        "

    - name: 部署成功
      if: success()
      run: |
        echo "🎉 部署成功完成!"
        echo "🌐 您的应用地址: https://huggingface.co/spaces/${{ secrets.HF_SPACE }}"
        echo "⏰ 请等待 2-5 分钟让应用完成构建"
```

### 步骤 5：触发部署（立即）

推送代码到 main 分支：
```bash
git add .
git commit -m "添加 GitHub Actions 自动部署"
git push origin main
```

或者手动触发：
1. 进入 GitHub 仓库 → "Actions"
2. 选择 "Deploy to Hugging Face Spaces"
3. 点击 "Run workflow"

---

## ✅ 检查部署状态

### GitHub Actions
- 进入仓库 → "Actions" → 查看工作流状态
- 绿色勾号 = 成功，红色叉号 = 失败

### Hugging Face Spaces
- 访问 `https://huggingface.co/spaces/您的用户名/space名称`
- 查看构建状态和日志

---

## 🔧 可选配置

### 添加 API 密钥（如果需要 AI 功能）

在 GitHub Secrets 中添加：
```
OPENAI_API_KEY = 您的OpenAI密钥
OPENAI_API_URL = https://api.openai.com/v1
OPENAI_API_MODEL = gpt-4o
```

然后在 Hugging Face Space 设置中也添加相同的环境变量。

### 自定义 README

创建 `README_HF.md` 文件：
```markdown
---
title: 智能分析系统（股票）
emoji: 📈
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: 4.44.0
app_file: app.py
pinned: false
license: mit
---

# 智能分析系统（股票）

基于 Python 和 Flask 的智能股票分析系统。

## 功能特点
- 多维度股票分析
- AI 增强分析
- 实时数据获取
- 可视化图表

## 免责声明
仅供学习研究使用，不构成投资建议。
```

---

## 🚨 常见问题

**Q: 部署失败怎么办？**
A: 检查 GitHub Actions 日志，确认 Secrets 设置正确。

**Q: Space 一直显示 Building？**
A: 查看 Hugging Face Space 的 Logs 选项卡，检查构建错误。

**Q: 如何更新应用？**
A: 推送新代码到 main 分支即可自动重新部署。

---

## 📞 获取帮助

- 详细教程：查看 `GitHub_Actions_HF_部署教程.md`
- GitHub Issues：在项目仓库创建问题
- 社区支持：[Hugging Face 论坛](https://discuss.huggingface.co/)

---

**🎉 完成！您的股票分析系统现在会自动部署到 Hugging Face Spaces！**
