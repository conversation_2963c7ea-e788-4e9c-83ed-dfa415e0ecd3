
{% extends "layout.html" %}

{% block title %}投资组合 - 智能分析系统{% endblock %}

{% block head %}
<style>
    /* Portfolio-specific Material Design 3 enhancements */
    .md3-portfolio-container {
        padding: 32px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .md3-portfolio-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 32px;
        padding: 24px 0;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .md3-button-group {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    /* 表格排序功能样式 */
    .sortable-header {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px 6px;
        border-radius: var(--md-sys-shape-corner-small);
        width: 100%;
        min-height: 40px;
        text-align: center;
    }

    .sortable-header:hover {
        background-color: var(--md-sys-color-surface-container-highest);
        color: var(--md-sys-color-primary);
    }

    .sortable-header span {
        font-weight: 500;
        white-space: nowrap;
    }

    .sort-indicator {
        font-size: 16px;
        color: var(--md-sys-color-on-surface-variant);
        transition: all 0.3s ease;
        opacity: 0.6;
        margin-left: 2px;
        flex-shrink: 0;
    }

    .sortable-header:hover .sort-indicator {
        opacity: 1;
        color: var(--md-sys-color-primary);
    }

    .sortable-header[data-sort="asc"] .sort-indicator {
        color: var(--md-sys-color-primary);
        opacity: 1;
    }

    .sortable-header[data-sort="desc"] .sort-indicator {
        color: var(--md-sys-color-primary);
        opacity: 1;
    }

    .sortable-header[data-sort="none"] .sort-indicator {
        opacity: 0.6;
    }

    /* 响应式设计 - 小屏幕优化 */
    @media (max-width: 768px) {
        .sortable-header {
            gap: 4px;
            padding: 6px 4px;
            font-size: 12px;
        }

        .sortable-header span {
            font-size: 12px;
        }

        .sort-indicator {
            font-size: 14px;
        }
    }

    .md3-portfolio-title {
        font-family: var(--md-sys-typescale-headline-large-font);
        font-size: var(--md-sys-typescale-headline-large-size);
        font-weight: var(--md-sys-typescale-headline-large-weight);
        color: var(--md-sys-color-on-surface);
        margin: 0;
        display: flex;
        align-items: center;
    }

    .md3-portfolio-title i {
        margin-right: 16px;
        color: var(--md-sys-color-primary);
        font-size: 32px;
    }

    .md3-empty-state {
        text-align: center;
        padding: 64px 32px;
        background-color: var(--md-sys-color-surface-container);
        border-radius: var(--md-sys-shape-corner-large);
        border: 1px solid var(--md-sys-color-outline-variant);
    }

    .md3-empty-state i {
        font-size: 64px;
        color: var(--md-sys-color-outline);
        margin-bottom: 24px;
    }

    .md3-empty-state h3 {
        font-family: var(--md-sys-typescale-title-large-font);
        font-size: var(--md-sys-typescale-title-large-size);
        font-weight: var(--md-sys-typescale-title-large-weight);
        color: var(--md-sys-color-on-surface);
        margin-bottom: 16px;
    }

    .md3-empty-state p {
        font-family: var(--md-sys-typescale-body-large-font);
        font-size: var(--md-sys-typescale-body-large-size);
        color: var(--md-sys-color-on-surface-variant);
        margin-bottom: 32px;
    }

    /* 多投资组合标签页样式 */
    .md3-portfolio-tabs {
        margin: 24px 0 32px 0;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .md3-tabs {
        display: flex;
        align-items: center;
        gap: 4px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .md3-tabs::-webkit-scrollbar {
        display: none;
    }

    .md3-tab {
        position: relative;
        padding: 12px 24px;
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        font-weight: var(--md-sys-typescale-title-small-weight);
        color: var(--md-sys-color-on-surface-variant);
        background: none;
        border: none;
        border-radius: var(--md-sys-shape-corner-small) var(--md-sys-shape-corner-small) 0 0;
        cursor: pointer;
        transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
        white-space: nowrap;
        min-width: 120px;
        text-align: center;
        user-select: none;
    }

    .md3-tab:hover {
        background-color: var(--md-sys-color-surface-container-low);
        color: var(--md-sys-color-on-surface);
    }

    .md3-tab.active {
        color: var(--md-sys-color-primary);
        background-color: var(--md-sys-color-surface-container);
    }

    .md3-tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--md-sys-color-primary);
        border-radius: 3px 3px 0 0;
    }

    .md3-tab-add-btn {
        padding: 8px 12px;
        margin-left: 8px;
        font-size: 18px;
        color: var(--md-sys-color-primary);
        background: none;
        border: 2px dashed var(--md-sys-color-outline-variant);
        border-radius: var(--md-sys-shape-corner-small);
        cursor: pointer;
        transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;
    }

    .md3-tab-add-btn:hover {
        border-color: var(--md-sys-color-primary);
        background-color: var(--md-sys-color-primary-container);
    }

    .md3-tab-delete-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: var(--md-sys-color-error);
        color: var(--md-sys-color-on-error);
        border: none;
        font-size: 12px;
        cursor: pointer;
        display: none;
        align-items: center;
        justify-content: center;
        transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
    }

    .md3-tab:hover .md3-tab-delete-btn {
        display: flex;
    }

    .md3-tab-delete-btn:hover {
        background-color: var(--md-sys-color-error-container);
        color: var(--md-sys-color-on-error-container);
    }

    /* 投资组合切换动画 */
    .portfolio-switching {
        opacity: 0.6;
        pointer-events: none;
        transition: opacity var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
    }

    .portfolio-content-fade {
        animation: fadeInUp 0.3s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 加载状态指示器 */
    .portfolio-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: var(--md-sys-color-on-surface-variant);
    }

    .portfolio-loading i {
        margin-right: 8px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="md3-portfolio-container">
    <div id="alerts-container"></div>

    <!-- Enhanced Material Design 3 Portfolio Header -->
    <div class="md3-portfolio-header">
        <h1 class="md3-portfolio-title">
            <i class="material-icons">account_balance_wallet</i>
            我的投资组合
        </h1>
        <div class="md3-button-group">
            <button class="md3-button md3-button-filled" data-bs-toggle="modal" data-bs-target="#addStockModal">
                <i class="material-icons">add</i> 添加股票
            </button>
            <button class="md3-button md3-button-outlined" data-bs-toggle="modal" data-bs-target="#importCsvModal">
                <i class="material-icons">upload_file</i> 批量导入
            </button>
            <button class="md3-button md3-button-outlined" onclick="exportPortfolioToCSV()" title="导出投资组合数据为CSV文件">
                <i class="material-icons">file_download</i> 导出CSV
            </button>
        </div>
    </div>

    <!-- 多投资组合标签页 -->
    <div class="md3-portfolio-tabs">
        <div class="md3-tabs" id="portfolio-tabs">
            <!-- 标签页将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- Enhanced Empty State -->
    <div id="portfolio-empty" class="md3-empty-state">
        <i class="material-icons">folder_open</i>
        <h3>投资组合为空</h3>
        <p>开始构建您的专业投资组合，获取智能分析和建议</p>
        <button class="md3-button md3-button-filled" data-bs-toggle="modal" data-bs-target="#addStockModal">
            <i class="material-icons">add</i> 添加第一只股票
        </button>
    </div>

    <!-- Enhanced Portfolio Content -->
    <div id="portfolio-content" style="display: none;">
        <div class="md3-card">
            <div class="md3-card-header">
                <h2 class="md3-card-title">
                    <i class="material-icons">list</i>
                    持仓明细 (支持排序)
                </h2>
            </div>
            <div class="md3-card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="md3-table">
                        <thead>
                            <tr>
                                <th>股票代码</th>
                                <th>股票名称</th>
                                <th>所属行业</th>
                                <th>持仓比例</th>
                                <th>当前价格</th>
                                <th>
                                    <div class="sortable-header" onclick="sortTable('price_change')" data-sort="none">
                                        <span>今日涨跌</span>
                                        <i class="material-icons sort-indicator" id="sort-price_change">unfold_more</i>
                                    </div>
                                </th>
                                <th>
                                    <div class="sortable-header" onclick="sortTable('score')" data-sort="none">
                                        <span>综合评分</span>
                                        <i class="material-icons sort-indicator" id="sort-score">unfold_more</i>
                                    </div>
                                </th>
                                <th>投资建议</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="portfolio-table">
                            <!-- 投资组合数据将在JS中动态填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Portfolio Analysis Section -->
    <div id="portfolio-analysis" style="display: none;">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 32px;">
            <!-- Portfolio Score Card -->
            <div class="md3-card md3-card-elevated">
                <div class="md3-card-header">
                    <h2 class="md3-card-title">
                        <i class="material-icons">analytics</i>
                        投资组合评分
                    </h2>
                </div>
                <div class="md3-card-body">
                    <div style="display: grid; grid-template-columns: 200px 1fr; gap: 24px; align-items: center;">
                        <div style="text-align: center;">
                            <div id="portfolio-score-chart"></div>
                            <div style="margin-top: 16px;">
                                <div id="portfolio-score" style="font-family: var(--md-sys-typescale-financial-large-font); font-size: var(--md-sys-typescale-financial-large-size); font-weight: var(--md-sys-typescale-financial-large-weight); color: var(--md-sys-color-primary);">--</div>
                                <div style="font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); color: var(--md-sys-color-on-surface-variant); margin-top: 4px;">综合评分</div>
                            </div>
                        </div>
                        <div>
                            <h3 style="font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: var(--md-sys-typescale-title-medium-weight); margin-bottom: 24px;">维度评分</h3>

                            <!-- Technical Score -->
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); color: var(--md-sys-color-on-surface);">技术面</span>
                                    <span id="technical-score" style="font-family: var(--md-sys-typescale-financial-small-font); font-size: var(--md-sys-typescale-financial-small-size); color: var(--md-sys-color-primary);">--/40</span>
                                </div>
                                <div style="height: 8px; background-color: var(--md-sys-color-surface-container-high); border-radius: var(--md-sys-shape-corner-small); overflow: hidden;">
                                    <div id="technical-progress" style="height: 100%; background-color: var(--md-sys-color-tertiary); width: 0%; transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);"></div>
                                </div>
                            </div>

                            <!-- Fundamental Score -->
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); color: var(--md-sys-color-on-surface);">基本面</span>
                                    <span id="fundamental-score" style="font-family: var(--md-sys-typescale-financial-small-font); font-size: var(--md-sys-typescale-financial-small-size); color: var(--md-sys-color-success);">--/40</span>
                                </div>
                                <div style="height: 8px; background-color: var(--md-sys-color-surface-container-high); border-radius: var(--md-sys-shape-corner-small); overflow: hidden;">
                                    <div id="fundamental-progress" style="height: 100%; background-color: var(--md-sys-color-success); width: 0%; transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);"></div>
                                </div>
                            </div>

                            <!-- Capital Flow Score -->
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); color: var(--md-sys-color-on-surface);">资金面</span>
                                    <span id="capital-flow-score" style="font-family: var(--md-sys-typescale-financial-small-font); font-size: var(--md-sys-typescale-financial-small-size); color: var(--md-sys-color-warning);">--/20</span>
                                </div>
                                <div style="height: 8px; background-color: var(--md-sys-color-surface-container-high); border-radius: var(--md-sys-shape-corner-small); overflow: hidden;">
                                    <div id="capital-flow-progress" style="height: 100%; background-color: var(--md-sys-color-warning); width: 0%; transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Industry Distribution Card -->
            <div class="md3-card md3-card-elevated">
                <div class="md3-card-header">
                    <h2 class="md3-card-title">
                        <i class="material-icons">pie_chart</i>
                        行业分布
                    </h2>
                </div>
                <div class="md3-card-body">
                    <div id="industry-chart"></div>
                </div>
            </div>
        </div>

        <!-- Investment Recommendations -->
        <div id="portfolio-recommendations" class="md3-card">
            <div class="md3-card-header">
                <h2 class="md3-card-title">
                    <i class="material-icons">lightbulb</i>
                    智能投资建议
                </h2>
            </div>
            <div class="md3-card-body">
                <div id="recommendations-list">
                    <!-- 投资建议将在JS中动态填充 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Material Design 3 Add Stock Modal -->
<div class="modal fade" id="addStockModal" tabindex="-1" aria-labelledby="addStockModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border-radius: var(--md-sys-shape-corner-large); border: none; box-shadow: var(--md-sys-elevation-level3);">
            <div class="modal-header" style="background-color: var(--md-sys-color-surface-container-high); border-bottom: 1px solid var(--md-sys-color-outline-variant); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0; padding: 24px;">
                <h5 class="modal-title" id="addStockModalLabel" style="font-family: var(--md-sys-typescale-title-large-font); font-size: var(--md-sys-typescale-title-large-size); font-weight: var(--md-sys-typescale-title-large-weight); color: var(--md-sys-color-on-surface); margin: 0; display: flex; align-items: center;">
                    <i class="material-icons" style="margin-right: 12px; color: var(--md-sys-color-primary);">add_circle</i>
                    添加股票到投资组合
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 24px; color: var(--md-sys-color-on-surface-variant); cursor: pointer;">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="modal-body" style="padding: 32px; background-color: var(--md-sys-color-surface-container);">
                <form id="add-stock-form">
                    <!-- Stock Code Input -->
                    <div class="md3-text-field md3-text-field-outlined" style="margin-bottom: 24px;">
                        <input type="text" class="md3-text-field-input" id="add-stock-code" required placeholder=" ">
                        <label for="add-stock-code" class="md3-text-field-label">股票代码</label>
                        <div class="md3-text-field-supporting-text">
                            <i class="material-icons" style="font-size: 16px; margin-right: 4px;">info</i>
                            请输入6位股票代码，如：000001
                        </div>
                    </div>

                    <!-- Weight Input -->
                    <div class="md3-text-field md3-text-field-outlined" style="margin-bottom: 24px;">
                        <input type="number" class="md3-text-field-input" id="add-stock-weight" min="1" max="100" value="10" required placeholder=" ">
                        <label for="add-stock-weight" class="md3-text-field-label">持仓比例 (%)</label>
                        <div class="md3-text-field-supporting-text">
                            <i class="material-icons" style="font-size: 16px; margin-right: 4px;">info</i>
                            建议单只股票持仓比例不超过20%
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 16px 32px 32px 32px; background-color: var(--md-sys-color-surface-container); border-radius: 0 0 var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large); border-top: none; display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" class="md3-button md3-button-text" data-bs-dismiss="modal">取消</button>
                <button type="button" class="md3-button md3-button-filled" id="add-stock-btn">
                    <i class="material-icons">add</i> 添加股票
                </button>
            </div>
        </div>
    </div>
</div>

<!-- CSV批量导入模态框 -->
<div class="modal fade" id="importCsvModal" tabindex="-1" aria-labelledby="importCsvModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content" style="border-radius: var(--md-sys-shape-corner-large); border: none; box-shadow: var(--md-sys-elevation-level3);">
            <div class="modal-header" style="background-color: var(--md-sys-color-surface-container-high); border-bottom: 1px solid var(--md-sys-color-outline-variant); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0; padding: 24px;">
                <h5 class="modal-title" id="importCsvModalLabel" style="font-family: var(--md-sys-typescale-title-large-font); font-size: var(--md-sys-typescale-title-large-size); font-weight: var(--md-sys-typescale-title-large-weight); color: var(--md-sys-color-on-surface); margin: 0; display: flex; align-items: center;">
                    <i class="material-icons" style="margin-right: 12px; color: var(--md-sys-color-primary);">upload_file</i>
                    CSV批量导入股票
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 24px; color: var(--md-sys-color-on-surface-variant); cursor: pointer;">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="modal-body" style="padding: 32px; background-color: var(--md-sys-color-surface-container);">
                <!-- 文件上传区域 -->
                <div class="csv-upload-area" style="border: 2px dashed var(--md-sys-color-outline); border-radius: var(--md-sys-shape-corner-medium); padding: 32px; text-align: center; margin-bottom: 24px; background-color: var(--md-sys-color-surface-container-low); transition: all 0.3s ease;">
                    <i class="material-icons" style="font-size: 48px; color: var(--md-sys-color-primary); margin-bottom: 16px;">cloud_upload</i>
                    <h3 style="font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); color: var(--md-sys-color-on-surface); margin-bottom: 8px;">选择CSV文件</h3>
                    <p style="color: var(--md-sys-color-on-surface-variant); margin-bottom: 16px;">拖拽文件到此处或点击选择文件</p>
                    <input type="file" id="csv-file-input" accept=".csv" style="display: none;">
                    <button type="button" class="md3-button md3-button-outlined" onclick="document.getElementById('csv-file-input').click();">
                        <i class="material-icons">folder_open</i> 选择文件
                    </button>
                </div>

                <!-- 文件信息显示 -->
                <div id="csv-file-info" style="display: none; margin-bottom: 24px;">
                    <div class="md3-card" style="padding: 16px; background-color: var(--md-sys-color-surface-container-highest);">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <i class="material-icons" style="color: var(--md-sys-color-primary); margin-right: 8px;">description</i>
                            <span id="csv-file-name" style="font-weight: 500; color: var(--md-sys-color-on-surface);"></span>
                        </div>
                        <div style="font-size: 14px; color: var(--md-sys-color-on-surface-variant);">
                            <span>文件大小: </span><span id="csv-file-size"></span>
                        </div>
                    </div>
                </div>

                <!-- 格式说明 -->
                <div class="md3-card" style="padding: 20px; background-color: var(--md-sys-color-surface-container-highest); margin-bottom: 24px;">
                    <h4 style="font-family: var(--md-sys-typescale-title-small-font); color: var(--md-sys-color-on-surface); margin-bottom: 12px; display: flex; align-items: center;">
                        <i class="material-icons" style="margin-right: 8px; color: var(--md-sys-color-primary);">info</i>
                        CSV文件格式要求
                    </h4>
                    <ul style="color: var(--md-sys-color-on-surface-variant); margin: 0; padding-left: 20px;">
                        <li>文件必须包含 <code style="background-color: var(--md-sys-color-surface-variant); padding: 2px 6px; border-radius: 4px;">secID</code> 列</li>
                        <li>支持格式：603316.XSHG、601218.XSHG 等</li>
                        <li>支持编码：UTF-8、GBK、GB2312</li>
                        <li>系统会自动转换为6位股票代码格式</li>
                    </ul>
                </div>

                <!-- 进度显示 -->
                <div id="csv-upload-progress" style="display: none; margin-bottom: 24px;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <i class="material-icons" style="color: var(--md-sys-color-primary); margin-right: 8px;">hourglass_empty</i>
                        <span style="color: var(--md-sys-color-on-surface);">正在处理CSV文件...</span>
                    </div>
                    <div class="progress" style="height: 8px; background-color: var(--md-sys-color-surface-variant); border-radius: 4px;">
                        <div class="progress-bar" style="background-color: var(--md-sys-color-primary); border-radius: 4px; transition: width 0.3s ease;" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- 结果显示 -->
                <div id="csv-import-result" style="display: none;">
                    <!-- 结果内容将通过JavaScript动态生成 -->
                </div>
            </div>
            <div class="modal-footer" style="padding: 16px 32px 32px 32px; background-color: var(--md-sys-color-surface-container); border-radius: 0 0 var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large); border-top: none; display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" class="md3-button md3-button-text" data-bs-dismiss="modal">取消</button>
                <button type="button" class="md3-button md3-button-filled" id="csv-import-btn" disabled>
                    <i class="material-icons">upload</i> 开始导入
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 投资组合数据
    let portfolio = [];
    let portfolioAnalysis = null;

    // 多投资组合管理器
    let portfolioManager = {
        portfolios: {},
        currentPortfolioId: 'default',
        portfolioMetadata: {}
    };

    // ==================== 多投资组合管理核心函数 ====================

    // 获取投资组合管理器数据
    function getPortfolioManager() {
        const saved = localStorage.getItem('portfolioManager');
        if (saved) {
            return JSON.parse(saved);
        }

        // 如果没有多投资组合数据，检查是否需要迁移旧数据
        const oldPortfolio = localStorage.getItem('portfolio');
        if (oldPortfolio) {
            return migrateOldPortfolio(JSON.parse(oldPortfolio));
        }

        // 返回默认结构
        return {
            portfolios: {
                'default': []
            },
            currentPortfolioId: 'default',
            portfolioMetadata: {
                'default': {
                    name: '默认投资组合',
                    createdAt: new Date().toISOString(),
                    isDefault: true
                }
            }
        };
    }

    // 保存投资组合管理器数据
    function savePortfolioManager() {
        localStorage.setItem('portfolioManager', JSON.stringify(portfolioManager));
    }

    // 迁移旧的单投资组合数据
    function migrateOldPortfolio(oldData) {
        const migrated = {
            portfolios: {
                'default': oldData
            },
            currentPortfolioId: 'default',
            portfolioMetadata: {
                'default': {
                    name: '默认投资组合',
                    createdAt: new Date().toISOString(),
                    isDefault: true
                }
            }
        };

        // 保存迁移后的数据
        localStorage.setItem('portfolioManager', JSON.stringify(migrated));
        // 删除旧数据
        localStorage.removeItem('portfolio');

        showSuccess('已自动迁移您的投资组合数据到新的多投资组合系统');
        return migrated;
    }

    // 创建新投资组合
    function createNewPortfolio() {
        let name = prompt('请输入新投资组合的名称：');
        if (!name) {
            return; // 用户取消
        }

        name = name.trim();
        if (name === '') {
            showError('投资组合名称不能为空');
            return;
        }

        if (name.length > 20) {
            showError('投资组合名称不能超过20个字符');
            return;
        }

        // 检查名称是否已存在
        const existingNames = Object.values(portfolioManager.portfolioMetadata).map(meta => meta.name);
        if (existingNames.includes(name)) {
            showError('投资组合名称已存在，请使用其他名称');
            return;
        }

        const portfolioId = 'portfolio_' + Date.now();
        portfolioManager.portfolios[portfolioId] = [];
        portfolioManager.portfolioMetadata[portfolioId] = {
            name: name,
            createdAt: new Date().toISOString(),
            isDefault: false
        };

        savePortfolioManager();
        renderPortfolioTabs();
        showSuccess(`投资组合 "${name}" 创建成功`);

        // 询问是否立即切换到新投资组合
        if (confirm(`是否立即切换到新创建的投资组合 "${name}"？`)) {
            switchPortfolio(portfolioId);
        }
    }

    // 删除投资组合
    function deletePortfolio(portfolioId) {
        if (portfolioId === 'default') {
            showError('默认投资组合不能删除');
            return;
        }

        const metadata = portfolioManager.portfolioMetadata[portfolioId];
        if (!metadata) {
            return;
        }

        if (!confirm(`确定要删除投资组合 "${metadata.name}" 吗？此操作不可撤销。`)) {
            return;
        }

        delete portfolioManager.portfolios[portfolioId];
        delete portfolioManager.portfolioMetadata[portfolioId];

        // 如果删除的是当前投资组合，切换到默认投资组合
        if (portfolioManager.currentPortfolioId === portfolioId) {
            portfolioManager.currentPortfolioId = 'default';
            loadCurrentPortfolio();
        }

        savePortfolioManager();
        renderPortfolioTabs();
        showSuccess(`投资组合 "${metadata.name}" 已删除`);
    }

    // 切换投资组合
    function switchPortfolio(portfolioId) {
        if (portfolioId === portfolioManager.currentPortfolioId) {
            return;
        }

        // 添加切换动画
        const contentArea = $('.md3-portfolio-container');
        contentArea.addClass('portfolio-switching');

        // 显示加载状态
        const loadingHtml = `
            <div class="portfolio-loading">
                <i class="material-icons">refresh</i>
                <span>正在切换投资组合...</span>
            </div>
        `;

        // 保存当前投资组合数据
        portfolioManager.portfolios[portfolioManager.currentPortfolioId] = [...portfolio];

        // 延迟切换以显示动画效果
        setTimeout(() => {
            // 切换到新投资组合
            portfolioManager.currentPortfolioId = portfolioId;
            loadCurrentPortfolio();

            savePortfolioManager();
            renderPortfolioTabs();

            // 移除切换动画，添加内容动画
            contentArea.removeClass('portfolio-switching');
            $('#portfolio-content, #portfolio-analysis, #portfolio-recommendations').addClass('portfolio-content-fade');

            const metadata = portfolioManager.portfolioMetadata[portfolioId];
            showSuccess(`已切换到投资组合 "${metadata.name}"`);

            // 清除动画类
            setTimeout(() => {
                $('.portfolio-content-fade').removeClass('portfolio-content-fade');
            }, 300);
        }, 200);
    }

    // 加载当前投资组合数据
    function loadCurrentPortfolio() {
        const currentId = portfolioManager.currentPortfolioId;
        portfolio = [...(portfolioManager.portfolios[currentId] || [])];
        renderPortfolio();
        analyzePortfolio();
    }

    $(document).ready(function() {
        // 初始化多投资组合管理器
        portfolioManager = getPortfolioManager();

        // 渲染投资组合标签页
        renderPortfolioTabs();

        // 从本地存储加载投资组合
        loadPortfolio();
        // 从本地存储加载投资组合
        loadPortfolio();
    });

    // 渲染投资组合标签页
    function renderPortfolioTabs() {
        const tabsContainer = $('#portfolio-tabs');
        tabsContainer.empty();

        // 渲染所有投资组合标签
        Object.keys(portfolioManager.portfolios).forEach(portfolioId => {
            const metadata = portfolioManager.portfolioMetadata[portfolioId];
            const isActive = portfolioId === portfolioManager.currentPortfolioId;

            const tab = $(`
                <div class="md3-tab ${isActive ? 'active' : ''}" data-portfolio-id="${portfolioId}">
                    ${metadata.name}
                    ${!metadata.isDefault ? '<button class="md3-tab-delete-btn" onclick="deletePortfolio(\'' + portfolioId + '\'); event.stopPropagation();">×</button>' : ''}
                </div>
            `);

            tab.click(function() {
                switchPortfolio(portfolioId);
            });

            tabsContainer.append(tab);
        });

        // 添加新建投资组合按钮
        const addBtn = $(`
            <button class="md3-tab-add-btn" title="创建新投资组合">
                +
            </button>
        `);

        addBtn.click(function() {
            createNewPortfolio();
        });

        tabsContainer.append(addBtn);
    }

    // 重新初始化文档就绪事件处理
    $(document).ready(function() {
        // 添加股票按钮点击事件
        $('#add-stock-btn').click(function() {
            addStockToPortfolio();
        });

        // CSV文件选择事件
        $('#csv-file-input').change(function() {
            handleCsvFileSelect(this.files[0]);
        });

        // CSV导入按钮点击事件
        $('#csv-import-btn').click(function() {
            importCsvFile();
        });

        // 拖拽上传功能
        const uploadArea = $('.csv-upload-area');

        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).css('border-color', 'var(--md-sys-color-primary)');
            $(this).css('background-color', 'var(--md-sys-color-primary-container)');
        });

        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).css('border-color', 'var(--md-sys-color-outline)');
            $(this).css('background-color', 'var(--md-sys-color-surface-container-low)');
        });

        uploadArea.on('drop', function(e) {
            e.preventDefault();
            $(this).css('border-color', 'var(--md-sys-color-outline)');
            $(this).css('background-color', 'var(--md-sys-color-surface-container-low)');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleCsvFileSelect(files[0]);
            }
        });
    });

    // 从本地存储加载投资组合（支持多投资组合）
    function loadPortfolio() {
        // 加载当前投资组合数据
        loadCurrentPortfolio();

        // 为每个股票获取最新数据
        portfolio.forEach((stock, index) => {
            fetchStockData(stock.stock_code);
        });
    }

    // 渲染投资组合
    function renderPortfolio() {
        if (portfolio.length === 0) {
            $('#portfolio-empty').show();
            $('#portfolio-content').hide();
            $('#portfolio-analysis').hide();
            $('#portfolio-recommendations').hide();
            return;
        }

        $('#portfolio-empty').hide();
        $('#portfolio-content').show();
        $('#portfolio-analysis').show();
        $('#portfolio-recommendations').show();

        let html = '';
        portfolio.forEach((stock, index) => {
            const scoreClass = getMD3ScoreColorClass(stock.score || 0);
            const priceChangeClass = (stock.price_change || 0) >= 0 ? 'trend-up' : 'trend-down';
            const priceChangeIcon = (stock.price_change || 0) >= 0 ? '<i class="material-icons">trending_up</i>' : '<i class="material-icons">trending_down</i>';

            // 显示加载状态或实际数据
            const stockName = stock.loading ?
                '<span style="color: var(--md-sys-color-on-surface-variant);"><i class="material-icons" style="font-size: 16px; animation: spin 1s linear infinite;">refresh</i> 加载中...</span>' :
                `<span class="stock-name">${stock.stock_name || '未知'}</span>`;

            const industryDisplay = stock.industry || '-';

            html += `
                <tr>
                    <td><span class="stock-code">${stock.stock_code}</span></td>
                    <td>${stockName}</td>
                    <td>${industryDisplay}</td>
                    <td class="financial-data">${stock.weight}%</td>
                    <td class="financial-data">${stock.price ? formatNumber(stock.price, 2) : '-'}</td>
                    <td class="${priceChangeClass} financial-data">${stock.price_change ? (priceChangeIcon + ' ' + formatPercent(stock.price_change, 2)) : '-'}</td>
                    <td><span class="md3-badge ${scoreClass}">${stock.score || '-'}</span></td>
                    <td>${stock.recommendation || '-'}</td>
                    <td>
                        <div style="display: flex; gap: 8px;">
                            <a href="/stock_detail/${stock.stock_code}" class="md3-icon-button" title="查看详情" target="_blank" rel="noopener noreferrer">
                                <i class="material-icons">analytics</i>
                            </a>
                            <button type="button" class="md3-icon-button" onclick="removeStock(${index})" title="移除股票" style="color: var(--md-sys-color-error);">
                                <i class="material-icons">delete</i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        $('#portfolio-table').html(html);
    }

    // 添加股票到投资组合
    function addStockToPortfolio() {
        const stockCode = $('#add-stock-code').val().trim();
        const weight = parseInt($('#add-stock-weight').val() || 10);

        if (!stockCode) {
            showError('请输入股票代码');
            return;
        }

        // 检查是否已存在
        const existingIndex = portfolio.findIndex(s => s.stock_code === stockCode);
        if (existingIndex >= 0) {
            showError('此股票已在投资组合中');
            return;
        }

        // 添加到投资组合
        portfolio.push({
            stock_code: stockCode,
            weight: weight,
            stock_name: '加载中...',
            industry: '-',
            price: null,
            price_change: null,
            score: null,
            recommendation: null,
            loading: true,
            isNew: true  // 标记为新添加的股票
        });

        savePortfolio();
        $('#addStockModal').modal('hide');
        $('#add-stock-form')[0].reset();
        fetchStockData(stockCode);
    }

    // 添加重试加载功能
    function retryFetchStockData(stockCode) {
        showInfo(`正在重新获取 ${stockCode} 的数据...`);
        fetchStockData(stockCode);
    }

    // 获取股票数据
    function fetchStockData(stockCode) {
        const index = portfolio.findIndex(s => s.stock_code === stockCode);
        if (index < 0) return;

        // 显示加载状态
        portfolio[index].loading = true;
        savePortfolio();
        renderPortfolio();

        // 使用统一评分API获取完整的评分详情数据
        $.ajax({
            url: '/api/stock_score',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: 'A'
            }),
            success: function(response) {
                if (response && response.stock_code) {
                    // 更新投资组合数据，包含完整的评分详情
                    portfolio[index].stock_name = response.stock_name || '未知';
                    portfolio[index].industry = response.industry || '未知';
                    portfolio[index].price = response.price || 0;
                    portfolio[index].price_change = response.price_change || 0;
                    portfolio[index].score = response.score || 0;
                    portfolio[index].score_details = response.score_details || {};
                    portfolio[index].score_breakdown = response.score_breakdown || {};
                    portfolio[index].recommendation = response.recommendation || '-';
                    portfolio[index].loading = false;

                    savePortfolio();
                    analyzePortfolio();

                    // 只在添加新股票时显示成功消息
                    if (portfolio[index].isNew) {
                        showSuccess(`已添加 ${response.stock_name || stockCode} 到投资组合`);
                        portfolio[index].isNew = false;
                    }
                } else {
                    portfolio[index].stock_name = '数据获取失败';
                    portfolio[index].loading = false;
                    savePortfolio();
                    renderPortfolio();
                    showError(`获取股票 ${stockCode} 数据失败`);
                }
            },
            error: function(error) {
                portfolio[index].stock_name = '获取失败';
                portfolio[index].loading = false;
                savePortfolio();
                renderPortfolio();
                showError(`获取股票 ${stockCode} 数据失败`);
            }
        });
    }

    // 从投资组合中移除股票
    function removeStock(index) {
        if (confirm('确定要从投资组合中移除此股票吗？')) {
            portfolio.splice(index, 1);
            savePortfolio();
            renderPortfolio();
            analyzePortfolio();
        }
    }

    // 保存投资组合到本地存储（支持多投资组合）
    function savePortfolio() {
        // 保存当前投资组合数据到管理器
        portfolioManager.portfolios[portfolioManager.currentPortfolioId] = [...portfolio];
        savePortfolioManager();
        renderPortfolio();
    }


    // 分析投资组合
    function analyzePortfolio() {
        if (portfolio.length === 0) return;

        // 计算投资组合评分
        let totalScore = 0;
        let totalWeight = 0;
        let industriesMap = {};

        portfolio.forEach(stock => {
            if (stock.score) {
                totalScore += stock.score * stock.weight;
                totalWeight += stock.weight;

                // 统计行业分布
                const industry = stock.industry || '其他';
                if (industriesMap[industry]) {
                    industriesMap[industry] += stock.weight;
                } else {
                    industriesMap[industry] = stock.weight;
                }
            }
        });

        // 确保总权重不为零
        if (totalWeight > 0) {
            const portfolioScore = Math.round(totalScore / totalWeight);

            // 更新评分显示
            $('#portfolio-score').text(portfolioScore);

            // 简化的维度评分计算
            const technicalScore = Math.round(portfolioScore * 0.4);
            const fundamentalScore = Math.round(portfolioScore * 0.4);
            const capitalFlowScore = Math.round(portfolioScore * 0.2);

            $('#technical-score').text(technicalScore + '/40');
            $('#fundamental-score').text(fundamentalScore + '/40');
            $('#capital-flow-score').text(capitalFlowScore + '/20');

            $('#technical-progress').css('width', (technicalScore / 40 * 100) + '%');
            $('#fundamental-progress').css('width', (fundamentalScore / 40 * 100) + '%');
            $('#capital-flow-progress').css('width', (capitalFlowScore / 20 * 100) + '%');

            // 更新投资组合评分图表
            renderPortfolioScoreChart(portfolioScore);

            // 更新行业分布图表
            renderIndustryChart(industriesMap);

            // 生成投资建议
            generateRecommendations(portfolioScore);
        }
    }

    // 渲染投资组合评分图表
    function renderPortfolioScoreChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 150,
                type: 'radialBar',
            },
            plotOptions: {
                radialBar: {
                    hollow: {
                        size: '70%',
                    },
                    dataLabels: {
                        show: false
                    }
                }
            },
            colors: [getScoreColor(score)],
            stroke: {
                lineCap: 'round'
            }
        };

        // 清除旧图表
        $('#portfolio-score-chart').empty();

        const chart = new ApexCharts(document.querySelector("#portfolio-score-chart"), options);
        chart.render();
    }

    // 渲染行业分布图表
    function renderIndustryChart(industriesMap) {
        // 转换数据格式为图表所需
        const seriesData = [];
        const labels = [];

        for (const industry in industriesMap) {
            if (industriesMap.hasOwnProperty(industry)) {
                seriesData.push(industriesMap[industry]);
                labels.push(industry);
            }
        }

        const options = {
            series: seriesData,
            chart: {
                type: 'pie',
                height: 300
            },
            labels: labels,
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        height: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + '%';
                    }
                }
            }
        };

        // 清除旧图表
        $('#industry-chart').empty();

        const chart = new ApexCharts(document.querySelector("#industry-chart"), options);
        chart.render();
    }

    // 生成投资建议
    function generateRecommendations(portfolioScore) {
        let recommendations = [];

        // 根据总分生成基本建议
        if (portfolioScore >= 80) {
            recommendations.push({
                text: '您的投资组合整体评级优秀，当前市场环境下建议保持较高仓位',
                type: 'success'
            });
        } else if (portfolioScore >= 60) {
            recommendations.push({
                text: '您的投资组合整体评级良好，可以考虑适度增加仓位',
                type: 'primary'
            });
        } else if (portfolioScore >= 40) {
            recommendations.push({
                text: '您的投资组合整体评级一般，建议持币观望，等待更好的入场时机',
                type: 'warning'
            });
        } else {
            recommendations.push({
                text: '您的投资组合整体评级较弱，建议减仓规避风险',
                type: 'danger'
            });
        }

        // 检查行业集中度
        const industries = {};
        let totalWeight = 0;

        portfolio.forEach(stock => {
            const industry = stock.industry || '其他';
            if (industries[industry]) {
                industries[industry] += stock.weight;
            } else {
                industries[industry] = stock.weight;
            }
            totalWeight += stock.weight;
        });

        // 计算行业集中度
        let maxIndustryWeight = 0;
        let maxIndustry = '';

        for (const industry in industries) {
            if (industries[industry] > maxIndustryWeight) {
                maxIndustryWeight = industries[industry];
                maxIndustry = industry;
            }
        }

        const industryConcentration = maxIndustryWeight / totalWeight;

        if (industryConcentration > 0.5) {
            recommendations.push({
                text: `行业集中度较高，${maxIndustry}行业占比${Math.round(industryConcentration * 100)}%，建议适当分散投资降低非系统性风险`,
                type: 'warning'
            });
        }

        // 检查需要调整的个股
        const weakStocks = portfolio.filter(stock => stock.score && stock.score < 40);
        if (weakStocks.length > 0) {
            const stockNames = weakStocks.map(s => `${s.stock_name}(${s.stock_code})`).join('、');
            recommendations.push({
                text: `以下个股评分较低，建议考虑调整：${stockNames}`,
                type: 'danger'
            });
        }

        const strongStocks = portfolio.filter(stock => stock.score && stock.score > 70);
        if (strongStocks.length > 0 && portfolioScore < 60) {
            const stockNames = strongStocks.map(s => `${s.stock_name}(${s.stock_code})`).join('、');
            recommendations.push({
                text: `以下个股表现强势，可考虑增加配置比例：${stockNames}`,
                type: 'success'
            });
        }

        // 渲染建议 - Material Design 3 Style
        let html = '';
        recommendations.forEach(rec => {
            const iconMap = {
                success: 'check_circle',
                primary: 'info',
                warning: 'warning',
                danger: 'error'
            };

            const colorMap = {
                success: 'var(--md-sys-color-success-container)',
                primary: 'var(--md-sys-color-primary-container)',
                warning: 'var(--md-sys-color-warning-container)',
                danger: 'var(--md-sys-color-error-container)'
            };

            const textColorMap = {
                success: 'var(--md-sys-color-on-success-container)',
                primary: 'var(--md-sys-color-on-primary-container)',
                warning: 'var(--md-sys-color-on-warning-container)',
                danger: 'var(--md-sys-color-on-error-container)'
            };

            html += `
                <div style="
                    background-color: ${colorMap[rec.type]};
                    color: ${textColorMap[rec.type]};
                    padding: 16px 20px;
                    border-radius: var(--md-sys-shape-corner-medium);
                    margin-bottom: 12px;
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    font-family: var(--md-sys-typescale-body-medium-font);
                    font-size: var(--md-sys-typescale-body-medium-size);
                    line-height: var(--md-sys-typescale-body-medium-line-height);
                ">
                    <i class="material-icons" style="font-size: 20px; margin-top: 2px;">${iconMap[rec.type]}</i>
                    <span>${rec.text}</span>
                </div>
            `;
        });

        $('#recommendations-list').html(html);
    }

    // Enhanced Material Design 3 Score Color Functions
    function getMD3ScoreColorClass(score) {
        if (score >= 80) return 'md3-score-excellent';
        if (score >= 60) return 'md3-score-good';
        if (score >= 40) return 'md3-score-fair';
        return 'md3-score-poor';
    }

    // 获取评分颜色 - Material Design 3
    function getScoreColor(score) {
        if (score >= 80) return 'var(--md-sys-color-success)';
        if (score >= 60) return 'var(--md-sys-color-bull)';
        if (score >= 40) return 'var(--md-sys-color-warning)';
        return 'var(--md-sys-color-error)';
    }

    // 格式化数字
    function formatNumber(num, decimals = 2) {
        if (num === null || num === undefined) return '-';
        return parseFloat(num).toFixed(decimals);
    }

    // 格式化百分比
    function formatPercent(num, decimals = 2) {
        if (num === null || num === undefined) return '-';
        const formatted = parseFloat(num).toFixed(decimals);
        return formatted + '%';
    }

    // 显示成功消息
    function showSuccess(message) {
        showAlert(message, 'success');
    }

    // 显示错误消息
    function showError(message) {
        showAlert(message, 'error');
    }

    // 显示信息消息
    function showInfo(message) {
        showAlert(message, 'info');
    }

    // 通用消息显示函数
    function showAlert(message, type) {
        const alertContainer = $('#alerts-container');
        const alertId = 'alert-' + Date.now();

        const alertColors = {
            success: 'var(--md-sys-color-success-container)',
            error: 'var(--md-sys-color-error-container)',
            info: 'var(--md-sys-color-primary-container)'
        };

        const alertTextColors = {
            success: 'var(--md-sys-color-on-success-container)',
            error: 'var(--md-sys-color-on-error-container)',
            info: 'var(--md-sys-color-on-primary-container)'
        };

        const alertIcons = {
            success: 'check_circle',
            error: 'error',
            info: 'info'
        };

        const alertHtml = `
            <div id="${alertId}" class="md3-alert" style="
                background-color: ${alertColors[type]};
                color: ${alertTextColors[type]};
                padding: 16px 24px;
                border-radius: var(--md-sys-shape-corner-large);
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                box-shadow: var(--md-sys-elevation-level1);
                animation: slideInDown 0.3s ease-out;
            ">
                <i class="material-icons" style="margin-right: 12px; font-size: 20px;">${alertIcons[type]}</i>
                <span style="flex: 1; font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size);">${message}</span>
                <button onclick="$('#${alertId}').fadeOut(300, function() { $(this).remove(); })" style="
                    background: none;
                    border: none;
                    color: inherit;
                    cursor: pointer;
                    padding: 4px;
                    margin-left: 12px;
                ">
                    <i class="material-icons" style="font-size: 18px;">close</i>
                </button>
            </div>
        `;

        alertContainer.append(alertHtml);

        // 自动移除消息
        setTimeout(() => {
            $(`#${alertId}`).fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // 表格排序功能
    let currentSort = {
        column: null,
        direction: 'none' // 'none', 'asc', 'desc'
    };

    function sortTable(column) {
        // 获取当前排序状态
        const currentDirection = currentSort.column === column ? currentSort.direction : 'none';

        // 循环切换排序方向：none -> asc -> desc -> none
        let newDirection;
        switch (currentDirection) {
            case 'none':
                newDirection = 'asc';
                break;
            case 'asc':
                newDirection = 'desc';
                break;
            case 'desc':
                newDirection = 'none';
                break;
            default:
                newDirection = 'asc';
        }

        // 重置所有排序指示器
        resetSortIndicators();

        // 更新当前排序状态
        currentSort.column = column;
        currentSort.direction = newDirection;

        // 更新排序指示器
        updateSortIndicator(column, newDirection);

        // 执行排序
        if (newDirection === 'none') {
            // 恢复原始顺序（按添加顺序）
            renderPortfolio();
        } else {
            // 执行排序
            const sortedPortfolio = [...portfolio].sort((a, b) => {
                return compareValues(a, b, column, newDirection);
            });
            renderSortedPortfolio(sortedPortfolio);
        }
    }

    function compareValues(a, b, column, direction) {
        let valueA, valueB;

        switch (column) {
            case 'price_change':
                valueA = parseFloat(a.price_change) || 0;
                valueB = parseFloat(b.price_change) || 0;
                break;
            case 'score':
                valueA = parseInt(a.score) || 0;
                valueB = parseInt(b.score) || 0;
                break;
            default:
                return 0;
        }

        if (direction === 'asc') {
            return valueA - valueB;
        } else {
            return valueB - valueA;
        }
    }

    function resetSortIndicators() {
        // 重置所有排序指示器
        $('.sortable-header').attr('data-sort', 'none');
        $('.sort-indicator').text('unfold_more');
    }

    function updateSortIndicator(column, direction) {
        const header = $(`.sortable-header[onclick="sortTable('${column}')"]`);
        const indicator = $(`#sort-${column}`);

        header.attr('data-sort', direction);

        switch (direction) {
            case 'asc':
                indicator.text('keyboard_arrow_up');
                break;
            case 'desc':
                indicator.text('keyboard_arrow_down');
                break;
            case 'none':
                indicator.text('unfold_more');
                break;
        }
    }

    function renderSortedPortfolio(sortedPortfolio) {
        if (sortedPortfolio.length === 0) {
            $('#portfolio-empty').show();
            $('#portfolio-content').hide();
            $('#portfolio-analysis').hide();
            $('#portfolio-recommendations').hide();
            return;
        }

        $('#portfolio-empty').hide();
        $('#portfolio-content').show();

        let html = '';
        sortedPortfolio.forEach((stock, index) => {
            const scoreClass = getMD3ScoreColorClass(stock.score || 0);
            const priceChangeClass = (stock.price_change || 0) >= 0 ? 'trend-up' : 'trend-down';
            const priceChangeIcon = (stock.price_change || 0) >= 0 ? '<i class="material-icons">trending_up</i>' : '<i class="material-icons">trending_down</i>';

            // 显示加载状态或实际数据
            const stockName = stock.loading ?
                '<span style="color: var(--md-sys-color-on-surface-variant);"><i class="material-icons" style="font-size: 16px; animation: spin 1s linear infinite;">refresh</i> 加载中...</span>' :
                `<span class="stock-name">${stock.stock_name || '未知'}</span>`;

            const industryDisplay = stock.industry || '-';

            // 找到原始索引用于删除操作
            const originalIndex = portfolio.findIndex(s => s.stock_code === stock.stock_code);

            html += `
                <tr>
                    <td><span class="stock-code">${stock.stock_code}</span></td>
                    <td>${stockName}</td>
                    <td>${industryDisplay}</td>
                    <td class="financial-data">${stock.weight}%</td>
                    <td class="financial-data">${stock.price ? formatNumber(stock.price, 2) : '-'}</td>
                    <td class="${priceChangeClass} financial-data">${stock.price_change ? (priceChangeIcon + ' ' + formatPercent(stock.price_change, 2)) : '-'}</td>
                    <td><span class="md3-badge ${scoreClass}">${stock.score || '-'}</span></td>
                    <td>${stock.recommendation || '-'}</td>
                    <td>
                        <div style="display: flex; gap: 8px;">
                            <a href="/stock_detail/${stock.stock_code}" class="md3-icon-button" title="查看详情" target="_blank" rel="noopener noreferrer">
                                <i class="material-icons">analytics</i>
                            </a>
                            <button type="button" class="md3-icon-button" onclick="removeStock(${originalIndex})" title="移除股票" style="color: var(--md-sys-color-error);">
                                <i class="material-icons">delete</i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        $('#portfolio-table').html(html);
    }

    // CSV文件选择处理
    function handleCsvFileSelect(file) {
        if (!file) {
            return;
        }

        // 检查文件类型
        if (!file.name.toLowerCase().endsWith('.csv')) {
            showError('请选择CSV格式文件');
            return;
        }

        // 显示文件信息
        $('#csv-file-name').text(file.name);
        $('#csv-file-size').text(formatFileSize(file.size));
        $('#csv-file-info').show();
        $('#csv-import-btn').prop('disabled', false);

        // 存储文件对象
        window.selectedCsvFile = file;
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // CSV文件导入处理
    function importCsvFile() {
        if (!window.selectedCsvFile) {
            showError('请先选择CSV文件');
            return;
        }

        // 显示进度
        $('#csv-upload-progress').show();
        $('#csv-import-result').hide();
        $('#csv-import-btn').prop('disabled', true);

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', window.selectedCsvFile);

        // 发送请求
        $.ajax({
            url: '/api/portfolio/import_csv',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        $('.progress-bar').css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                $('#csv-upload-progress').hide();
                displayCsvImportResult(response);

                if (response.success && response.converted_stocks.length > 0) {
                    // 批量添加股票到投资组合
                    addStocksToPortfolio(response.converted_stocks);
                }
            },
            error: function(xhr, status, error) {
                $('#csv-upload-progress').hide();
                $('#csv-import-btn').prop('disabled', false);

                let errorMessage = '导入失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }
                showError(errorMessage);
            }
        });
    }

    // 显示CSV导入结果
    function displayCsvImportResult(result) {
        const resultHtml = `
            <div class="md3-card" style="padding: 20px; background-color: var(--md-sys-color-surface-container-highest);">
                <h4 style="font-family: var(--md-sys-typescale-title-small-font); color: var(--md-sys-color-on-surface); margin-bottom: 16px; display: flex; align-items: center;">
                    <i class="material-icons" style="margin-right: 8px; color: var(--md-sys-color-primary);">assessment</i>
                    导入结果统计
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 16px; margin-bottom: 16px;">
                    <div style="text-align: center; padding: 12px; background-color: var(--md-sys-color-primary-container); border-radius: var(--md-sys-shape-corner-medium);">
                        <div style="font-size: 24px; font-weight: bold; color: var(--md-sys-color-on-primary-container);">${result.total_count}</div>
                        <div style="font-size: 12px; color: var(--md-sys-color-on-primary-container);">总数</div>
                    </div>
                    <div style="text-align: center; padding: 12px; background-color: var(--md-sys-color-success-container); border-radius: var(--md-sys-shape-corner-medium);">
                        <div style="font-size: 24px; font-weight: bold; color: var(--md-sys-color-on-success-container);">${result.converted_count}</div>
                        <div style="font-size: 12px; color: var(--md-sys-color-on-success-container);">成功</div>
                    </div>
                    <div style="text-align: center; padding: 12px; background-color: var(--md-sys-color-error-container); border-radius: var(--md-sys-shape-corner-medium);">
                        <div style="font-size: 24px; font-weight: bold; color: var(--md-sys-color-on-error-container);">${result.failed_count}</div>
                        <div style="font-size: 12px; color: var(--md-sys-color-on-error-container);">失败</div>
                    </div>
                </div>

                ${result.failed_count > 0 ? `
                <div style="margin-top: 16px;">
                    <h5 style="color: var(--md-sys-color-error); margin-bottom: 8px;">失败的股票代码:</h5>
                    <div style="max-height: 120px; overflow-y: auto; background-color: var(--md-sys-color-surface-variant); padding: 12px; border-radius: var(--md-sys-shape-corner-small);">
                        ${result.failed_stocks.map(stock => `
                            <div style="font-size: 14px; color: var(--md-sys-color-on-surface-variant); margin-bottom: 4px;">
                                ${stock.code}: ${stock.reason}
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        $('#csv-import-result').html(resultHtml).show();
        $('#csv-import-btn').prop('disabled', false);
    }

    // 批量添加股票到投资组合
    function addStocksToPortfolio(convertedStocks) {
        let addedCount = 0;
        let skippedCount = 0;
        const skippedStocks = [];

        convertedStocks.forEach(stockInfo => {
            const stockCode = stockInfo.converted_code;

            // 检查是否已存在
            const existingIndex = portfolio.findIndex(s => s.stock_code === stockCode);
            if (existingIndex >= 0) {
                skippedCount++;
                skippedStocks.push(stockCode);
                return;
            }

            // 添加到投资组合
            portfolio.push({
                stock_code: stockCode,
                weight: 10, // 默认权重10%
                stock_name: '加载中...',
                industry: '-',
                price: null,
                price_change: null,
                score: null,
                recommendation: null,
                loading: true,
                isNew: true  // 标记为新添加的股票
            });

            addedCount++;
        });

        // 保存投资组合
        savePortfolio();

        // 为新添加的股票获取数据
        const newStocks = portfolio.filter(stock => stock.isNew);
        newStocks.forEach((stock, index) => {
            // 延迟请求避免并发过多
            setTimeout(() => {
                fetchStockData(stock.stock_code);
            }, index * 500); // 每500ms请求一个
        });

        // 显示添加结果
        let message = `成功添加 ${addedCount} 只股票到投资组合`;
        if (skippedCount > 0) {
            message += `，跳过 ${skippedCount} 只已存在的股票`;
        }
        showSuccess(message);

        if (skippedStocks.length > 0 && skippedStocks.length <= 5) {
            showInfo(`跳过的股票: ${skippedStocks.join(', ')}`);
        }

        // 关闭模态框
        setTimeout(() => {
            $('#importCsvModal').modal('hide');
            resetCsvImportModal();
        }, 2000);
    }

    // 重置CSV导入模态框
    function resetCsvImportModal() {
        $('#csv-file-input').val('');
        $('#csv-file-info').hide();
        $('#csv-upload-progress').hide();
        $('#csv-import-result').hide();
        $('#csv-import-btn').prop('disabled', true);
        $('.progress-bar').css('width', '0%');
        window.selectedCsvFile = null;

        // 重置拖拽区域样式
        $('.csv-upload-area').css('border-color', 'var(--md-sys-color-outline)');
        $('.csv-upload-area').css('background-color', 'var(--md-sys-color-surface-container-low)');
    }

    // 模态框关闭时重置状态
    $('#importCsvModal').on('hidden.bs.modal', function() {
        resetCsvImportModal();
    });

    // ==================== CSV导出功能 ====================

    /**
     * 导出投资组合数据为CSV文件
     */
    function exportPortfolioToCSV() {
        try {
            // 检查投资组合是否为空
            if (!portfolio || portfolio.length === 0) {
                showError('投资组合为空，无法导出');
                return;
            }

            // 检查是否有加载中的股票
            const loadingStocks = portfolio.filter(stock => stock.loading);
            if (loadingStocks.length > 0) {
                const confirmExport = confirm(`有 ${loadingStocks.length} 只股票正在加载数据，是否继续导出？\n（加载中的股票将显示为"加载中..."）`);
                if (!confirmExport) {
                    return;
                }
            }

            // 获取导出按钮并显示加载状态
            const exportButton = $('button[onclick="exportPortfolioToCSV()"]');
            const originalText = exportButton.html();
            exportButton.prop('disabled', true);
            exportButton.html('<i class="material-icons" style="animation: spin 1s linear infinite;">refresh</i> 导出中...');

            // 显示导出进度提示
            showInfo('正在生成CSV文件...');

            // 使用setTimeout让UI有时间更新
            setTimeout(() => {
                try {
                    // 生成CSV数据
                    const csvData = generateCSVData();

                    // 创建并下载文件
                    downloadCSVFile(csvData);

                    // 获取当前投资组合名称
                    const currentPortfolioName = portfolioManager.portfolioMetadata[portfolioManager.currentPortfolioId]?.name || '投资组合';
                    showSuccess(`成功导出"${currentPortfolioName}"的 ${portfolio.length} 只股票数据`);

                } catch (error) {
                    console.error('CSV导出失败:', error);
                    showError('CSV导出失败: ' + error.message);
                } finally {
                    // 恢复按钮状态
                    exportButton.prop('disabled', false);
                    exportButton.html(originalText);
                }
            }, 100);

        } catch (error) {
            console.error('CSV导出失败:', error);
            showError('CSV导出失败: ' + error.message);
        }
    }

    /**
     * 生成CSV格式的数据
     */
    function generateCSVData() {
        // 验证投资组合数据
        if (!portfolio || !Array.isArray(portfolio) || portfolio.length === 0) {
            throw new Error('投资组合数据无效或为空');
        }

        // CSV列标题（中文）- 与股票详情页面保持一致的5个评分维度
        const headers = [
            '股票代码',
            '股票名称',
            '所属行业',
            '持仓比例(%)',
            '当前价格',
            '今日涨跌(%)',
            '综合评分',
            '趋势分析评分',
            '技术指标评分',
            '成交量分析评分',
            '波动率评估评分',
            '动量指标评分',
            '投资建议',
            '数据时间'
        ];

        // 生成当前时间戳
        const currentTime = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 构建CSV数据行
        const rows = [headers];

        portfolio.forEach((stock, index) => {
            try {
                // 安全地获取股票数据，处理各种边界情况
                const stockCode = sanitizeCSVField(stock.stock_code) || `未知代码_${index + 1}`;
                const stockName = sanitizeCSVField(stock.stock_name) || (stock.loading ? '加载中...' : '未知股票');
                const industry = sanitizeCSVField(stock.industry) || '未知行业';
                const weight = validateNumber(stock.weight, 0);

                // 处理价格数据
                const price = stock.price !== null && stock.price !== undefined ?
                    formatCSVNumber(stock.price, 2) : '-';
                const priceChange = stock.price_change !== null && stock.price_change !== undefined ?
                    formatCSVNumber(stock.price_change, 2) : '-';

                // 获取真实的评分维度数据（与股票详情页面一致）
                const totalScore = validateNumber(stock.score, 0);

                // 从stock对象中获取详细评分数据，如果不存在则使用默认值
                const scoreDetails = stock.score_details || {};
                const trendScore = validateNumber(scoreDetails.trend, 0);
                const technicalScore = validateNumber(scoreDetails.technical, 0);
                const volumeScore = validateNumber(scoreDetails.volume, 0);
                const volatilityScore = validateNumber(scoreDetails.volatility, 0);
                const momentumScore = validateNumber(scoreDetails.momentum, 0);

                const recommendation = sanitizeCSVField(stock.recommendation) || '-';

                const row = [
                    stockCode,
                    stockName,
                    industry,
                    weight,
                    price,
                    priceChange,
                    totalScore || '-',
                    trendScore || '-',
                    technicalScore || '-',
                    volumeScore || '-',
                    volatilityScore || '-',
                    momentumScore || '-',
                    recommendation,
                    currentTime
                ];

                rows.push(row);

            } catch (error) {
                console.warn(`处理股票数据时出错 (索引 ${index}):`, error);
                // 添加错误行，确保数据完整性
                rows.push([
                    stock.stock_code || `错误_${index + 1}`,
                    '数据处理错误',
                    '-', '-', '-', '-', '-', '-', '-', '-', '-',
                    currentTime
                ]);
            }
        });

        // 转换为CSV格式字符串
        const csvContent = rows.map(row =>
            row.map(field => escapeCSVField(field)).join(',')
        ).join('\n');

        // 添加UTF-8 BOM以确保中文正确显示
        const BOM = '\uFEFF';
        return BOM + csvContent;
    }

    /**
     * 清理和验证CSV字段
     */
    function sanitizeCSVField(value) {
        if (value === null || value === undefined) return '';
        return String(value).trim();
    }

    /**
     * 验证数字字段
     */
    function validateNumber(value, defaultValue = 0) {
        if (value === null || value === undefined || value === '') return defaultValue;
        const num = parseFloat(value);
        return isNaN(num) ? defaultValue : num;
    }

    /**
     * 转义CSV字段中的特殊字符
     */
    function escapeCSVField(field) {
        const fieldStr = String(field);
        // 如果字段包含逗号、引号、换行符或制表符，需要用引号包围并转义内部引号
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n') || fieldStr.includes('\r') || fieldStr.includes('\t')) {
            return '"' + fieldStr.replace(/"/g, '""') + '"';
        }
        return fieldStr;
    }

    /**
     * 下载CSV文件
     */
    function downloadCSVFile(csvData) {
        try {
            // 验证CSV数据
            if (!csvData || typeof csvData !== 'string') {
                throw new Error('CSV数据无效');
            }

            // 生成安全的文件名
            const fileName = generateSafeFileName();

            // 检查浏览器兼容性
            if (!window.Blob || !window.URL || !window.URL.createObjectURL) {
                throw new Error('您的浏览器不支持文件下载功能，请升级浏览器');
            }

            // 创建Blob对象
            const blob = new Blob([csvData], {
                type: 'text/csv;charset=utf-8;'
            });

            // 检查Blob大小（限制为10MB）
            if (blob.size > 10 * 1024 * 1024) {
                throw new Error('导出文件过大，请减少投资组合中的股票数量');
            }

            // 尝试使用现代下载API
            if (window.navigator && window.navigator.msSaveBlob) {
                // IE浏览器支持
                window.navigator.msSaveBlob(blob, fileName);
                return;
            }

            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', fileName);
            link.style.visibility = 'hidden';
            link.style.position = 'absolute';
            link.style.left = '-9999px';

            // 添加到DOM并触发下载
            document.body.appendChild(link);

            // 触发下载
            link.click();

            // 延迟清理，确保下载开始
            setTimeout(() => {
                try {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                } catch (cleanupError) {
                    console.warn('清理下载资源时出错:', cleanupError);
                }
            }, 100);

        } catch (error) {
            console.error('文件下载失败:', error);
            throw new Error(`文件下载失败: ${error.message}`);
        }
    }

    /**
     * 生成安全的文件名
     */
    function generateSafeFileName() {
        try {
            const now = new Date();
            const dateStr = now.getFullYear() +
                           String(now.getMonth() + 1).padStart(2, '0') +
                           String(now.getDate()).padStart(2, '0');
            const timeStr = String(now.getHours()).padStart(2, '0') +
                           String(now.getMinutes()).padStart(2, '0') +
                           String(now.getSeconds()).padStart(2, '0');

            // 获取当前投资组合名称并清理特殊字符
            let portfolioName = '投资组合';
            try {
                portfolioName = portfolioManager.portfolioMetadata[portfolioManager.currentPortfolioId]?.name || '投资组合';
                // 移除文件名中不安全的字符
                portfolioName = portfolioName.replace(/[<>:"/\\|?*]/g, '_');
            } catch (error) {
                console.warn('获取投资组合名称失败，使用默认名称');
            }

            return `${portfolioName}_${dateStr}_${timeStr}.csv`;

        } catch (error) {
            console.warn('生成文件名失败，使用默认名称:', error);
            return `投资组合_${Date.now()}.csv`;
        }
    }

    /**
     * 格式化CSV导出专用的数字
     */
    function formatCSVNumber(num, decimals = 2) {
        if (num === null || num === undefined || num === '') return '-';
        const numValue = parseFloat(num);
        if (isNaN(numValue)) return '-';
        return numValue.toFixed(decimals);
    }
</script>

<style>
    /* Additional animations and enhancements */
    @keyframes slideInDown {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Enhanced Material Design 3 Text Fields */
    .md3-text-field {
        position: relative;
        margin-bottom: 16px;
    }

    .md3-text-field-input {
        width: 100%;
        padding: 16px 16px 8px 16px;
        border: 1px solid var(--md-sys-color-outline);
        border-radius: var(--md-sys-shape-corner-small);
        background-color: var(--md-sys-color-surface-container-high);
        color: var(--md-sys-color-on-surface);
        font-family: var(--md-sys-typescale-body-large-font);
        font-size: var(--md-sys-typescale-body-large-size);
        transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
        outline: none;
    }

    .md3-text-field-input:focus {
        border-color: var(--md-sys-color-primary);
        box-shadow: 0 0 0 2px var(--md-sys-color-primary-container);
    }

    .md3-text-field-label {
        position: absolute;
        left: 16px;
        top: 16px;
        color: var(--md-sys-color-on-surface-variant);
        font-family: var(--md-sys-typescale-body-large-font);
        font-size: var(--md-sys-typescale-body-large-size);
        transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
        pointer-events: none;
        background-color: var(--md-sys-color-surface-container-high);
        padding: 0 4px;
    }

    .md3-text-field-input:focus + .md3-text-field-label,
    .md3-text-field-input:not(:placeholder-shown) + .md3-text-field-label {
        top: -8px;
        left: 12px;
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-primary);
    }

    .md3-text-field-supporting-text {
        margin-top: 4px;
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
        display: flex;
        align-items: center;
    }

    /* Enhanced Icon Button Styles */
    .md3-icon-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: var(--md-sys-shape-corner-full);
        border: none;
        background-color: transparent;
        color: var(--md-sys-color-on-surface-variant);
        cursor: pointer;
        transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
        position: relative;
        overflow: hidden;
    }

    .md3-icon-button:hover {
        background-color: var(--md-sys-color-primary-container);
        color: var(--md-sys-color-on-primary-container);
    }

    .md3-icon-button:active {
        transform: scale(0.95);
    }

    /* Responsive Design Enhancements */
    @media (max-width: 768px) {
        .md3-portfolio-container {
            padding: 16px;
        }

        .md3-portfolio-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }

        .md3-button-group {
            flex-direction: column;
            width: 100%;
        }

        .md3-button-group .md3-button {
            width: 100%;
        }

        .md3-portfolio-title {
            font-size: var(--md-sys-typescale-headline-medium-size);
        }

        .md3-table th,
        .md3-table td {
            padding: 8px 4px;
            font-size: 12px;
        }

        .md3-table .stock-code {
            font-size: 11px;
        }

        .md3-icon-button {
            width: 36px;
            height: 36px;
        }
    }
</style>
{% endblock %}
