{% extends "layout.html" %}

{% block title %}修复验证页面{% endblock %}

{% block content %}
<div class="md3-main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="md3-card md3-card-elevated" style="margin-bottom: 32px;">
                    <div class="md3-card-header">
                        <h2 class="md3-card-title">
                            <i class="material-icons">bug_report</i> 修复验证页面
                        </h2>
                        <p class="md3-card-subtitle">验证表格修复是否生效</p>
                    </div>
                    <div class="md3-card-body">
                        <!-- 版本信息 -->
                        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
                            <h4 style="color: #1976d2; margin: 0 0 12px 0;">🔍 版本信息</h4>
                            <p style="margin: 0; color: #1976d2;">
                                <strong>CSS版本:</strong> 20241201-fix<br>
                                <strong>修复时间:</strong> <span id="current-time"></span><br>
                                <strong>页面加载时间:</strong> <span id="load-time"></span>
                            </p>
                        </div>

                        <!-- 测试表格 -->
                        <div style="overflow-x: auto;">
                            <table class="md3-data-table">
                                <thead>
                                    <tr>
                                        <th style="text-align: center;">代码</th>
                                        <th style="text-align: left;">名称</th>
                                        <th style="text-align: right;">最新价</th>
                                        <th style="text-align: center;">涨跌幅</th>
                                        <th style="text-align: right;">主力净流入</th>
                                        <th style="text-align: right;">主力净流入占比</th>
                                        <th style="text-align: center;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="test-table-body">
                                    <!-- 数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 检查清单 -->
                        <div style="background: #e8f5e8; border: 1px solid #4caf50; border-radius: 8px; padding: 16px; margin-top: 24px;">
                            <h4 style="color: #2e7d32; margin: 0 0 12px 0;">✅ 检查清单</h4>
                            <div id="check-list">
                                <div class="check-item">
                                    <input type="checkbox" id="check1"> 
                                    <label for="check1">涨跌幅列只显示一个值(箭头+百分比)</label>
                                </div>
                                <div class="check-item">
                                    <input type="checkbox" id="check2"> 
                                    <label for="check2">主力净流入列显示正确的资金数据</label>
                                </div>
                                <div class="check-item">
                                    <input type="checkbox" id="check3"> 
                                    <label for="check3">主力净流入占比列显示百分比</label>
                                </div>
                                <div class="check-item">
                                    <input type="checkbox" id="check4"> 
                                    <label for="check4">操作列显示两个按钮</label>
                                </div>
                                <div class="check-item">
                                    <input type="checkbox" id="check5"> 
                                    <label for="check5">所有列都正确对齐</label>
                                </div>
                                <div class="check-item">
                                    <input type="checkbox" id="check6"> 
                                    <label for="check6">上涨显示红色箭头，下跌显示绿色箭头</label>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="text-align: center; margin-top: 24px;">
                            <button onclick="refreshTest()" class="md3-button md3-button-filled" style="margin-right: 12px;">
                                <i class="material-icons">refresh</i> 刷新测试
                            </button>
                            <button onclick="clearCache()" class="md3-button md3-button-outlined">
                                <i class="material-icons">clear_all</i> 清除缓存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.check-item {
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.check-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.check-item label {
    color: #2e7d32;
    font-weight: 500;
    cursor: pointer;
}
</style>

<script>
// 显示当前时间
document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
document.getElementById('load-time').textContent = new Date().toLocaleString('zh-CN');

// 测试数据
const testData = [
    {
        code: "600027",
        name: "华电国际",
        price: 83.10,
        change_percent: 1.25,
        main_net_inflow: 8060890000,
        main_net_inflow_percent: 1.51
    },
    {
        code: "600021",
        name: "上海电力",
        price: 28.38,
        change_percent: -0.48,
        main_net_inflow: 7947700000,
        main_net_inflow_percent: 3.65
    },
    {
        code: "600015",
        name: "华夏银行",
        price: 36.27,
        change_percent: 1.82,
        main_net_inflow: 7639300000,
        main_net_inflow_percent: 4.63
    }
];

// 格式化函数
function formatNumber(value, decimals = 2) {
    return parseFloat(value).toFixed(decimals);
}

function formatPercent(value) {
    return parseFloat(value).toFixed(2) + '%';
}

function formatMoney(value) {
    value = parseFloat(value);
    if (Math.abs(value) >= 1e8) {
        return (value / 1e8).toFixed(2) + ' 亿';
    } else if (Math.abs(value) >= 1e4) {
        return (value / 1e4).toFixed(2) + ' 万';
    } else {
        return value.toFixed(2) + ' 元';
    }
}

// 渲染测试表格
function renderTestTable() {
    console.log('开始渲染测试表格...');
    
    let html = '';
    testData.forEach((item, index) => {
        // 数据清理
        const cleanCode = String(item.code || '').trim().replace(/[\r\n\t]/g, '');
        const cleanName = String(item.name || '').trim().replace(/[\r\n\t]/g, '');
        const cleanPrice = parseFloat(item.price) || 0;
        const cleanChangePercent = parseFloat(item.change_percent) || 0;
        const cleanMainNetInflow = parseFloat(item.main_net_inflow) || 0;
        const cleanMainNetInflowPercent = parseFloat(item.main_net_inflow_percent) || 0;

        // 颜色和图标
        const changeClass = cleanChangePercent >= 0 ? 'trend-up' : 'trend-down';
        const changeIcon = cleanChangePercent >= 0 ? 
            '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : 
            '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

        const netFlowClass = cleanMainNetInflow >= 0 ? 'trend-up' : 'trend-down';
        const netFlowIcon = cleanMainNetInflow >= 0 ? 
            '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : 
            '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

        // 单行HTML构建
        const rowHtml = `<tr><td style="text-align: center; white-space: nowrap;">${cleanCode}</td><td style="text-align: left; white-space: nowrap;">${cleanName}</td><td style="text-align: right; white-space: nowrap;">${formatNumber(cleanPrice, 2)}</td><td class="${changeClass}" style="text-align: center; white-space: nowrap;">${changeIcon} ${formatPercent(cleanChangePercent)}</td><td class="${netFlowClass}" style="text-align: right; white-space: nowrap;">${netFlowIcon} ${formatMoney(cleanMainNetInflow)}</td><td class="${netFlowClass}" style="text-align: right; white-space: nowrap;">${formatPercent(cleanMainNetInflowPercent)}</td><td style="text-align: center; white-space: nowrap;"><a href="#" class="md3-button md3-button-outlined md3-button-small" style="margin-right: 4px;"><i class="material-icons">trending_up</i></a><button class="md3-button md3-button-outlined md3-button-small"><i class="material-icons">account_balance</i></button></td></tr>`;

        html += rowHtml;
    });

    document.getElementById('test-table-body').innerHTML = html;
    console.log('测试表格渲染完成！');
}

// 刷新测试
function refreshTest() {
    location.reload();
}

// 清除缓存
function clearCache() {
    if ('caches' in window) {
        caches.keys().then(function(names) {
            for (let name of names) {
                caches.delete(name);
            }
        });
    }
    alert('缓存已清除，请刷新页面查看效果！');
}

// 页面加载完成后渲染表格
document.addEventListener('DOMContentLoaded', function() {
    renderTestTable();
});
</script>
{% endblock %}
