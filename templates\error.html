{% extends "layout.html" %}

{% block title %}错误 {{ error_code }} - 智能分析系统{% endblock %}

{% block content %}
<div class="page-transition" style="padding: 64px 32px; min-height: calc(100vh - 72px); display: flex; align-items: center; justify-content: center;">
    <div style="max-width: 600px; width: 100%;">
        <!-- Enhanced Material Design 3 Error Card -->
        <div class="md3-card md3-card-elevated md3-animate-fade-in">
            <div class="md3-card-header" style="background-color: var(--md-sys-color-error-container); color: var(--md-sys-color-on-error-container); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0;">
                <h2 class="md3-card-title" style="color: var(--md-sys-color-on-error-container); display: flex; align-items: center; gap: 12px;">
                    <i class="material-icons" style="font-size: 28px;">error</i>
                    错误 {{ error_code }}
                </h2>
            </div>
            <div class="md3-card-body" style="text-align: center; padding: 64px 32px;">
                <div style="margin-bottom: 32px;">
                    <i class="material-icons" style="font-size: 80px; color: var(--md-sys-color-error); margin-bottom: 24px;">warning</i>
                    <h3 style="font-family: var(--md-sys-typescale-headline-medium-font); font-size: var(--md-sys-typescale-headline-medium-size); font-weight: 500; color: var(--md-sys-color-on-surface); margin-bottom: 16px;">出现错误</h3>
                    <p style="font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); color: var(--md-sys-color-on-surface-variant); margin-bottom: 0; line-height: 1.5;">{{ message }}</p>
                </div>
                <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
                    <a href="/" class="md3-button md3-button-filled">
                        <i class="material-icons">home</i> 返回首页
                    </a>
                    <button class="md3-button md3-button-outlined" onclick="history.back()">
                        <i class="material-icons">arrow_back</i> 返回上一页
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}