# API 提供商 (openai SDK)
API_PROVIDER=openai # Gemini 未适配，暂时只支持openai SDK，new-api解决方案

# OpenAI API 配置
OPENAI_API_URL=https://***/v1
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_MODEL=your_open_model
NEWS_MODEL=your_open_news_model

# 其他新闻获取接口 Serp 和 tavily（免费1000次/月） 二选一即可，如果都选，都会使用
SERP_API_KEY=your_serp_api_key # Serp key申请地址：https://serper.dev/api-key
TAVILY_API_KEY=your_tavily_api_key # tavily key申请地址：https://app.tavily.com/playground

FUNCTION_CALL_MODEL=your_function_call_model

# QA上下文数量
MAX_QA=10

# Gemini API 配置
# GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent
# GEMINI_API_KEY=your_gemini_api_key
# GEMINI_API_MODEL=gemini-pro

# 安全配置
# API_KEY=your_api_key_for_protected_endpoints
# HMAC_SECRET=your_hmac_secret_key_for_webhook_verification
# ALLOWED_ORIGINS=http://localhost:8888,https://your-domain.com

# Redis缓存设置(可选)
# REDIS_URL=redis://redis:6379  #docker配置
REDIS_URL=redis://localhost:6379
USE_REDIS_CACHE=False

# 数据库设置(可选)
# DATABASE_URL=sqlite:///app/data/stock_analyzer.db  #docker配置
DATABASE_URL=sqlite:///data/stock_analyzer.db
USE_DATABASE=False

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/stock_analyzer.log

# API_KEY=UZXJfw3YNX80DLfN