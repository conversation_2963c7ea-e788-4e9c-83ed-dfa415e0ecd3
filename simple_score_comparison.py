#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的股票评分差异对比分析
"""

import pandas as pd
import numpy as np

def main():
    print("=" * 80)
    print("股票评分差异对比分析")
    print("=" * 80)
    
    # 读取独立程序结果
    standalone_df = pd.read_csv('results_20250710_155835.csv', encoding='utf-8-sig')
    print(f"✅ 独立程序结果: {len(standalone_df)} 条记录")
    print(f"独立程序列名: {list(standalone_df.columns)}")
    
    # 读取网页版结果
    web_df = pd.read_csv('20250709_20250710_160056.csv', encoding='utf-8-sig')
    print(f"✅ 网页版结果: {len(web_df)} 条记录")
    print(f"网页版列名: {list(web_df.columns)}")
    
    # 数据清理
    standalone_clean = standalone_df[standalone_df['状态'] == '成功评分'].copy()
    standalone_clean['股票代码_clean'] = standalone_clean['股票代码'].str.replace('.XSHE', '').str.replace('.XSHG', '')
    
    web_clean = web_df.copy()
    web_clean['股票代码_clean'] = web_clean['股票代码'].astype(str)
    
    print(f"\n📊 独立程序有效评分: {len(standalone_clean)} 只股票")
    print(f"📊 网页版有效评分: {len(web_clean)} 只股票")
    
    # 合并数据
    merged_df = pd.merge(
        standalone_clean[['股票代码_clean', '股票名称', '总评分', '趋势评分', '技术指标评分', '成交量评分', '波动率评分', '动量评分']],
        web_clean[['股票代码_clean', '股票名称', '综合评分', '趋势分析评分', '技术指标评分', '成交量分析评分', '波动率评估评分', '动量指标评分']],
        on='股票代码_clean',
        suffixes=('_独立', '_网页'),
        how='inner'
    )
    
    print(f"🔗 成功匹配 {len(merged_df)} 只股票进行对比")
    
    # 处理数据类型和'-'值
    for col in ['成交量分析评分', '波动率评估评分']:
        merged_df[col] = merged_df[col].replace('-', 0)
        merged_df[col] = pd.to_numeric(merged_df[col], errors='coerce').fillna(0)

    # 计算差异
    merged_df['总评分差异'] = merged_df['总评分'] - merged_df['综合评分']
    merged_df['趋势评分差异'] = merged_df['趋势评分'] - merged_df['趋势分析评分']
    merged_df['技术指标差异'] = merged_df['技术指标评分_独立'] - merged_df['技术指标评分_网页']
    merged_df['成交量差异'] = merged_df['成交量评分'] - merged_df['成交量分析评分']
    merged_df['波动率差异'] = merged_df['波动率评分'] - merged_df['波动率评估评分']
    merged_df['动量差异'] = merged_df['动量评分'] - merged_df['动量指标评分']
    
    # 统计分析
    print(f"\n📊 总评分统计:")
    print(f"  独立程序平均分: {merged_df['总评分'].mean():.1f}")
    print(f"  网页版平均分: {merged_df['综合评分'].mean():.1f}")
    print(f"  平均差异: {merged_df['总评分差异'].mean():.1f}")
    print(f"  差异标准差: {merged_df['总评分差异'].std():.1f}")
    
    # 各维度差异统计
    print(f"\n📈 各维度平均差异:")
    print(f"  趋势评分差异: {merged_df['趋势评分差异'].mean():.1f}")
    print(f"  技术指标差异: {merged_df['技术指标差异'].mean():.1f}")
    print(f"  成交量差异: {merged_df['成交量差异'].mean():.1f}")
    print(f"  波动率差异: {merged_df['波动率差异'].mean():.1f}")
    print(f"  动量差异: {merged_df['动量差异'].mean():.1f}")
    
    # 找出差异较大的股票
    large_diff = merged_df[abs(merged_df['总评分差异']) > 5].copy()
    large_diff = large_diff.sort_values('总评分差异', key=abs, ascending=False)
    
    print(f"\n⚠️ 评分差异>5分的股票 ({len(large_diff)} 只):")
    for _, row in large_diff.head(15).iterrows():
        print(f"  {row['股票代码_clean']} {row['股票名称_独立']:8s} | 独立:{row['总评分']:3.0f} 网页:{row['综合评分']:3.0f} 差异:{row['总评分差异']:+3.0f}")
    
    # 详细分析几个差异最大的股票
    print(f"\n🔍 差异最大的股票详细分析:")
    for _, row in large_diff.head(5).iterrows():
        print(f"\n📊 {row['股票代码_clean']} {row['股票名称_独立']}")
        print(f"  总评分: 独立{row['总评分']:.0f} vs 网页{row['综合评分']:.0f} (差异:{row['总评分差异']:+.0f})")
        print(f"  趋势: 独立{row['趋势评分']:.0f} vs 网页{row['趋势分析评分']:.0f} (差异:{row['趋势评分差异']:+.0f})")
        print(f"  技术: 独立{row['技术指标评分_独立']:.0f} vs 网页{row['技术指标评分_网页']:.0f} (差异:{row['技术指标差异']:+.0f})")
        print(f"  成交量: 独立{row['成交量评分']:.0f} vs 网页{row['成交量分析评分']:.0f} (差异:{row['成交量差异']:+.0f})")
        print(f"  波动率: 独立{row['波动率评分']:.0f} vs 网页{row['波动率评估评分']:.0f} (差异:{row['波动率差异']:+.0f})")
        print(f"  动量: 独立{row['动量评分']:.0f} vs 网页{row['动量指标评分']:.0f} (差异:{row['动量差异']:+.0f})")
    
    # 保存结果
    output_file = "score_comparison_simple.csv"
    merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 对比结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
