{% extends "layout.html" %}

{% block title %}资金流向 - 智能分析系统{% endblock %}

{% block content %}
<style>
/* 强制表格对齐 - 页面级别最高优先级 */
#concept-flow-table tbody tr td:nth-child(1) { text-align: center !important; }
#concept-flow-table tbody tr td:nth-child(2) { text-align: left !important; }
#concept-flow-table tbody tr td:nth-child(3) { text-align: right !important; }
#concept-flow-table tbody tr td:nth-child(4) { text-align: center !important; }
#concept-flow-table tbody tr td:nth-child(5) { text-align: right !important; }
#concept-flow-table tbody tr td:nth-child(6) { text-align: right !important; }
#concept-flow-table tbody tr td:nth-child(7) { text-align: right !important; }
#concept-flow-table tbody tr td:nth-child(8) { text-align: center !important; }
#concept-flow-table tbody tr td:nth-child(9) { text-align: center !important; }

#concept-flow-table tbody tr td {
    padding: 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid var(--md-sys-color-outline-variant) !important;
    color: var(--md-sys-color-on-surface) !important;
    white-space: nowrap !important;
}

#concept-flow-table tbody tr td * {
    display: inline !important;
    flex-direction: unset !important;
    align-items: unset !important;
    justify-content: unset !important;
}

#concept-flow-table tbody tr td i {
    vertical-align: middle !important;
    margin-right: 4px !important;
}
</style>
<div class="page-transition">
    <!-- Enhanced Material Design 3 分析表单 -->
    <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
        <div class="md3-card-header">
            <h2 class="md3-card-title">
                <i class="material-icons">account_balance</i> 资金流向分析
            </h2>
            <p class="md3-card-subtitle">追踪主力资金动向与市场热点</p>
        </div>
        <div class="md3-card-body">
            <form id="capital-flow-form" style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 20px; align-items: end;">
                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="data-type">
                        <option value="concept" selected>概念资金流</option>
                        <option value="individual">个股资金流</option>
                    </select>
                    <label class="md3-text-field-label">数据类型</label>
                </div>

                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="period-select">
                        <option value="10日排行" selected>10日排行</option>
                        <option value="5日排行">5日排行</option>
                        <option value="3日排行">3日排行</option>
                    </select>
                    <label class="md3-text-field-label">分析周期</label>
                </div>

                <div class="md3-text-field md3-text-field-outlined stock-input" style="display: none;">
                    <input type="text" class="md3-text-field-input" id="stock-code" placeholder=" ">
                    <label class="md3-text-field-label">股票代码</label>
                    <div class="md3-text-field-supporting-text">例如：600519、0700.HK</div>
                </div>

                <button type="submit" class="md3-button md3-button-filled md3-button-large">
                    <i class="material-icons">search</i> 开始查询
                </button>
            </form>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Loading Panel -->
    <div id="loading-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-fade-in">
            <div class="md3-card-body" style="text-align: center; padding: 64px 32px;">
                <div class="md3-progress-indicator" style="margin-bottom: 24px;"></div>
                <p style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); margin: 0;">正在获取资金流向数据...</p>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Concept Fund Flow Panel -->
    <div id="concept-flow-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <div>
                        <h3 class="md3-card-title">
                            <i class="material-icons">trending_up</i> 概念资金流向
                        </h3>
                        <p class="md3-card-subtitle">热门概念板块资金流向排行</p>
                    </div>
                    <span id="concept-period-badge" class="md3-badge md3-badge-primary">10日排行</span>
                </div>
            </div>
            <div class="md3-card-body" style="padding: 0;">
                <div style="overflow-x: auto;">
                    <table class="md3-data-table">
                        <thead>
                            <tr>
                                <th style="text-align: center;">序号</th>
                                <th style="text-align: left;">概念/行业</th>
                                <th style="text-align: right;">行业指数</th>
                                <th style="text-align: center;">涨跌幅</th>
                                <th style="text-align: right;">流入资金(亿)</th>
                                <th style="text-align: right;">流出资金(亿)</th>
                                <th style="text-align: right;">净额(亿)</th>
                                <th style="text-align: center;">公司家数</th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="concept-flow-table">
                            <!-- 资金流向数据将在JS中填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Concept Stocks Panel -->
    <div id="concept-stocks-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-slide-in-up" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <h3 id="concept-stocks-title" class="md3-card-title">
                    <i class="material-icons">business</i> 概念成分股
                </h3>
                <p class="md3-card-subtitle">概念板块内个股资金流向详情</p>
            </div>
            <div class="md3-card-body" style="padding: 0;">
                <div style="overflow-x: auto;">
                    <table class="md3-data-table">
                        <thead>
                            <tr>
                                <th style="text-align: center;">代码</th>
                                <th style="text-align: left;">名称</th>
                                <th style="text-align: right;">最新价</th>
                                <th style="text-align: center;">涨跌幅</th>
                                <th style="text-align: right;">主力净流入</th>
                                <th style="text-align: right;">主力净流入占比</th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="concept-stocks-table">
                            <!-- 概念成分股数据将在JS中填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Individual Fund Flow Panel -->
    <div id="individual-flow-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <div>
                        <h3 id="individual-flow-title" class="md3-card-title">
                            <i class="material-icons">show_chart</i> 个股资金流向
                        </h3>
                        <p class="md3-card-subtitle">个股主力资金流向详细分析</p>
                    </div>
                    <span id="individual-period-badge" class="md3-badge md3-badge-primary">10日排行</span>
                </div>
            </div>
            <div class="md3-card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
                    <div>
                        <h4 style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 20px;">资金流向概览</h4>
                        <div style="overflow-x: auto;">
                            <table class="md3-data-table md3-data-table-compact">
                                <tbody id="individual-flow-summary">
                                    <!-- 个股资金流向概览将在JS中填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <h4 style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 20px;">资金流入占比</h4>
                        <div id="fund-flow-pie-chart" style="height: 240px;"></div>
                    </div>
                </div>
                <div>
                    <h4 style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 20px;">资金流向历史</h4>
                    <div id="fund-flow-history-chart" style="height: 360px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Individual Fund Flow Rank Panel -->
    <div id="individual-rank-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <div>
                        <h3 class="md3-card-title">
                            <i class="material-icons">leaderboard</i> 个股资金流向排名
                        </h3>
                        <p class="md3-card-subtitle">个股主力资金净流入排行榜</p>
                    </div>
                    <span id="individual-rank-period-badge" class="md3-badge md3-badge-primary">10日排行</span>
                </div>
            </div>
            <div class="md3-card-body" style="padding: 0;">
                <div style="overflow-x: auto;">
                    <table class="md3-data-table">
                        <thead>
                            <tr>
                                <th style="text-align: center;">序号</th>
                                <th style="text-align: center;">代码</th>
                                <th style="text-align: left;">名称</th>
                                <th style="text-align: right;">最新价</th>
                                <th style="text-align: center;">涨跌幅</th>
                                <th style="text-align: right;">主力净流入</th>
                                <th style="text-align: right;">主力净流入占比</th>
                                <th style="text-align: right;">超大单净流入</th>
                                <th style="text-align: right;">大单净流入</th>
                                <th style="text-align: right;">中单净流入</th>
                                <th style="text-align: right;">小单净流入</th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="individual-rank-table">
                            <!-- 个股资金流向排名数据将在JS中填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 页面加载完成，等待用户选择搜索条件
        // 移除自动搜索，让用户主动触发搜索

        // 表单提交事件
        $('#capital-flow-form').submit(function(e) {
            e.preventDefault();
            const dataType = $('#data-type').val();
            const period = $('#period-select').val();
            const stockCode = $('#stock-code').val().trim();

            if (dataType === 'concept') {
                loadConceptFundFlow(period);
            } else if (dataType === 'individual') {
                if (stockCode) {
                    loadIndividualFundFlow(stockCode);
                } else {
                    loadIndividualFundFlowRank(period);
                }
            }
        });

        // 数据类型切换事件
        $('#data-type').change(function() {
            const dataType = $(this).val();
            if (dataType === 'individual') {
                $('.stock-input').show();
            } else {
                $('.stock-input').hide();
            }
        });
    });

    // 加载概念资金流向
    function loadConceptFundFlow(period) {
        $('#loading-panel').show();
        $('#concept-flow-panel, #concept-stocks-panel, #individual-flow-panel, #individual-rank-panel').hide();

        $.ajax({
            url: `/api/concept_fund_flow?period=${period}`,
            type: 'GET',
            success: function(response) {
                renderConceptFundFlow(response, period);
                $('#loading-panel').hide();
                $('#concept-flow-panel').show();
            },
            error: function(xhr, status, error) {
                $('#loading-panel').hide();
                showError('获取概念资金流向数据失败: ' + error);
            }
        });
    }

    // 加载概念成分股
    function loadConceptStocks(sector) {
        $('#loading-panel').show();
        $('#concept-stocks-panel').hide();

        $.ajax({
            url: `/api/sector_stocks?sector=${encodeURIComponent(sector)}`,
            type: 'GET',
            success: function(response) {
                renderConceptStocks(response, sector);
                $('#loading-panel').hide();
                $('#concept-stocks-panel').show();

                // 添加平滑滚动到成分股结果区域
                setTimeout(() => {
                    scrollToConceptStocks();
                }, 300); // 等待面板显示动画完成
            },
            error: function(xhr, status, error) {
                $('#loading-panel').hide();
                showError('获取概念成分股数据失败: ' + error);
            }
        });
    }

    // 平滑滚动到成分股面板
    function scrollToConceptStocks() {
        const conceptStocksPanel = document.getElementById('concept-stocks-panel');
        if (conceptStocksPanel) {
            // 添加高亮动画类
            conceptStocksPanel.classList.add('md3-animate-scroll-to');

            // 计算滚动位置（考虑导航栏高度）
            const navHeight = 80; // 导航栏高度
            const elementTop = conceptStocksPanel.offsetTop - navHeight;

            // 平滑滚动
            window.scrollTo({
                top: elementTop,
                behavior: 'smooth'
            });

            // 移除高亮动画类
            setTimeout(() => {
                conceptStocksPanel.classList.remove('md3-animate-scroll-to');
            }, 2000);
        }
    }

    // 加载个股资金流向
    function loadIndividualFundFlow(stockCode) {
        $('#loading-panel').show();
        $('#concept-flow-panel, #concept-stocks-panel, #individual-flow-panel, #individual-rank-panel').hide();

        $.ajax({
            url: `/api/individual_fund_flow?stock_code=${stockCode}`,
            type: 'GET',
            success: function(response) {
                renderIndividualFundFlow(response);
                $('#loading-panel').hide();
                $('#individual-flow-panel').show();
            },
            error: function(xhr, status, error) {
                $('#loading-panel').hide();
                showError('获取个股资金流向数据失败: ' + error);
            }
        });
    }

    // 加载个股资金流向排名
    function loadIndividualFundFlowRank(period) {
        $('#loading-panel').show();
        $('#concept-flow-panel, #concept-stocks-panel, #individual-flow-panel, #individual-rank-panel').hide();

        $.ajax({
            url: `/api/individual_fund_flow_rank?period=${period}`,
            type: 'GET',
            success: function(response) {
                renderIndividualFundFlowRank(response, period);
                $('#loading-panel').hide();
                $('#individual-rank-panel').show();
            },
            error: function(xhr, status, error) {
                $('#loading-panel').hide();
                showError('获取个股资金流向排名数据失败: ' + error);
            }
        });
    }

    // 渲染概念资金流向
    function renderConceptFundFlow(data, period) {
        $('#concept-period-badge').text(period);

        let html = '';
        if (!data || data.length === 0) {
            html = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
        } else {
            data.forEach((item, index) => {
                // 使用中国股市习惯：上涨红色向上箭头，下跌绿色向下箭头
                const changeClass = parseFloat(item.change_percent) >= 0 ? 'trend-up' : 'trend-down';
                const changeIcon = parseFloat(item.change_percent) >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                const netFlowClass = parseFloat(item.net_flow) >= 0 ? 'trend-up' : 'trend-down';
                const netFlowIcon = parseFloat(item.net_flow) >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                html += `
                    <tr>
                        <td>${item.rank}</td>
                        <td><a href="javascript:void(0)" onclick="loadConceptStocks('${item.sector}')">${item.sector}</a></td>
                        <td>${formatNumber(item.sector_index, 2)}</td>
                        <td><span style="color: ${parseFloat(item.change_percent) >= 0 ? '#d32f2f' : '#2e7d32'}; font-weight: 500;">${changeIcon} ${formatPercent(item.change_percent)}</span></td>
                        <td>${formatNumber(item.inflow, 2)}</td>
                        <td>${formatNumber(item.outflow, 2)}</td>
                        <td><span style="color: ${parseFloat(item.net_flow) >= 0 ? '#d32f2f' : '#2e7d32'}; font-weight: 500;">${netFlowIcon} ${formatNumber(item.net_flow, 2)}</span></td>
                        <td>${item.company_count}</td>
                        <td>
                            <button class="md3-button md3-button-outlined md3-button-small" onclick="loadConceptStocks('${item.sector}')">
                                <i class="material-icons">search</i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        $('#concept-flow-table').html(html);

        // 强制设置表格对齐
        setTimeout(() => {
            $('#concept-flow-table tr').each(function() {
                $(this).find('td').each(function(index) {
                    const $td = $(this);
                    switch(index) {
                        case 0: // 序号
                        case 3: // 涨跌幅
                        case 7: // 公司家数
                        case 8: // 操作
                            $td.css({
                                'text-align': 'center',
                                'display': 'table-cell',
                                'vertical-align': 'middle'
                            });
                            break;
                        case 1: // 概念/行业
                            $td.css({
                                'text-align': 'left',
                                'display': 'table-cell',
                                'vertical-align': 'middle'
                            });
                            break;
                        case 2: // 行业指数
                        case 4: // 流入资金
                        case 5: // 流出资金
                        case 6: // 净额
                            $td.css({
                                'text-align': 'right',
                                'display': 'table-cell',
                                'vertical-align': 'middle'
                            });
                            break;
                    }
                });
            });
        }, 100);
    }

    // 渲染概念成分股
    function renderConceptStocks(data, sector) {
        $('#concept-stocks-title').text(`${sector} 成分股`);

        // 调试信息
        console.log('renderConceptStocks data:', data);

        let html = '';
        if (!data || data.length === 0) {
            html = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
        } else {
            data.forEach((item, index) => {
                // 调试每个数据项
                console.log(`Item ${index}:`, item);

                // 确保数据清洁，移除可能的换行符和多余空格
                const cleanCode = String(item.code || '').trim().replace(/[\r\n\t]/g, '');
                const cleanName = String(item.name || '').trim().replace(/[\r\n\t]/g, '');
                const cleanPrice = parseFloat(item.price) || 0;
                const cleanChangePercent = parseFloat(item.change_percent) || 0;
                const cleanMainNetInflow = parseFloat(item.main_net_inflow) || 0;
                const cleanMainNetInflowPercent = parseFloat(item.main_net_inflow_percent) || 0;

                // 调试清理后的数据
                console.log(`Cleaned data ${index}:`, {
                    cleanCode, cleanName, cleanPrice, cleanChangePercent,
                    cleanMainNetInflow, cleanMainNetInflowPercent
                });

                // 使用中国股市习惯：上涨红色向上箭头，下跌绿色向下箭头
                const changeClass = cleanChangePercent >= 0 ? 'trend-up' : 'trend-down';
                const changeIcon = cleanChangePercent >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                const netFlowClass = cleanMainNetInflow >= 0 ? 'trend-up' : 'trend-down';
                const netFlowIcon = cleanMainNetInflow >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                // 构建表格行HTML，使用更清晰的格式和强制对齐样式
                html += `
                    <tr>
                        <td class="force-center">${cleanCode}</td>
                        <td class="force-left">${cleanName}</td>
                        <td class="force-right">${formatNumber(cleanPrice, 2)}</td>
                        <td class="force-center ${changeClass}">
                            <span style="color: ${cleanChangePercent >= 0 ? '#d32f2f' : '#2e7d32'}; font-weight: 500;">
                                ${changeIcon} ${formatPercent(cleanChangePercent)}
                            </span>
                        </td>
                        <td class="force-right ${netFlowClass}">
                            <span style="color: ${cleanMainNetInflow >= 0 ? '#d32f2f' : '#2e7d32'}; font-weight: 500;">
                                ${netFlowIcon} ${formatMoney(cleanMainNetInflow)}
                            </span>
                        </td>
                        <td class="force-right ${netFlowClass}">
                            <span style="color: ${cleanMainNetInflowPercent >= 0 ? '#d32f2f' : '#2e7d32'}; font-weight: 500;">
                                ${formatPercent(cleanMainNetInflowPercent)}
                            </span>
                        </td>
                        <td class="force-center action-buttons">
                            <a href="/stock_detail/${cleanCode}" class="md3-button md3-button-outlined md3-button-small" style="margin-right: 4px;">
                                <i class="material-icons">trending_up</i>
                            </a>
                            <button class="md3-button md3-button-outlined md3-button-small" onclick="loadIndividualFundFlow('${cleanCode}')">
                                <i class="material-icons">account_balance</i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        console.log('Final HTML:', html);
        $('#concept-stocks-table').html(html);

        // 强制应用表格对齐样式
        setTimeout(() => {
            $('#concept-stocks-table tr').each(function() {
                $(this).find('td').each(function(index) {
                    const $td = $(this);
                    switch(index) {
                        case 0: // 代码
                            $td.addClass('force-center').css({
                                'text-align': 'center !important',
                                'white-space': 'nowrap'
                            });
                            break;
                        case 1: // 名称
                            $td.addClass('force-left').css({
                                'text-align': 'left !important',
                                'white-space': 'nowrap'
                            });
                            break;
                        case 2: // 最新价
                            $td.addClass('force-right').css({
                                'text-align': 'right !important',
                                'white-space': 'nowrap'
                            });
                            break;
                        case 3: // 涨跌幅
                            $td.addClass('force-center').css({
                                'text-align': 'center !important',
                                'white-space': 'nowrap'
                            });
                            break;
                        case 4: // 主力净流入
                        case 5: // 主力净流入占比
                            $td.addClass('force-right').css({
                                'text-align': 'right !important',
                                'white-space': 'nowrap'
                            });
                            break;
                        case 6: // 操作
                            $td.addClass('force-center action-buttons').css({
                                'text-align': 'center !important',
                                'white-space': 'nowrap'
                            });
                            break;
                    }
                });
            });
        }, 100);
    }

    // 渲染个股资金流向
    function renderIndividualFundFlow(data) {
        if (!data || !data.data || data.data.length === 0) {
            showError('未获取到有效的个股资金流向数据');
            return;
        }

        // Sort data by date (descending - newest first)
        data.data.sort((a, b) => {
            // Parse dates to ensure proper comparison
            let dateA = new Date(a.date);
            let dateB = new Date(b.date);
            return dateB - dateA;
        });

        // Re-calculate summary for 90 days instead of relying on backend calculation
        recalculateSummary(data, 90);

        // 设置标题
        $('#individual-flow-title').text(`${data.stock_code} 资金流向`);

        // 渲染概览
        renderIndividualFlowSummary(data);

        // 渲染资金流入占比饼图
        renderFundFlowPieChart(data);

        // 渲染资金流向历史图表
        renderFundFlowHistoryChart(data);
    }

    function recalculateSummary(data, days) {
        // Get recent data (up to the specified number of days)
        const recent_data = data.data.slice(0, Math.min(days, data.data.length));

        // Calculate summary statistics
        const total_main_net_inflow = recent_data.reduce((sum, item) => sum + item.main_net_inflow, 0);
        const avg_main_net_inflow_percent = recent_data.reduce((sum, item) => sum + item.main_net_inflow_percent, 0) / recent_data.length;
        const positive_days = recent_data.filter(item => item.main_net_inflow > 0).length;
        const negative_days = recent_data.length - positive_days;

        // Create or update summary object
        data.summary = {
            recent_days: recent_data.length,
            total_main_net_inflow: total_main_net_inflow,
            avg_main_net_inflow_percent: avg_main_net_inflow_percent,
            positive_days: positive_days,
            negative_days: negative_days
        };
    }

    // 渲染个股资金流向概览
    function renderIndividualFlowSummary(data) {
        if (!data.summary) return;

        const summary = data.summary;
        // Now using the first item after sorting
        const recent = data.data[0]; // 最近一天的数据

        let html = `
            <tr>
                <td>最新日期：</td>
                <td>${recent.date}</td>
                <td>最新价：</td>
                <td>${formatNumber(recent.price, 2)}</td>
            </tr>
            <tr>
                <td>涨跌幅：</td>
                <td class="${recent.change_percent >= 0 ? 'trend-up' : 'trend-down'}">
                    ${recent.change_percent >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>'} ${formatPercent(recent.change_percent)}
                </td>
                <td>分析周期：</td>
                <td>${summary.recent_days}天</td>
            </tr>
            <tr>
                <td>主力净流入：</td>
                <td class="${summary.total_main_net_inflow >= 0 ? 'trend-up' : 'trend-down'}">
                    ${summary.total_main_net_inflow >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>'} ${formatMoney(summary.total_main_net_inflow)}
                </td>
                <td>净流入占比：</td>
                <td class="${summary.avg_main_net_inflow_percent >= 0 ? 'trend-up' : 'trend-down'}">
                    ${summary.avg_main_net_inflow_percent >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>'} ${formatPercent(summary.avg_main_net_inflow_percent)}
                </td>
            </tr>
            <tr>
                <td>资金流入天数：</td>
                <td>${summary.positive_days}天</td>
                <td>资金流出天数：</td>
                <td>${summary.negative_days}天</td>
            </tr>
        `;

        $('#individual-flow-summary').html(html);
    }

    // 渲染资金流入占比饼图
    function renderFundFlowPieChart(data) {
        if (!data.data || data.data.length === 0) return;

        // Using the first item after sorting
        const recent = data.data[0]; // 最近一天的数据

        // 计算资金流入总额（绝对值）
        const totalInflow = Math.abs(recent.super_large_net_inflow) +
                            Math.abs(recent.large_net_inflow) +
                            Math.abs(recent.medium_net_inflow) +
                            Math.abs(recent.small_net_inflow);

        // 计算各类型占比
        const superLargePct = Math.abs(recent.super_large_net_inflow) / totalInflow * 100;
        const largePct = Math.abs(recent.large_net_inflow) / totalInflow * 100;
        const mediumPct = Math.abs(recent.medium_net_inflow) / totalInflow * 100;
        const smallPct = Math.abs(recent.small_net_inflow) / totalInflow * 100;

        const options = {
            series: [superLargePct, largePct, mediumPct, smallPct],
            chart: {
                type: 'pie',
                height: 200
            },
            labels: ['超大单', '大单', '中单', '小单'],
            colors: ['#0d6efd', '#198754', '#ffc107', '#dc3545'],
            legend: {
                position: 'bottom'
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value.toFixed(2) + '%';
                    }
                }
            }
        };

        // 清除旧图表
        $('#fund-flow-pie-chart').empty();

        const chart = new ApexCharts(document.querySelector("#fund-flow-pie-chart"), options);
        chart.render();
    }

    // 渲染资金流向历史图表
    function renderFundFlowHistoryChart(data) {
        if (!data.data || data.data.length === 0) return;

        // 最近90天的数据
        // Since we've already sorted the data, just get the first 90 and reverse for chronological display
        const historyData = data.data.slice(0, 90).reverse();

        const dates = historyData.map(item => item.date);
        const mainNetInflow = historyData.map(item => item.main_net_inflow);
        const superLargeInflow = historyData.map(item => item.super_large_net_inflow);
        const largeInflow = historyData.map(item => item.large_net_inflow);
        const mediumInflow = historyData.map(item => item.medium_net_inflow);
        const smallInflow = historyData.map(item => item.small_net_inflow);
        const priceChanges = historyData.map(item => item.change_percent);

        const options = {
            series: [
                {
                    name: '主力净流入',
                    type: 'column',
                    data: mainNetInflow
                },
                {
                    name: '超大单',
                    type: 'line',
                    data: superLargeInflow
                },
                {
                    name: '大单',
                    type: 'line',
                    data: largeInflow
                },
                {
                    name: '价格涨跌幅',
                    type: 'line',
                    data: priceChanges
                }
            ],
            chart: {
                height: 300,
                type: 'line',
                toolbar: {
                    show: false
                }
            },
            stroke: {
                width: [0, 2, 2, 2],
                curve: 'smooth'
            },
            plotOptions: {
                bar: {
                    columnWidth: '50%'
                }
            },
            colors: ['#0d6efd', '#198754', '#ffc107', '#dc3545'],
            dataLabels: {
                enabled: false
            },
            labels: dates,
            xaxis: {
                type: 'category'
            },
            yaxis: [
                {
                    title: {
                        text: '资金流入(亿)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    labels: {
                        formatter: function(val) {
                            // Convert to 亿 for display (divide by 100 million)
                            return (val / 100000000).toFixed(2);
                        }
                    }
                },
                {
                    opposite: true,
                    title: {
                        text: '价格涨跌幅(%)',
                        style: {
                            fontSize: '12px'
                        }
                    },
                    labels: {
                        formatter: function(val) {
                            return val.toFixed(2);
                        }
                    },
                    min: -10,
                    max: 10,
                    tickAmount: 5
                }
            ],
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(y, { seriesIndex }) {
                        if (seriesIndex === 3) {
                            return y.toFixed(2) + '%';
                        }
                        // Display money values in 亿 (hundred million) units
                        return (y / 100000000).toFixed(2) + ' 亿';
                    }
                }
            },
            legend: {
                position: 'top'
            }
        };

        // 清除旧图表
        $('#fund-flow-history-chart').empty();

        const chart = new ApexCharts(document.querySelector("#fund-flow-history-chart"), options);
        chart.render();
    }

    // 渲染个股资金流向排名
    function renderIndividualFundFlowRank(data, period) {
        $('#individual-rank-period-badge').text(period);

        let html = '';
        if (!data || data.length === 0) {
            html = '<tr><td colspan="12" class="text-center">暂无数据</td></tr>';
        } else {
            data.forEach((item) => {
                // 使用中国股市习惯：上涨红色向上箭头，下跌绿色向下箭头
                const changeClass = parseFloat(item.change_percent) >= 0 ? 'trend-up' : 'trend-down';
                const changeIcon = parseFloat(item.change_percent) >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                const mainNetClass = parseFloat(item.main_net_inflow) >= 0 ? 'trend-up' : 'trend-down';
                const mainNetIcon = parseFloat(item.main_net_inflow) >= 0 ? '<i class="material-icons" style="color: #d32f2f;">arrow_upward</i>' : '<i class="material-icons" style="color: #2e7d32;">arrow_downward</i>';

                html += `
                    <tr>
                        <td style="text-align: center;">${item.rank}</td>
                        <td style="text-align: center;">${item.code}</td>
                        <td style="text-align: left;">${item.name}</td>
                        <td style="text-align: right;">${formatNumber(item.price, 2)}</td>
                        <td class="${changeClass}" style="text-align: center;">${changeIcon} ${formatPercent(item.change_percent)}</td>
                        <td class="${mainNetClass}" style="text-align: right;">${mainNetIcon} ${formatMoney(item.main_net_inflow)}</td>
                        <td class="${mainNetClass}" style="text-align: right;">${formatPercent(item.main_net_inflow_percent)}</td>
                        <td style="text-align: right;">${formatMoney(item.super_large_net_inflow)}</td>
                        <td style="text-align: right;">${formatMoney(item.large_net_inflow)}</td>
                        <td style="text-align: right;">${formatMoney(item.medium_net_inflow)}</td>
                        <td style="text-align: right;">${formatMoney(item.small_net_inflow)}</td>
                        <td style="text-align: center; white-space: nowrap;">
                            <a href="/stock_detail/${item.code}" class="md3-button md3-button-outlined md3-button-small" style="margin-right: 4px;">
                                <i class="material-icons">trending_up</i>
                            </a>
                            <button class="md3-button md3-button-outlined md3-button-small" onclick="loadIndividualFundFlow('${item.code}')">
                                <i class="material-icons">account_balance</i>
                            </button>
                        </td>
                    </tr>
                `;
            });
        }

        $('#individual-rank-table').html(html);
    }

    // 格式化资金数字（支持大数字缩写）
    function formatCompactNumber(num) {
        if (Math.abs(num) >= 1.0e9) {
            return (num / 1.0e9).toFixed(2) + "B";
        } else if (Math.abs(num) >= 1.0e6) {
            return (num / 1.0e6).toFixed(2) + "M";
        } else if (Math.abs(num) >= 1.0e3) {
            return (num / 1.0e3).toFixed(2) + "K";
        } else {
            return num.toFixed(2);
        }
    }

    // 格式化资金
    function formatMoney(value) {
        if (value === null || value === undefined) {
            return '--';
        }

        value = parseFloat(value);
        if (isNaN(value)) {
            return '--';
        }

        if (Math.abs(value) >= 1e8) {
            return (value / 1e8).toFixed(2) + ' 亿';
        } else if (Math.abs(value) >= 1e4) {
            return (value / 1e4).toFixed(2) + ' 万';
        } else {
            return value.toFixed(2) + ' 元';
        }
    }

    // 格式化百分比
    function formatPercent(value) {
        if (value === null || value === undefined) {
            return '--';
        }

        value = parseFloat(value);
        if (isNaN(value)) {
            return '--';
        }

        return value.toFixed(2) + '%';
    }

    // 格式化数字
    function formatNumber(value, decimals = 2) {
        if (value === null || value === undefined) {
            return '--';
        }

        value = parseFloat(value);
        if (isNaN(value)) {
            return '--';
        }

        return value.toFixed(decimals);
    }

    document.addEventListener('DOMContentLoaded', function() {
    const dataType = document.getElementById('data-type');
    const periodSelect = document.getElementById('period-select');
    const stockInput = document.querySelector('.stock-input');

    // 初始加载时检查默认值
    toggleOptions();

    dataType.addEventListener('change', toggleOptions);

    function toggleOptions() {
        if (dataType.value === 'individual') {
            // 个股资金流选项
            periodSelect.innerHTML = `
                <option value="3日">3日</option>
                <option value="5日">5日</option>
                <option value="10日">10日</option>
            `;
            stockInput.style.display = 'block';
        } else {
            // 概念资金流选项
            periodSelect.innerHTML = `
                <option value="10日排行" selected>10日排行</option>
                <option value="5日排行">5日排行</option>
                <option value="3日排行">3日排行</option>
            `;
            stockInput.style.display = 'none';
        }
    }
});
</script>
{% endblock %}