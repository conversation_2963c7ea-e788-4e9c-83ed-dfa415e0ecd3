#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ROC动量指标计算逻辑详细分析演示程序
基于standalone_stock_scorer.py中的ROC计算逻辑
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def analyze_roc_calculation():
    """详细分析ROC动量指标的计算逻辑"""
    
    print("=" * 80)
    print("ROC动量指标计算逻辑详细分析")
    print("=" * 80)
    
    # 1. 选择一个具体的股票和涨停日期作为示例
    stock_code = "000002"  # 万科A
    limit_up_date = "20250212"  # 涨停日期
    
    print(f"\n📊 分析股票: {stock_code} (万科A)")
    print(f"📅 涨停日期: {limit_up_date}")
    
    # 2. 获取历史数据（模拟standalone_stock_scorer.py的逻辑）
    print(f"\n🔍 数据获取逻辑分析:")
    print(f"   - 涨停日期: {limit_up_date}")
    
    # 解析涨停日期
    limit_date = datetime.strptime(limit_up_date, '%Y%m%d')
    print(f"   - 解析后的涨停日期: {limit_date.strftime('%Y-%m-%d')}")
    
    # 计算数据获取范围（以涨停日期为截止日期，向前获取90天数据）
    end_date = limit_date.strftime('%Y-%m-%d')
    start_date = (limit_date - timedelta(days=90)).strftime('%Y-%m-%d')
    
    print(f"   - 数据获取范围: {start_date} 到 {end_date}")
    print(f"   - 说明: 以涨停日期为截止点，向前获取约90天历史数据（确保至少60个交易日）")
    
    # 3. 使用模拟历史数据演示（避免网络问题）
    print(f"\n📈 生成模拟历史价格数据...")

    # 生成60个交易日的模拟数据
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    # 过滤掉周末
    dates = [d for d in dates if d.weekday() < 5]

    # 生成模拟价格数据（基于万科A的实际价格水平）
    np.random.seed(42)  # 固定随机种子，确保结果可重现
    base_price = 7.5
    price_changes = np.random.normal(0, 0.02, len(dates))  # 2%的日波动

    prices = [base_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 0.1))  # 确保价格不为负

    # 在涨停日设置涨停价格
    limit_up_index = None
    for i, date in enumerate(dates):
        if date.strftime('%Y%m%d') == limit_up_date:
            limit_up_index = i
            # 模拟涨停：在前一天基础上涨10%
            if i > 0:
                prices[i] = prices[i-1] * 1.10
            break

    df = pd.DataFrame({
        'date': dates[:len(prices)],
        'close': prices,
        'open': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'volume': [np.random.randint(100000, 1000000) for _ in prices]
    })

    print(f"✅ 生成 {len(df)} 条模拟历史数据")
    print(f"   - 数据时间范围: {df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"   - 涨停日索引: {limit_up_index}")
    if limit_up_index:
        print(f"   - 涨停日价格: {df.iloc[limit_up_index]['close']:.2f}")

    # 确保有足够的数据
    if len(df) < 15:
        print(f"❌ 数据不足，需要至少15条记录进行演示")
        return
    
    # 4. ROC计算逻辑详解
    print(f"\n🧮 ROC动量指标计算详解:")
    print(f"   - ROC公式: ROC = (当前收盘价 - N天前收盘价) / N天前收盘价 * 100")
    print(f"   - 代码实现: df['ROC'] = df['close'].pct_change(periods=10) * 100")
    print(f"   - 计算周期: 10个交易日")
    print(f"   - 使用数据: 收盘价 (close)")
    
    # 计算ROC
    df['ROC'] = df['close'].pct_change(periods=10) * 100
    
    # 5. 展示具体计算过程
    print(f"\n📋 ROC计算过程演示 (最后15个交易日):")
    print("-" * 80)
    
    # 显示最后15天的数据
    last_15_days = df.tail(15).copy()
    last_15_days['ROC_manual'] = ((last_15_days['close'] - last_15_days['close'].shift(10)) / last_15_days['close'].shift(10) * 100)
    
    for i, row in last_15_days.iterrows():
        date_str = row['date'].strftime('%Y-%m-%d')
        close_price = row['close']
        roc_value = row['ROC']
        
        # 找到10天前的价格
        if i >= 10:
            prev_close = df.iloc[i-10]['close']
            prev_date = df.iloc[i-10]['date'].strftime('%Y-%m-%d')
            manual_roc = (close_price - prev_close) / prev_close * 100
            
            print(f"{date_str}: 收盘价={close_price:6.2f}, 10天前({prev_date})={prev_close:6.2f}, ROC={roc_value:6.2f}%")
            print(f"           计算: ({close_price:.2f} - {prev_close:.2f}) / {prev_close:.2f} * 100 = {manual_roc:.2f}%")
        else:
            print(f"{date_str}: 收盘价={close_price:6.2f}, ROC=NaN (数据不足10天)")
        print()
    
    # 6. 涨停日当天的ROC值
    print(f"\n🎯 涨停日当天ROC分析:")
    print("-" * 50)
    
    # 找到涨停日当天的数据
    limit_up_row = df[df['date'] == limit_date]
    if not limit_up_row.empty:
        limit_up_data = limit_up_row.iloc[0]
        limit_up_roc = limit_up_data['ROC']
        limit_up_close = limit_up_data['close']
        
        print(f"涨停日期: {limit_date.strftime('%Y-%m-%d')}")
        print(f"涨停当天收盘价: {limit_up_close:.2f}")
        print(f"涨停当天ROC值: {limit_up_roc:.2f}%")
        
        # 找到10天前的数据
        limit_up_index = limit_up_row.index[0]
        if limit_up_index >= 10:
            prev_data = df.iloc[limit_up_index - 10]
            prev_close = prev_data['close']
            prev_date = prev_data['date'].strftime('%Y-%m-%d')
            
            print(f"10天前日期: {prev_date}")
            print(f"10天前收盘价: {prev_close:.2f}")
            print(f"ROC计算验证: ({limit_up_close:.2f} - {prev_close:.2f}) / {prev_close:.2f} * 100 = {((limit_up_close - prev_close) / prev_close * 100):.2f}%")
        
        # 7. ROC评分逻辑
        print(f"\n⭐ ROC动量评分逻辑:")
        print("-" * 30)
        
        if limit_up_roc > 5:
            score = 10
            level = "强劲上升动量"
        elif 2 <= limit_up_roc <= 5:
            score = 8
            level = "适度上升动量"
        elif 0 <= limit_up_roc < 2:
            score = 5
            level = "弱上升动量"
        elif -2 <= limit_up_roc < 0:
            score = 3
            level = "弱下降动量"
        else:
            score = 0
            level = "强劲下降动量"
        
        print(f"ROC值: {limit_up_roc:.2f}%")
        print(f"评分等级: {level}")
        print(f"动量评分: {score}/10分")
        
    else:
        print(f"❌ 未找到涨停日当天的数据")
    
    # 8. 时间基准确认
    print(f"\n⏰ 时间基准确认:")
    print("-" * 30)
    print("✅ ROC动量指标基于涨停日当天计算")
    print("✅ 使用涨停日当天的收盘价作为计算基准")
    print("✅ 向前回溯10个交易日进行动量计算")
    print("✅ 数据获取范围：以涨停日期为截止点，向前获取60个交易日历史数据")
    
    # 9. 关键结论
    print(f"\n📝 关键结论:")
    print("=" * 50)
    print("1. 数据来源：ROC基于涨停日期向前60个交易日的历史数据计算")
    print("2. 计算方法：ROC = (当前收盘价 - 10天前收盘价) / 10天前收盘价 * 100")
    print("3. 计算周期：10个交易日")
    print("4. 使用价格：收盘价 (close)")
    print("5. 时间基准：涨停日当天的ROC值，反映涨停前10个交易日的价格动量")
    print("6. 评分逻辑：根据ROC值大小分为5个等级，最高10分")

if __name__ == "__main__":
    analyze_roc_calculation()
