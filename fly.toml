app = "stock-analysis-system"
primary_region = "hkg"

[build]

[env]
  PORT = "8888"
  PYTHONUNBUFFERED = "1"
  PYTHONDONTWRITEBYTECODE = "1"

[http_service]
  internal_port = 8888
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256

[mounts]
  source = "stock_data"
  destination = "/app/data"
