#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动对比分析股票评分差异
"""

import pandas as pd

def main():
    print("=" * 80)
    print("手动股票评分差异对比分析")
    print("=" * 80)
    
    # 读取数据
    standalone_df = pd.read_csv('results_20250710_155835.csv', encoding='utf-8-sig')
    web_df = pd.read_csv('20250709_20250710_160056.csv', encoding='utf-8-sig')
    
    print(f"独立程序: {len(standalone_df)} 条记录")
    print(f"网页版: {len(web_df)} 条记录")
    
    # 手动选择几个股票进行对比
    test_stocks = ['002636', '600475', '600744', '605500', '605162', '000514']
    
    print(f"\n📊 手动对比分析 {len(test_stocks)} 只股票:")
    print("-" * 80)
    
    for stock_code in test_stocks:
        # 查找独立程序结果
        standalone_row = None
        for _, row in standalone_df.iterrows():
            if stock_code in str(row['股票代码']):
                standalone_row = row
                break
        
        # 查找网页版结果
        web_row = None
        for _, row in web_df.iterrows():
            if str(row['股票代码']) == stock_code:
                web_row = row
                break
        
        if standalone_row is not None and web_row is not None:
            print(f"\n🔍 {stock_code} {standalone_row['股票名称']}")
            print(f"  总评分: 独立{standalone_row['总评分']:.0f} vs 网页{web_row['综合评分']:.0f} (差异:{standalone_row['总评分'] - web_row['综合评分']:+.0f})")
            print(f"  趋势: 独立{standalone_row['趋势评分']:.0f} vs 网页{web_row['趋势分析评分']:.0f} (差异:{standalone_row['趋势评分'] - web_row['趋势分析评分']:+.0f})")
            print(f"  技术: 独立{standalone_row['技术指标评分']:.0f} vs 网页{web_row['技术指标评分']:.0f} (差异:{standalone_row['技术指标评分'] - web_row['技术指标评分']:+.0f})")
            
            # 处理成交量评分
            web_volume = web_row['成交量分析评分']
            if web_volume == '-':
                web_volume = 0
            else:
                web_volume = float(web_volume)
            print(f"  成交量: 独立{standalone_row['成交量评分']:.0f} vs 网页{web_volume:.0f} (差异:{standalone_row['成交量评分'] - web_volume:+.0f})")
            
            # 处理波动率评分
            web_volatility = web_row['波动率评估评分']
            if web_volatility == '-':
                web_volatility = 0
            else:
                web_volatility = float(web_volatility)
            print(f"  波动率: 独立{standalone_row['波动率评分']:.0f} vs 网页{web_volatility:.0f} (差异:{standalone_row['波动率评分'] - web_volatility:+.0f})")
            
            print(f"  动量: 独立{standalone_row['动量评分']:.0f} vs 网页{web_row['动量指标评分']:.0f} (差异:{standalone_row['动量评分'] - web_row['动量指标评分']:+.0f})")
        else:
            print(f"\n❌ {stock_code}: 数据不完整")
    
    # 分析关键发现
    print(f"\n" + "=" * 80)
    print("🔍 关键发现分析")
    print("=" * 80)
    
    print(f"\n1. 📈 波动率评分差异:")
    print(f"   - 独立程序: 大部分股票波动率评分为0")
    print(f"   - 网页版: 大部分股票波动率评分为'-'(即0)或有具体分值")
    print(f"   - 这表明波动率计算可能存在问题")
    
    print(f"\n2. 📊 成交量评分差异:")
    print(f"   - 网页版中部分股票成交量评分为'-'")
    print(f"   - 独立程序中所有股票都有成交量评分")
    print(f"   - 需要检查成交量计算逻辑")
    
    print(f"\n3. 🎯 总评分差异:")
    print(f"   - 主要差异来源于波动率和成交量评分")
    print(f"   - 趋势、技术指标、动量评分基本一致")
    
    # 检查独立程序中波动率为0的原因
    print(f"\n4. 🔍 独立程序波动率评分分析:")
    volatility_zero_count = (standalone_df['波动率评分'] == 0).sum()
    print(f"   - 波动率评分为0的股票数量: {volatility_zero_count}/{len(standalone_df)}")
    print(f"   - 这表明独立程序的波动率计算可能有问题")

if __name__ == "__main__":
    main()
