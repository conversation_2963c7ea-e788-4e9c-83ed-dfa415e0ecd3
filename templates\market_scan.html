{% extends "layout.html" %}

{% block title %}市场扫描 - 智能分析系统{% endblock %}

{% block content %}
<div class="page-transition">
    <!-- Enhanced Material Design 3 分析表单 -->
    <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
        <div class="md3-card-header">
            <h2 class="md3-card-title">
                <i class="material-icons">search</i> 市场扫描
            </h2>
            <p class="md3-card-subtitle">智能筛选高评分投资机会</p>
        </div>
        <div class="md3-card-body">
            <form id="scan-form" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px; align-items: end;">
                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="index-selector">
                        <option value="">-- 选择指数 --</option>
                        <option value="000300">沪深300</option>
                        <option value="000905">中证500</option>
                        <option value="000852">中证1000</option>
                        <option value="000001">上证指数</option>
                    </select>
                    <label class="md3-text-field-label">选择指数</label>
                </div>
                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="industry-selector">
                        <option value="">-- 选择行业 --</option>
                        <option value="保险">保险</option>
                        <option value="食品饮料">食品饮料</option>
                        <option value="多元金融">多元金融</option>
                        <option value="游戏">游戏</option>
                        <option value="酿酒行业">酿酒行业</option>
                        <option value="商业百货">商业百货</option>
                        <option value="证券">证券</option>
                        <option value="船舶制造">船舶制造</option>
                        <option value="家用轻工">家用轻工</option>
                        <option value="旅游酒店">旅游酒店</option>
                        <option value="美容护理">美容护理</option>
                        <option value="医疗服务">医疗服务</option>
                        <option value="软件开发">软件开发</option>
                        <option value="化学制药">化学制药</option>
                        <option value="医疗器械">医疗器械</option>
                        <option value="家电行业">家电行业</option>
                        <option value="汽车服务">汽车服务</option>
                        <option value="造纸印刷">造纸印刷</option>
                        <option value="纺织服装">纺织服装</option>
                        <option value="光伏设备">光伏设备</option>
                        <option value="房地产服务">房地产服务</option>
                        <option value="文化传媒">文化传媒</option>
                        <option value="医药商业">医药商业</option>
                        <option value="中药">中药</option>
                        <option value="专业服务">专业服务</option>
                        <option value="生物制品">生物制品</option>
                        <option value="仪器仪表">仪器仪表</option>
                        <option value="房地产开发">房地产开发</option>
                        <option value="教育">教育</option>
                        <option value="半导体">半导体</option>
                        <option value="玻璃玻纤">玻璃玻纤</option>
                        <option value="汽车整车">汽车整车</option>
                        <option value="消费电子">消费电子</option>
                        <option value="贸易行业">贸易行业</option>
                        <option value="包装材料">包装材料</option>
                        <option value="汽车零部件">汽车零部件</option>
                        <option value="电子化学品">电子化学品</option>
                        <option value="电子元件">电子元件</option>
                        <option value="装修建材">装修建材</option>
                        <option value="交运设备">交运设备</option>
                        <option value="农牧饲渔">农牧饲渔</option>
                        <option value="塑料制品">塑料制品</option>
                        <option value="珠宝首饰">珠宝首饰</option>
                        <option value="贵金属">贵金属</option>
                        <option value="非金属材料">非金属材料</option>
                        <option value="装修装饰">装修装饰</option>
                        <option value="风电设备">风电设备</option>
                        <option value="工程咨询服务">工程咨询服务</option>
                        <option value="专用设备">专用设备</option>
                        <option value="光学光电子">光学光电子</option>
                        <option value="航空机场">航空机场</option>
                        <option value="小金属">小金属</option>
                        <option value="物流行业">物流行业</option>
                        <option value="通用设备">通用设备</option>
                        <option value="计算机设备">计算机设备</option>
                        <option value="环保行业">环保行业</option>
                        <option value="航运港口">航运港口</option>
                        <option value="通信设备">通信设备</option>
                        <option value="水泥建材">水泥建材</option>
                        <option value="电池">电池</option>
                        <option value="化肥行业">化肥行业</option>
                        <option value="互联网服务">互联网服务</option>
                        <option value="工程建设">工程建设</option>
                        <option value="橡胶制品">橡胶制品</option>
                        <option value="化学原料">化学原料</option>
                        <option value="化纤行业">化纤行业</option>
                        <option value="农药兽药">农药兽药</option>
                        <option value="化学制品">化学制品</option>
                        <option value="能源金属">能源金属</option>
                        <option value="有色金属">有色金属</option>
                        <option value="采掘行业">采掘行业</option>
                        <option value="燃气">燃气</option>
                        <option value="综合行业">综合行业</option>
                        <option value="工程机械">工程机械</option>
                        <option value="银行">银行</option>
                        <option value="铁路公路">铁路公路</option>
                        <option value="石油行业">石油行业</option>
                        <option value="公用事业">公用事业</option>
                        <option value="电机">电机</option>
                        <option value="通信服务">通信服务</option>
                        <option value="钢铁行业">钢铁行业</option>
                        <option value="电力行业">电力行业</option>
                        <option value="电网设备">电网设备</option>
                        <option value="煤炭行业">煤炭行业</option>
                        <option value="电源设备">电源设备</option>
                        <option value="航天航空">航天航空</option>
                    </select>
                    <label class="md3-text-field-label">选择行业</label>
                </div>

                <div class="md3-text-field md3-text-field-outlined">
                    <input type="text" class="md3-text-field-input" id="custom-stocks" placeholder=" ">
                    <label class="md3-text-field-label">自定义股票</label>
                    <div class="md3-text-field-supporting-text">多个股票代码用逗号分隔</div>
                </div>

                <div style="display: flex; gap: 12px; align-items: end;">
                    <div class="md3-text-field md3-text-field-outlined" style="flex: 1;">
                        <input type="number" class="md3-text-field-input" id="min-score" value="60" min="0" max="100" placeholder=" ">
                        <label class="md3-text-field-label">最低分数</label>
                    </div>
                    <button type="submit" class="md3-button md3-button-filled md3-button-large">
                        <i class="material-icons">search</i> 开始扫描
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Material Design 3 扫描结果区域 -->
    <div class="md3-card md3-card-elevated md3-animate-fade-in">
        <div class="md3-card-header">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div>
                    <h3 class="md3-card-title">
                        <i class="material-icons">list_alt</i> 扫描结果
                    </h3>
                    <p class="md3-card-subtitle">高评分投资机会筛选结果</p>
                </div>
                <div style="display: flex; align-items: center; gap: 12px;">
                    <span class="md3-badge md3-badge-primary" id="result-count">0</span>
                    <button class="md3-button md3-button-outlined md3-button-small" id="export-btn" style="display: none;">
                        <i class="material-icons">download</i> 导出结果
                    </button>
                </div>
            </div>
        </div>
        <div class="md3-card-body">
            <!-- Enhanced Material Design 3 Loading Panel -->
            <div id="scan-loading" style="display: none; text-align: center; padding: 64px 32px;">
                <div class="md3-progress-indicator" style="margin-bottom: 24px;"></div>
                <p id="scan-message" style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); margin: 0 0 16px 0;">正在扫描市场，请稍候...</p>
                <div style="width: 100%; height: 8px; background-color: var(--md-sys-color-surface-container-highest); border-radius: 4px; margin: 24px 0; overflow: hidden;">
                    <div style="height: 100%; background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-secondary)); border-radius: 4px; animation: progress-animation 2s ease-in-out infinite;"></div>
                </div>
                <button id="cancel-scan-btn" class="md3-button md3-button-outlined">
                    <i class="material-icons">close</i> 取消扫描
                </button>
            </div>

            <!-- Enhanced Material Design 3 Error Retry Panel -->
            <div id="scan-error-retry" style="display: none; text-align: center; padding: 32px;">
                <button id="scan-retry-button" class="md3-button md3-button-filled">
                    <i class="material-icons">refresh</i> 重试扫描
                </button>
                <p style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); margin: 16px 0 0 0;">
                    系统繁忙，请稍后重试
                </p>
            </div>

            <!-- Enhanced Material Design 3 Results Table -->
            <div id="scan-results" style="padding: 0;">
                <div style="overflow-x: auto;">
                    <table class="md3-data-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="stock_code" data-type="text" style="text-align: left; cursor: pointer; user-select: none;">
                                    代码 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="stock_name" data-type="text" style="text-align: left; cursor: pointer; user-select: none;">
                                    名称 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="industry" data-type="text" style="text-align: left; cursor: pointer; user-select: none;">
                                    行业 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="score" data-type="number" style="text-align: center; cursor: pointer; user-select: none;">
                                    得分 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="price" data-type="number" style="text-align: right; cursor: pointer; user-select: none;">
                                    价格 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="price_change" data-type="number" style="text-align: center; cursor: pointer; user-select: none;">
                                    涨跌幅 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="rsi" data-type="number" style="text-align: center; cursor: pointer; user-select: none;">
                                    RSI <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="ma_trend" data-type="text" style="text-align: center; cursor: pointer; user-select: none;">
                                    MA趋势 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="volume_status" data-type="text" style="text-align: center; cursor: pointer; user-select: none;">
                                    成交量 <span class="sort-indicator"></span>
                                </th>
                                <th class="sortable" data-column="recommendation" data-type="text" style="text-align: center; cursor: pointer; user-select: none;">
                                    建议 <span class="sort-indicator"></span>
                                </th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="results-table">
                            <tr>
                                <td colspan="11" style="text-align: center; padding: 48px; color: var(--md-sys-color-on-surface-variant);">暂无数据，请开始扫描</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes progress-animation {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* 表头排序样式 */
.sortable {
    position: relative;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: var(--md-sys-color-surface-variant) !important;
}

.sort-indicator {
    display: inline-block;
    margin-left: 8px;
    font-size: 16px;
    color: var(--md-sys-color-outline);
    transition: color 0.2s ease;
}

.sort-indicator:before {
    content: '↕';
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 16px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
}

.sortable.sort-asc .sort-indicator:before {
    content: '↑';
    color: var(--md-sys-color-primary);
}

.sortable.sort-desc .sort-indicator:before {
    content: '↓';
    color: var(--md-sys-color-primary);
}

.sortable.sort-asc .sort-indicator,
.sortable.sort-desc .sort-indicator {
    color: var(--md-sys-color-primary);
}
</style>
{% endblock %}

{% block scripts %}
<script>
    // 全局变量
    let currentTaskId = null;

    // 获取指数成分股
    function fetchIndexStocks(indexCode) {
        console.log('开始获取指数成分股:', indexCode);
        $('#scan-loading').show();
        $('#scan-results').hide();

        $.ajax({
            url: `/api/index_stocks?index_code=${indexCode}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('指数成分股获取成功:', response);
                const stockList = response.stock_list;
                if (stockList && stockList.length > 0) {
                    window.lastScanList = stockList;
                    scanMarket(stockList);
                } else {
                    $('#scan-loading').hide();
                    $('#scan-results').show();
                    showError('获取指数成分股失败，或成分股列表为空');
                }
            },
            error: function(error) {
                console.error('指数成分股获取失败:', error);
                $('#scan-loading').hide();
                $('#scan-results').show();
                showError('获取指数成分股失败: ' + (error.responseJSON ? error.responseJSON.error : error.statusText));
            }
        });
    }

    // 获取行业成分股
    function fetchIndustryStocks(industry) {
        console.log('开始获取行业成分股:', industry);
        $('#scan-loading').show();
        $('#scan-results').hide();

        $.ajax({
            url: `/api/industry_stocks?industry=${encodeURIComponent(industry)}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('行业成分股获取成功:', response);
                const stockList = response.stock_list;
                if (stockList && stockList.length > 0) {
                    window.lastScanList = stockList;
                    scanMarket(stockList);
                } else {
                    $('#scan-loading').hide();
                    $('#scan-results').show();
                    showError('获取行业成分股失败，或成分股列表为空');
                }
            },
            error: function(error) {
                console.error('行业成分股获取失败:', error);
                $('#scan-loading').hide();
                $('#scan-results').show();
                showError('获取行业成分股失败: ' + (error.responseJSON ? error.responseJSON.error : error.statusText));
            }
        });
    }

    // 扫描市场
    function scanMarket(stockList) {
        console.log('开始扫描市场，股票数量:', stockList.length);
        $('#scan-loading').show();
        $('#scan-results').hide();
        $('#scan-error-retry').hide();

        const minScore = parseInt($('#min-score').val() || 60);
        $('#scan-message').html(`正在准备扫描${stockList.length}只股票，请稍候...`);

        $.ajax({
            url: '/api/start_market_scan',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_list: stockList,
                min_score: minScore,
                market_type: 'A'
            }),
            success: function(response) {
                console.log('扫描任务启动成功:', response);
                const taskId = response.task_id;
                currentTaskId = taskId;

                if (!taskId) {
                    showError('启动扫描任务失败：未获取到任务ID');
                    $('#scan-loading').hide();
                    $('#scan-results').show();
                    $('#scan-error-retry').show();
                    return;
                }

                $('#cancel-scan-btn').show();
                pollScanStatus(taskId);
            },
            error: function(xhr, status, error) {
                console.error('扫描任务启动失败:', xhr, status, error);
                $('#scan-loading').hide();
                $('#scan-results').show();

                let errorMsg = '启动扫描任务失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }

                showError(errorMsg);
                $('#scan-error-retry').show();
            }
        });
    }

    // 轮询扫描任务状态 - 优化版
    function pollScanStatus(taskId) {
        console.log('开始轮询任务状态:', taskId);
        const startTime = Date.now();
        let pollCount = 0;
        let retryCount = 0;
        const maxRetries = 10;
        const pollInterval = 30000; // 30秒间隔

        function checkStatus() {
            pollCount++;
            const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);

            console.log(`轮询 #${pollCount}: 任务 ${taskId}, 已耗时 ${elapsedSeconds}秒`);

            $.ajax({
                url: `/api/scan_status/${taskId}`,
                type: 'GET',
                timeout: 10000, // 10秒超时
                success: function(response) {
                    console.log('任务状态响应:', response);
                    consecutiveErrors = 0; // 重置错误计数

                    const progress = response.progress || 0;

                    // 更新进度消息
                    let progressHtml = `正在扫描市场...<br>
                        进度: ${progress}% 完成<br>
                        已处理: ${response.processed || 0} / ${response.total || 0} 只股票<br>
                        耗时: ${elapsedSeconds}秒`;

                    if (response.found !== undefined) {
                        progressHtml += `<br>找到符合条件: ${response.found} 只`;
                    }

                    if (response.failed !== undefined && response.failed > 0) {
                        progressHtml += `<br>失败: ${response.failed} 只`;
                    }

                    $('#scan-message').html(progressHtml);

                    if (response.status === 'completed') {
                        console.log('扫描完成，结果数量:', response.result ? response.result.length : 0);
                        $('#cancel-scan-btn').hide();
                        currentTaskId = null;

                        renderResults(response.result || []);
                        $('#scan-loading').hide();
                        $('#scan-results').show();

                        if (!response.result || response.result.length === 0) {
                            showInfo('扫描完成，但未找到符合条件的股票');
                        } else {
                            showSuccess(`扫描完成！找到 ${response.result.length} 只符合条件的股票`);
                        }

                    } else if (response.status === 'failed') {
                        console.error('扫描任务失败:', response.error);
                        $('#cancel-scan-btn').hide();
                        currentTaskId = null;
                        $('#scan-loading').hide();
                        $('#scan-results').show();
                        showError('扫描任务失败: ' + (response.error || '未知错误'));
                        $('#scan-error-retry').show();

                    } else if (response.status === 'cancelled') {
                        console.log('扫描任务已取消');
                        $('#cancel-scan-btn').hide();
                        currentTaskId = null;
                        $('#scan-loading').hide();
                        $('#scan-results').show();
                        showInfo('扫描任务已取消');
                        $('#scan-error-retry').show();

                    } else {
                        // 继续轮询，使用30秒间隔
                        setTimeout(checkStatus, pollInterval);
                    }
                },
                error: function(xhr, status, error) {
                    retryCount++;
                    console.error(`轮询状态失败 (第${retryCount}次):`, {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        error: error,
                        response: xhr.responseText
                    });

                    if (retryCount <= maxRetries) {
                        // 继续重试，使用固定30秒间隔
                        console.log(`轮询错误重试，${pollInterval/1000}秒后重试`);
                        setTimeout(checkStatus, pollInterval);
                        return;
                    } else {
                        // 重试次数用尽，但不清理状态，继续30秒后重试
                        console.warn('重试次数用尽，但继续保持轮询状态');
                        retryCount = 0; // 重置重试计数，继续轮询
                        console.log(`重置重试计数，${pollInterval/1000}秒后继续轮询`);
                        setTimeout(checkStatus, pollInterval);
                        return;
                    }
                }
            });
        }

        // 立即开始检查
        checkStatus();
    }

    // 渲染扫描结果
    function renderResults(results) {
        console.log('渲染结果:', results);
        if (!results || results.length === 0) {
            $('#results-table').html('<tr><td colspan="11" class="text-center">未找到符合条件的股票</td></tr>');
            $('#result-count').text('0');
            $('#export-btn').hide();
            return;
        }

        let html = '';
        results.forEach(result => {
            const scoreClass = getMD3ScoreColorClass(result.score);
            const maTrendClass = getMD3TrendColorClass(result.ma_trend);
            const maTrendIcon = getTrendIcon(result.ma_trend);
            const priceChangeClass = result.price_change >= 0 ? 'md3-text-bull' : 'md3-text-bear';
            const priceChangeIcon = result.price_change >= 0 ? '<i class="material-icons">arrow_upward</i>' : '<i class="material-icons">arrow_downward</i>';

            html += `
                <tr>
                    <td style="text-align: left;">${result.stock_code}</td>
                    <td style="text-align: left;">${result.stock_name || '未知'}</td>
                    <td style="text-align: left;">${result.industry || '-'}</td>
                    <td style="text-align: center;"><span class="badge ${scoreClass}">${result.score}</span></td>
                    <td style="text-align: right;">${formatNumber(result.price)}</td>
                    <td style="text-align: center;" class="${priceChangeClass}">${priceChangeIcon} ${formatPercent(result.price_change)}</td>
                    <td style="text-align: center;">${formatNumber(result.rsi)}</td>
                    <td style="text-align: center;" class="${maTrendClass}">${maTrendIcon} ${result.ma_trend}</td>
                    <td style="text-align: center;">${result.volume_status}</td>
                    <td style="text-align: center;">${result.recommendation}</td>
                    <td style="text-align: center;">
                        <a href="/stock_detail/${result.stock_code}" class="btn btn-sm btn-primary">
                            <i class="fas fa-chart-line"></i> 详情
                        </a>
                    </td>
                </tr>
            `;
        });

        $('#results-table').html(html);
        $('#result-count').text(results.length);
        $('#export-btn').show();

        // 存储原始数据用于排序
        tableData = results.map(result => ({
            stock_code: result.stock_code,
            stock_name: result.stock_name || '未知',
            industry: result.industry || '-',
            score: result.score,
            price: result.price,
            price_change: result.price_change,
            rsi: result.rsi,
            ma_trend: result.ma_trend,
            volume_status: result.volume_status,
            recommendation: result.recommendation
        }));

        // 重置排序状态
        currentSortColumn = null;
        currentSortDirection = 'asc';

        // 立即初始化排序功能，无需延迟
        initTableSort();

        // 清除所有排序指示器
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
        });

        // 强制重新计算表格布局
        const table = document.querySelector('.md3-data-table');
        if (table) {
            table.style.tableLayout = 'fixed';
            // 触发重排
            table.offsetHeight;
            table.style.tableLayout = 'auto';
        }
    }

    // 取消扫描任务
    function cancelScan(taskId) {
        if (!taskId) return;

        if (!confirm('确定要取消当前扫描任务吗？')) return;

        $.ajax({
            url: `/api/cancel_scan/${taskId}`,
            type: 'POST',
            success: function(response) {
                $('#scan-loading').hide();
                $('#scan-results').show();
                showInfo('扫描任务已取消');
                $('#scan-error-retry').show();
                $('#cancel-scan-btn').hide();
                currentTaskId = null;
            },
            error: function(xhr, status, error) {
                console.error('取消扫描任务失败:', error);
                showError('取消任务失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : error));
            }
        });
    }

    // 导出到CSV
    function exportToCSV() {
        const table = document.querySelector('#scan-results table');
        let csv = [];
        let rows = table.querySelectorAll('tr');

        for (let i = 0; i < rows.length; i++) {
            let row = [], cols = rows[i].querySelectorAll('td, th');
            for (let j = 0; j < cols.length - 1; j++) {
                let text = cols[j].innerText.replace(/(\r\n|\n|\r)/gm, '').replace(/,/g, '，');
                row.push(text);
            }
            csv.push(row.join(','));
        }

        const csvString = csv.join('\n');
        const filename = '市场扫描结果_' + new Date().toISOString().slice(0, 10) + '.csv';
        const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 事件绑定
    $(document).ready(function() {
        // 表单提交
        $('#scan-form').submit(function(e) {
            e.preventDefault();
            console.log('表单提交');

            const indexCode = $('#index-selector').val();
            if (indexCode) {
                fetchIndexStocks(indexCode);
                return;
            }

            const industry = $('#industry-selector').val();
            if (industry) {
                fetchIndustryStocks(industry);
                return;
            }

            const customStocks = $('#custom-stocks').val().trim();
            if (customStocks) {
                const stockList = customStocks.split(',').map(s => s.trim());
                scanMarket(stockList);
            } else {
                showError('请至少选择一种方式获取股票列表');
            }
        });

        // 指数选择变化
        $('#index-selector').change(function() {
            if ($(this).val()) {
                $('#industry-selector').val('');
            }
        });

        // 行业选择变化
        $('#industry-selector').change(function() {
            if ($(this).val()) {
                $('#index-selector').val('');
            }
        });

        // 导出结果
        $('#export-btn').click(function() {
            exportToCSV();
        });

        // 取消按钮
        $('#cancel-scan-btn').click(function() {
            if (currentTaskId) {
                cancelScan(currentTaskId);
            } else {
                $('#scan-loading').hide();
                $('#scan-results').show();
            }
        });

        console.log('页面初始化完成');
    });
    // 数值格式化函数
    function formatPercent(value) {
        if (value === null || value === undefined || value === '') {
            return '--';
        }
        const num = parseFloat(value);
        if (isNaN(num)) {
            return '--';
        }
        return num.toFixed(2) + '%';
    }

    function formatNumber(value) {
        if (value === null || value === undefined || value === '') {
            return '--';
        }
        const num = parseFloat(value);
        if (isNaN(num)) {
            return '--';
        }
        return num.toFixed(2);
    }

    // 表格排序功能
    let currentSortColumn = null;
    let currentSortDirection = 'asc';
    let tableData = []; // 存储原始数据

    // 初始化表格排序功能
    function initTableSort() {
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-column');
                const type = this.getAttribute('data-type');
                sortTable(column, type);
            });
        });
    }

    // 排序表格
    function sortTable(column, type) {
        // 确定排序方向
        if (currentSortColumn === column) {
            currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            currentSortDirection = 'asc';
        }
        currentSortColumn = column;

        // 更新排序指示器
        updateSortIndicators();

        // 排序数据
        const sortedData = [...tableData].sort((a, b) => {
            let valueA = a[column];
            let valueB = b[column];

            if (type === 'number') {
                valueA = parseFloat(valueA) || 0;
                valueB = parseFloat(valueB) || 0;
            } else {
                valueA = String(valueA).toLowerCase();
                valueB = String(valueB).toLowerCase();
            }

            if (currentSortDirection === 'asc') {
                return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
            } else {
                return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
            }
        });

        // 重新渲染表格
        renderSortedTable(sortedData);
    }

    // 更新排序指示器
    function updateSortIndicators() {
        const sortableHeaders = document.querySelectorAll('.sortable');
        sortableHeaders.forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (header.getAttribute('data-column') === currentSortColumn) {
                header.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        });
    }

    // 渲染排序后的表格
    function renderSortedTable(sortedData) {
        let html = '';
        if (!sortedData || sortedData.length === 0) {
            html = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
        } else {
            sortedData.forEach((item, index) => {
                // 使用中国股市习惯：上涨红色向上箭头，下跌绿色向下箭头
                const changePercent = parseFloat(item.price_change) || 0;
                const changeIcon = changePercent >= 0 ?
                    '<i class="material-icons" style="color: #d32f2f; font-size: 16px;">arrow_upward</i>' :
                    '<i class="material-icons" style="color: #2e7d32; font-size: 16px;">arrow_downward</i>';
                const changeColor = changePercent >= 0 ? '#d32f2f' : '#2e7d32';

                html += `
                    <tr>
                        <td style="text-align: left;">${item.stock_code}</td>
                        <td style="text-align: left;">${item.stock_name}</td>
                        <td style="text-align: left;">${item.industry || '--'}</td>
                        <td style="text-align: center;">${item.score || '--'}</td>
                        <td style="text-align: right;">${item.price || '--'}</td>
                        <td style="text-align: center;">
                            <span style="color: ${changeColor}; font-weight: 500;">
                                ${changeIcon} ${formatPercent(item.price_change)}
                            </span>
                        </td>
                        <td style="text-align: center;">${item.rsi || '--'}</td>
                        <td style="text-align: center;">${item.ma_trend || '--'}</td>
                        <td style="text-align: center;">${item.volume_status || '--'}</td>
                        <td style="text-align: center;">${item.recommendation || '--'}</td>
                        <td style="text-align: center;">
                            <a href="/stock_detail/${item.stock_code}" target="_blank" class="md3-button md3-button-outlined md3-button-small">
                                <i class="material-icons">analytics</i>
                            </a>
                        </td>
                    </tr>
                `;
            });
        }

        $('#results-table').html(html);
    }





</script>
{% endblock %}
