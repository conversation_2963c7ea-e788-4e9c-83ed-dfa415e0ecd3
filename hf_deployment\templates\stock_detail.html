{% extends "layout.html" %}

{% block title %}股票详情 - {{ stock_code }} - 智能分析系统{% endblock %}

{% block head %}
<style>
    /* Enhanced Material Design 3 Stock Detail Styles */
    .stock-detail-header {
        background: linear-gradient(135deg, var(--md-sys-color-primary-container) 0%, var(--md-sys-color-secondary-container) 100%);
        border-radius: var(--md-sys-shape-corner-large);
        padding: 32px;
        margin-bottom: 32px;
        position: relative;
        overflow: hidden;
    }

    .stock-detail-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .stock-price-display {
        font-family: var(--md-sys-typescale-financial-large-font);
        font-size: var(--md-sys-typescale-financial-large-size);
        font-weight: var(--md-sys-typescale-financial-large-weight);
        color: var(--md-sys-color-on-primary-container);
        margin-bottom: 8px;
    }

    .stock-change-display {
        font-family: var(--md-sys-typescale-financial-medium-font);
        font-size: var(--md-sys-typescale-financial-medium-size);
        font-weight: var(--md-sys-typescale-financial-medium-weight);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .stock-controls {
        display: flex;
        gap: 16px;
        align-items: center;
        flex-wrap: wrap;
    }

    /* 评分加载状态样式 */
    .score-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        opacity: 0.7;
    }

    .score-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        color: var(--md-sys-color-error);
    }

    .score-error .material-icons {
        margin-bottom: 16px;
        opacity: 0.6;
    }

    /* 评分加载动画 */
    @keyframes scoreLoading {
        0% { opacity: 0.3; }
        50% { opacity: 0.8; }
        100% { opacity: 0.3; }
    }

    .score-loading .shimmer-skeleton {
        animation: scoreLoading 1.5s ease-in-out infinite;
    }
    }

    .stock-metrics-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 32px;
    }

    @media (max-width: 768px) {
        .stock-metrics-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .metric-card {
        background: var(--md-sys-color-surface-container);
        border-radius: var(--md-sys-shape-corner-medium);
        padding: 24px;
        text-align: center;
        transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
        border: 1px solid var(--md-sys-color-outline-variant);
    }

    .metric-card:hover {
        background: var(--md-sys-color-surface-container-high);
        transform: translateY(-4px);
        box-shadow: var(--md-sys-elevation-level2);
    }

    .metric-value {
        font-family: var(--md-sys-typescale-financial-medium-font);
        font-size: var(--md-sys-typescale-financial-medium-size);
        font-weight: var(--md-sys-typescale-financial-medium-weight);
        color: var(--md-sys-color-on-surface);
        margin-bottom: 8px;
    }

    .metric-label {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
    }

    .chart-tabs {
        margin-bottom: 32px;
    }

    .chart-container {
        background: var(--md-sys-color-surface);
        border-radius: var(--md-sys-shape-corner-large);
        border: 1px solid var(--md-sys-color-outline-variant);
        overflow: hidden;
    }

    .ai-analysis-section {
        background: var(--md-sys-color-surface-container-low);
        border-radius: var(--md-sys-shape-corner-large);
        padding: 32px;
        position: relative;
        min-height: 300px;
    }

    .ai-loader {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: rgba(var(--md-sys-color-surface-rgb), 0.9);
        backdrop-filter: blur(8px);
        border-radius: var(--md-sys-shape-corner-large);
        z-index: 10;
    }

    .typing-animation {
        display: flex;
        gap: 4px;
        margin-top: 16px;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        background: var(--md-sys-color-primary);
        border-radius: 50%;
        animation: typing-bounce 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typing-bounce {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1.2); opacity: 1; }
    }

    .shimmer-skeleton {
        background: linear-gradient(90deg, var(--md-sys-color-surface-container) 25%, var(--md-sys-color-surface-container-high) 50%, var(--md-sys-color-surface-container) 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: var(--md-sys-shape-corner-small);
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .score-circle {
        position: relative;
        width: 160px;
        height: 160px;
        margin: 0 auto;
    }

    .score-display {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }

    .score-number {
        font-family: var(--md-sys-typescale-financial-large-font);
        font-size: 48px;
        font-weight: var(--md-sys-typescale-financial-large-weight);
        color: var(--md-sys-color-primary);
        line-height: 1;
    }

    .score-text {
        font-family: var(--md-sys-typescale-body-small-font);
        font-size: var(--md-sys-typescale-body-small-size);
        color: var(--md-sys-color-on-surface-variant);
        margin-top: 4px;
    }

    .support-resistance-table {
        width: 100%;
        border-collapse: collapse;
    }

    .support-resistance-table th,
    .support-resistance-table td {
        padding: 12px 16px;
        text-align: center;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .support-resistance-table th {
        background: var(--md-sys-color-surface-container);
        color: var(--md-sys-color-on-surface);
        font-family: var(--md-sys-typescale-title-small-font);
        font-size: var(--md-sys-typescale-title-small-size);
        font-weight: var(--md-sys-typescale-title-small-weight);
    }

    .trend-indicator {
        padding: 8px 16px;
        border-radius: var(--md-sys-shape-corner-small);
        font-family: var(--md-sys-typescale-label-medium-font);
        font-size: var(--md-sys-typescale-label-medium-size);
        font-weight: var(--md-sys-typescale-label-medium-weight);
    }

    .trend-up {
        background: var(--md-sys-color-bull-container);
        color: var(--md-sys-color-on-bull-container);
        border-left: 4px solid var(--md-sys-color-bull);
    }

    .trend-down {
        background: var(--md-sys-color-bear-container);
        color: var(--md-sys-color-on-bear-container);
        border-left: 4px solid var(--md-sys-color-bear);
    }
</style>
{% endblock %}

{% block content %}
<div class="page-transition">
    <div id="alerts-container"></div>

    <!-- Enhanced Material Design 3 股票详情头部 -->
    <div class="stock-detail-header md3-animate-fade-in">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; position: relative; z-index: 1;">
            <div style="flex: 1;">
                <div id="stock-title" style="margin-bottom: 16px;">
                    <div class="shimmer-skeleton" style="width: 300px; height: 32px; margin-bottom: 8px;"></div>
                    <div class="shimmer-skeleton" style="width: 200px; height: 20px;"></div>
                </div>
                <div id="stock-price-section" style="margin-bottom: 24px;">
                    <div id="price-container">
                        <div class="shimmer-skeleton" style="width: 200px; height: 48px; margin-bottom: 8px;"></div>
                        <div class="shimmer-skeleton" style="width: 150px; height: 24px;"></div>
                    </div>
                </div>
                <div id="stock-basic-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 16px;">
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">今开</div>
                        <div id="open-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">最高</div>
                        <div id="high-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">最低</div>
                        <div id="low-price" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                    <div>
                        <div style="color: var(--md-sys-color-on-primary-container); opacity: 0.7; font-size: 14px; margin-bottom: 4px;">成交量</div>
                        <div id="volume-info" class="shimmer-skeleton" style="width: 80px; height: 20px;"></div>
                    </div>
                </div>
            </div>
            <div class="stock-controls">
                <div class="md3-text-field md3-text-field-outlined md3-text-field-small">
                    <select class="md3-text-field-input" id="market-type">
                        <option value="A" {% if market_type == 'A' %}selected{% endif %}>A股</option>
                        <option value="HK" {% if market_type == 'HK' %}selected{% endif %}>港股</option>
                        <option value="US" {% if market_type == 'US' %}selected{% endif %}>美股</option>
                    </select>
                    <label class="md3-text-field-label">市场</label>
                </div>
                <div class="md3-text-field md3-text-field-outlined md3-text-field-small">
                    <select class="md3-text-field-input" id="analysis-period">
                        <option value="1m">1个月</option>
                        <option value="3m">3个月</option>
                        <option value="6m">6个月</option>
                        <option value="1y" selected>1年</option>
                    </select>
                    <label class="md3-text-field-label">周期</label>
                </div>
                <button id="refresh-btn" class="md3-button md3-button-filled md3-button-small">
                    <i class="material-icons">refresh</i>
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 系统指标区域 -->
    <div id="system-indicators">
        <!-- 综合评分和关键指标 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 综合评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">analytics</i> 综合评分
                    </h3>
                    <p class="md3-card-subtitle">基于多维度分析的投资评分</p>
                </div>
                <div class="md3-card-body" style="text-align: center; padding: 32px;">
                    <div id="score-container">
                        <div class="score-circle">
                            <div class="shimmer-skeleton" style="width: 160px; height: 160px; border-radius: 50%;"></div>
                            <div class="score-display">
                                <div class="shimmer-skeleton" style="width: 60px; height: 48px; margin: 0 auto 8px;"></div>
                                <div class="shimmer-skeleton" style="width: 80px; height: 16px; margin: 0 auto;"></div>
                            </div>
                        </div>
                    </div>
                    <div id="recommendation-container" style="margin-top: 24px;">
                        <div class="shimmer-skeleton" style="width: 120px; height: 32px; margin: 0 auto;"></div>
                    </div>
                </div>
            </div>

            <!-- 关键指标卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">speed</i> 关键指标
                    </h3>
                    <p class="md3-card-subtitle">重要技术指标快速概览</p>
                </div>
                <div class="md3-card-body">
                    <div class="stock-metrics-grid">
                        <div class="metric-card" id="rsi-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="ma-trend-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="macd-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                        <div class="metric-card" id="volume-trend-container">
                            <div class="shimmer-skeleton" style="width: 60px; height: 24px; margin: 0 auto 8px;"></div>
                            <div class="shimmer-skeleton" style="width: 40px; height: 16px; margin: 0 auto;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Material Design 3 图表区域 -->
        <div class="md3-card md3-card-elevated chart-container md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <h3 class="md3-card-title">
                    <i class="material-icons">show_chart</i> 技术分析图表
                </h3>
                <p class="md3-card-subtitle">价格走势与技术指标分析</p>
            </div>

            <!-- Enhanced Material Design 3 Chart Tabs -->
            <div class="chart-tabs">
                <div class="md3-tabs">
                    <div class="md3-tab-bar">
                        <button class="md3-tab md3-tab-active" id="price-tab" data-target="price-content">
                            <i class="material-icons">candlestick_chart</i>
                            <span>价格趋势</span>
                        </button>
                        <button class="md3-tab" id="indicators-tab" data-target="indicators-content">
                            <i class="material-icons">analytics</i>
                            <span>技术指标</span>
                        </button>
                        <button class="md3-tab" id="volume-tab" data-target="volume-content">
                            <i class="material-icons">bar_chart</i>
                            <span>成交量</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="md3-card-body" style="padding: 0;">
                <div class="md3-tab-content">
                    <div class="md3-tab-panel md3-tab-panel-active" id="price-content">
                        <div id="price-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                    <div class="md3-tab-panel" id="indicators-content">
                        <div id="indicators-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                    <div class="md3-tab-panel" id="volume-content">
                        <div id="volume-chart" style="height: 450px; padding: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Material Design 3 支撑压力位和雷达图 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 支撑与压力位卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">support</i> 支撑与压力位
                    </h3>
                    <p class="md3-card-subtitle">关键价格支撑与阻力位分析</p>
                </div>
                <div class="md3-card-body">
                    <div id="support-resistance-container">
                        <div class="shimmer-skeleton" style="width: 100%; height: 200px;"></div>
                    </div>
                </div>
            </div>

            <!-- 多维度评分卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right">
                <div class="md3-card-header">
                    <h3 class="md3-card-title">
                        <i class="material-icons">radar</i> 多维度评分
                    </h3>
                    <p class="md3-card-subtitle">技术面、基本面、资金面综合分析</p>
                </div>
                <div class="md3-card-body">
                    <div id="radar-chart-container">
                        <div class="shimmer-skeleton" style="width: 100%; height: 250px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 AI分析区域 -->
    <div class="md3-card md3-card-elevated md3-animate-slide-in-up">
        <div class="md3-card-header">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div>
                    <h3 class="md3-card-title">
                        <i class="material-icons">psychology</i> AI智能分析报告
                    </h3>
                    <p class="md3-card-subtitle">基于深度学习的股票投资分析</p>
                </div>
                <div>
                    <span id="ai-analysis-status" class="md3-badge md3-badge-info">准备分析...</span>
                </div>
            </div>
        </div>
        <div class="md3-card-body">
            <div class="ai-analysis-section">
                <!-- Enhanced Material Design 3 AI分析加载动画 -->
                <div id="ai-analysis-loader" class="ai-loader">
                    <div class="md3-progress-indicator" style="margin-bottom: 24px;"></div>
                    <h4 style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 16px;">
                        正在对 <span id="ai-analysis-stock-name" style="color: var(--md-sys-color-primary);">该股票</span> 进行智能分析
                    </h4>
                    <p style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); margin-bottom: 16px;">
                        已用时 <span id="ai-processing-time" style="color: var(--md-sys-color-primary); font-weight: 500;">0</span> 秒
                    </p>
                    <div class="typing-animation">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <div style="width: 300px; height: 8px; background: var(--md-sys-color-surface-container-high); border-radius: var(--md-sys-shape-corner-small); margin: 24px 0; overflow: hidden;">
                        <div id="ai-progress-bar" style="height: 100%; background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-secondary)); width: 0%; transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);"></div>
                    </div>
                    <button id="cancel-analysis-btn" class="md3-button md3-button-outlined md3-button-small">
                        <i class="material-icons">close</i> 取消分析
                    </button>
                </div>

                <!-- AI分析内容 -->
                <div id="ai-analysis-content" style="opacity: 0; transition: opacity var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);">
                    <div id="ai-analysis" style="font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); line-height: 1.6; color: var(--md-sys-color-on-surface);">
                        <!-- AI分析内容将异步填充 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 错误重试区域 -->
    <div id="error-retry" style="display: none; margin-top: 32px;">
        <div class="md3-card md3-card-elevated" style="background: var(--md-sys-color-error-container); border: 1px solid var(--md-sys-color-error);">
            <div class="md3-card-body" style="text-align: center; padding: 32px;">
                <i class="material-icons" style="font-size: 48px; color: var(--md-sys-color-on-error-container); margin-bottom: 16px;">warning</i>
                <h4 style="color: var(--md-sys-color-on-error-container); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 16px;">分析过程中遇到错误</h4>
                <button id="retry-button" class="md3-button md3-button-filled">
                    <i class="material-icons">refresh</i> 重试分析
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const stockCode = '{{ stock_code }}';
    let marketType = '{{ market_type }}';
    let period = '1y';
    let stockData = [];
    let analysisResult = null;
    let processingTimer;
    let processingTime = 0;
    
    // 页面加载时的闪烁效果
    function addShimmerEffect() {
        let style = document.createElement('style');
        style.innerHTML = `
            .shimmer-bg {
                background: #f6f7f8;
                background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
                background-repeat: no-repeat;
                background-size: 800px 104px;
                display: inline-block;
                position: relative;
                animation-duration: 1.5s;
                animation-fill-mode: forwards;
                animation-iteration-count: infinite;
                animation-name: shimmer;
                animation-timing-function: linear;
                border-radius: 4px;
            }
            @keyframes shimmer {
                0% { background-position: -468px 0 }
                100% { background-position: 468px 0 }
            }
        `;
        document.head.appendChild(style);
    }

    $(document).ready(function() {
        addShimmerEffect();
        
        // 初始加载
        loadStockData();

        // 刷新按钮点击事件
        $('#refresh-btn').click(function() {
            marketType = $('#market-type').val();
            period = $('#analysis-period').val();
            loadStockData();
        });

        // 市场类型改变事件
        $('#market-type').change(function() {
            marketType = $(this).val();
        });

        // 分析周期改变事件
        $('#analysis-period').change(function() {
            period = $(this).val();
        });
        
        // 取消分析按钮
        $('#cancel-analysis-btn').click(function() {
            cancelAnalysis();
        });
        
        // 重试按钮
        $('#retry-button').click(function() {
            $('#error-retry').hide();
            startAIAnalysis();
        });
    });

    // 加载股票数据
    function loadStockData() {
        resetUI();
        showSystemLoadingState();
        
        // 获取股票数据
        $.ajax({
            url: `/api/stock_data?stock_code=${stockCode}&market_type=${marketType}&period=${period}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (!response.data || response.data.length === 0) {
                    hideSystemLoadingState();
                    showError('未找到股票数据');
                    return;
                }

                stockData = response.data;
                
                // 渲染系统指标部分
                renderSystemIndicators();
                
                // 系统指标加载完成后，异步启动AI分析
                startAIAnalysis();
            },
            error: function(xhr, status, error) {
                hideSystemLoadingState();
                
                let errorMsg = '获取股票数据失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }
                showError(errorMsg);
            }
        });
    }
    
    // 重置UI状态
    function resetUI() {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        processingTime = 0;
        
        // 隐藏错误重试区域
        $('#error-retry').hide();
        
        // 重置AI分析区域
        $('#ai-analysis-status').text('准备分析...').removeClass('bg-success bg-danger').addClass('bg-info');
        $('#ai-analysis-content').css('opacity', '0');
        $('#ai-analysis-loader').show();
        $('#ai-progress-bar').css('width', '0%');
        $('#ai-processing-time').text('0');
    }
    
    // 显示系统指标加载状态
    function showSystemLoadingState() {
        // 已通过闪烁效果展示
    }
    
    // 隐藏系统指标加载状态
    function hideSystemLoadingState() {
        // 闪烁效果会在渲染实际内容时被替换
    }
    
    // 渲染系统指标部分
    function renderSystemIndicators() {
        try {
            if (!stockData || stockData.length === 0) {
                showError("无可用数据");
                return;
            }
            
            const latestData = stockData[stockData.length - 1];
            const prevData = stockData.length > 1 ? stockData[stockData.length - 2] : latestData;
            
            // 设置标题和基本信息
            updateStockInfo(latestData);
            
            // 渲染价格图表
            renderPriceChart();
            
            // 渲染指标图表
            renderIndicatorsChart();
            
            // 渲染成交量图表
            renderVolumeChart();
            
            // 获取或计算支撑压力位
            calculateAndRenderSupportResistance(latestData);
            
            // 调用后端API获取统一评分
            fetchAndRenderScore();
            
            // 隐藏加载状态
            hideSystemLoadingState();
        } catch (error) {
            console.error("渲染系统指标时出错:", error);
            showError("渲染系统指标时出错: " + error.message);
        }
    }

    // 添加到全局作用域
    function safeGet(obj, path, defaultValue) {
        if (!obj) return defaultValue;
        
        const props = path.split('.');
        let current = obj;
        
        for (let i = 0; i < props.length; i++) {
            if (current === undefined || current === null) {
                console.warn(`属性路径 ${path} 在 ${props.slice(0, i).join('.')} 处中断`);
                return defaultValue;
            }
            current = current[props[i]];
        }
        
        return current !== undefined && current !== null ? current : defaultValue;
    }
    
    // 更新股票基本信息
    function updateStockInfo(latestData) {

        console.info('数据集📚:', latestData);

        // 获取最新价格和涨跌
        const currentPrice = parseFloat(latestData.close);
        const previousPrice = stockData.length > 1 ? parseFloat(stockData[stockData.length - 2].close) : currentPrice;
        const priceChange = currentPrice - previousPrice;
        const priceChangePercent = (priceChange / previousPrice) * 100;
        
        // 初始设置占位值，等待AI分析结果
        let stockName = "加载中...";
        let industry = "行业数据加载中...";
    
        // 设置页面中的股票代码和默认值
        $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
        $('#stock-info').text(`${industry} | 数据日期: ${new Date(latestData.date).toLocaleDateString()}`);
    
        
        // 更新股票标题
        // const stockName = latestData.stockName || "未知";
        // $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
        
        // 根据市场类型显示不同标签
        let marketBadge = '';
        if (marketType === 'A') {
            marketBadge = '<span class="badge bg-primary">A股</span>';
        } else if (marketType === 'HK') {
            marketBadge = '<span class="badge bg-success">港股</span>';
        } else if (marketType === 'US') {
            marketBadge = '<span class="badge bg-info">美股</span>';
        }
        $('#market-badge').html(marketBadge);
        
        // 更新股票信息
        // const industry = latestData.industry || "未知行业";
        const date = new Date(latestData.date).toLocaleDateString();
        // $('#stock-info').text(`${industry} | 数据日期: ${date}`);
        
        // 更新价格
        const priceChangeClass = priceChange >= 0 ? 'trend-up' : 'trend-down';
        const priceChangeIcon = priceChange >= 0 ? '<i class="fas fa-caret-up"></i>' : '<i class="fas fa-caret-down"></i>';
        const priceChangeDisplay = `${priceChangeIcon} ${Math.abs(priceChange).toFixed(2)} (${Math.abs(priceChangePercent).toFixed(2)}%)`;
        
        $('#price-container').html(`
            <div class="stock-price ${priceChangeClass}">${currentPrice.toFixed(2)}</div>
            <div class="price-change ${priceChangeClass}">${priceChangeDisplay}</div>
        `);
        
        // 更新开盘、最高、最低价格，并清除shimmer-skeleton样式
        $('#open-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.open ? parseFloat(latestData.open).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        $('#high-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.high ? parseFloat(latestData.high).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        $('#low-price')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(latestData.low ? parseFloat(latestData.low).toFixed(2) : '--')
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        // 更新成交量信息，并清除shimmer-skeleton样式
        const volume = latestData.volume;
        let volumeText = '--';
        if (volume && volume > 0) {
            if (volume >= 100000000) {
                volumeText = (volume / 100000000).toFixed(2) + '亿';
            } else if (volume >= 10000) {
                volumeText = (volume / 10000).toFixed(2) + '万';
            } else {
                volumeText = volume.toFixed(0);
            }
        }
        $('#volume-info')
            .removeClass('shimmer-skeleton')
            .removeAttr('style')
            .text(volumeText)
            .css({
                'color': 'var(--md-sys-color-on-primary-container)',
                'font-size': '16px',
                'font-weight': '500'
            });

        // 更新AI分析股票名称
        $('#ai-analysis-stock-name').text(stockName);
    }
    
    // 从后端API获取统一评分并渲染
    function fetchAndRenderScore() {
        console.log(`开始获取股票 ${stockCode} 的统一评分`);

        // 显示加载状态
        $('#score-container').html(`
            <div class="score-indicator">
                <div class="score-loading">
                    <div class="shimmer-skeleton" style="width: 160px; height: 160px; border-radius: 50%;"></div>
                    <div class="score-display">
                        <div class="shimmer-skeleton" style="width: 60px; height: 48px; margin: 0 auto 8px;"></div>
                        <div class="shimmer-skeleton" style="width: 80px; height: 16px; margin: 0 auto;"></div>
                    </div>
                </div>
            </div>
        `);

        $('#recommendation-container').html(`
            <div class="shimmer-skeleton" style="width: 120px; height: 32px; margin: 0 auto;"></div>
        `);

        // 调用后端统一评分API
        $.ajax({
            url: '/api/stock_score',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: marketType
            }),
            timeout: 15000, // 15秒超时
            success: function(response) {
                console.log('获取评分成功:', response);

                const score = response.score || 0;
                const scoreDetails = response.score_details || {};
                const recommendation = response.recommendation || '无建议';

                // 渲染评分
                const scoreClass = getScoreColorClass(score);
                const scoreText = getScoreDescription(score);

                $('#score-container').html(`
                    <div class="score-indicator">
                        <div id="score-chart"></div>
                        <div class="score-display">
                            <p class="score-value">${score}</p>
                            <p class="score-label">综合评分</p>
                        </div>
                    </div>
                `);

                // 渲染环形图表
                renderScoreChart(score);

                // 渲染建议
                $('#recommendation-container').html(`
                    <div class="badge badge-pill ${scoreClass} px-3 py-2">${scoreText}</div>
                `);

                console.log(`股票 ${stockCode} 评分渲染完成: ${score}`);

                // 渲染技术指标（保留原有逻辑，但从stockData获取）
                if (stockData && stockData.length > 0) {
                    renderTechnicalIndicators(stockData[stockData.length - 1]);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取评分失败:', error);

                let errorMsg = '获取评分失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }

                // 显示错误状态
                $('#score-container').html(`
                    <div class="score-indicator">
                        <div class="score-error">
                            <i class="material-icons" style="font-size: 48px; color: #f44336;">error</i>
                            <div class="score-display">
                                <p class="score-value">--</p>
                                <p class="score-label">评分获取失败</p>
                            </div>
                        </div>
                    </div>
                `);

                $('#recommendation-container').html(`
                    <div class="badge badge-pill badge-secondary px-3 py-2">评分获取失败</div>
                `);

                // 显示错误提示
                showError(errorMsg);

                // 渲染技术指标（保留原有逻辑，但从stockData获取）
                if (stockData && stockData.length > 0) {
                    renderTechnicalIndicators(stockData[stockData.length - 1]);
                }
            }
        });
    }

    // 渲染技术指标（从原函数中分离出来）
    function renderTechnicalIndicators(latestData) {
        const rsi = parseFloat(latestData.RSI || 50);
        const ma5 = parseFloat(latestData.MA5 || 0);
        const ma20 = parseFloat(latestData.MA20 || 0);
        const ma60 = parseFloat(latestData.MA60 || 0);
        const macd = parseFloat(latestData.MACD || 0);
        const signal = parseFloat(latestData.Signal || 0);
        const volRatio = parseFloat(latestData.Volume_Ratio || 1);

        // 渲染关键指标
        $('#rsi-container').html(`
            <div class="stat-value">${rsi.toFixed(1)}</div>
            <div class="stat-label">RSI</div>
        `);

        const maTrend = ma5 > ma20 ? 'trend-up' : 'trend-down';
        const maTrendIcon = ma5 > ma20 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#ma-trend-container').html(`
            <div class="stat-value ${maTrend}">${maTrendIcon}</div>
            <div class="stat-label">均线趋势</div>
        `);

        const macdTrend = macd > signal ? 'trend-up' : 'trend-down';
        const macdTrendIcon = macd > signal ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#macd-container').html(`
            <div class="stat-value ${macdTrend}">${macdTrendIcon}</div>
            <div class="stat-label">MACD</div>
        `);

        const volumeStatus = volRatio > 1 ? 'trend-up' : 'trend-down';
        const volumeIcon = volRatio > 1 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#volume-container').html(`
            <div class="stat-value ${volumeStatus}">${volRatio.toFixed(1)}</div>
            <div class="stat-label">量比</div>
        `);

        // 计算成交量趋势指标
        const currentVolume = parseFloat(latestData.volume || 0);
        const avgVolume = parseFloat(latestData.Volume_MA || currentVolume);
        const volumeTrendRatio = avgVolume > 0 ? (currentVolume / avgVolume) : 1;
        const volumeTrendStatus = volumeTrendRatio > 1 ? 'trend-up' : 'trend-down';
        const volumeTrendIcon = volumeTrendRatio > 1 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';

        $('#volume-trend-container').html(`
            <div class="stat-value ${volumeTrendStatus}">${volumeTrendIcon}</div>
            <div class="stat-label">成交量趋势</div>
        `);

        // 渲染雷达图
        renderRadarChart(rsi/10, (ma5 > ma20 ? 8 : 3), (macd > signal ? 8 : 3), (volRatio > 1 ? 8 : 3));
    }
    
    // 计算并渲染支撑压力位
    function calculateAndRenderSupportResistance(latestData) {
        const currentPrice = parseFloat(latestData.close);
        
        // 布林带支撑压力
        const upperBand = parseFloat(latestData.BB_upper || (currentPrice * 1.05));
        const lowerBand = parseFloat(latestData.BB_lower || (currentPrice * 0.95));
        
        // 移动平均线支撑压力
        const ma5 = parseFloat(latestData.MA5 || 0);
        const ma20 = parseFloat(latestData.MA20 || 0);
        const ma60 = parseFloat(latestData.MA60 || 0);
        
        // 计算支撑位
        const supports = [];
        const resistances = [];
        
        // 如果价格在均线上方，将均线作为支撑位
        if (currentPrice > ma5) supports.push({level: ma5, type: 'MA5', distance: ((ma5 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma5, type: 'MA5', distance: ((ma5 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        if (currentPrice > ma20) supports.push({level: ma20, type: 'MA20', distance: ((ma20 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma20, type: 'MA20', distance: ((ma20 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        if (currentPrice > ma60) supports.push({level: ma60, type: 'MA60', distance: ((ma60 - currentPrice) / currentPrice * 100).toFixed(2)});
        else resistances.push({level: ma60, type: 'MA60', distance: ((ma60 - currentPrice) / currentPrice * 100).toFixed(2)});
        
        // 布林带支撑压力
        supports.push({level: lowerBand, type: '布林下轨', distance: ((lowerBand - currentPrice) / currentPrice * 100).toFixed(2)});
        resistances.push({level: upperBand, type: '布林上轨', distance: ((upperBand - currentPrice) / currentPrice * 100).toFixed(2)});
        
        // 添加整数关口
        const integerSupport = Math.floor(currentPrice);
        const integerResistance = Math.ceil(currentPrice);
        
        if (integerSupport < currentPrice) {
            supports.push({level: integerSupport, type: '整数关口', distance: ((integerSupport - currentPrice) / currentPrice * 100).toFixed(2)});
        }
        
        if (integerResistance > currentPrice) {
            resistances.push({level: integerResistance, type: '整数关口', distance: ((integerResistance - currentPrice) / currentPrice * 100).toFixed(2)});
        }
        
        // 按距离排序
        supports.sort((a, b) => parseFloat(b.distance) - parseFloat(a.distance));
        resistances.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
        
        // 渲染表格
        let tableHtml = `
            <table class="table table-sm support-resistance-table">
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>价格</th>
                        <th>距离</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        // 添加压力位
        resistances.slice(0, 3).forEach(r => {
            tableHtml += `
                <tr>
                    <td><span class="badge bg-danger">压力</span> ${r.type}</td>
                    <td>${r.level.toFixed(2)}</td>
                    <td>+${Math.abs(r.distance)}%</td>
                </tr>
            `;
        });
        
        // 添加当前价格
        tableHtml += `
            <tr class="table-info">
                <td><span class="badge bg-primary">当前</span></td>
                <td>${currentPrice.toFixed(2)}</td>
                <td>-</td>
            </tr>
        `;
        
        // 添加支撑位
        supports.slice(0, 3).forEach(s => {
            tableHtml += `
                <tr>
                    <td><span class="badge bg-success">支撑</span> ${s.type}</td>
                    <td>${s.level.toFixed(2)}</td>
                    <td>${s.distance}%</td>
                </tr>
            `;
        });
        
        tableHtml += `
                </tbody>
            </table>
        `;
        
        $('#support-resistance-container').html(tableHtml);
    }
    
    // 渲染评分图表
    function renderScoreChart(score) {
        const options = {
            series: [score],
            chart: {
                height: 180,
                type: 'radialBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -135,
                    endAngle: 135,
                    hollow: {
                        margin: 0,
                        size: '70%',
                    },
                    dataLabels: {
                        show: false
                    },
                    track: {
                        background: '#f2f2f2',
                        strokeWidth: '97%',
                        margin: 5,
                        dropShadow: {
                            enabled: false
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: [getScoreGradientColor(score)],
                    inverseColors: true,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            stroke: {
                lineCap: 'round'
            },
            colors: [getScoreColor(score)]
        };

        const chart = new ApexCharts(document.querySelector("#score-chart"), options);
        chart.render();
    }
    
    // 渲染雷达图
    function renderRadarChart(rsiScore, trendScore, macdScore, volumeScore) {
        $('#radar-chart-container').html('<div id="radar-chart" style="height: 200px;"></div>');
        
        const options = {
            series: [{
                name: '评分',
                data: [rsiScore, trendScore, macdScore, volumeScore]
            }],
            chart: {
                height: 200,
                type: 'radar',
                toolbar: {
                    show: false
                },
                dropShadow: {
                    enabled: true,
                    blur: 1,
                    left: 1,
                    top: 1
                }
            },
            stroke: {
                width: 2
            },
            fill: {
                opacity: 0.2
            },
            markers: {
                size: 3
            },
            xaxis: {
                categories: ['RSI', '趋势', 'MACD', '成交量']
            },
            yaxis: {
                show: false,
                min: 0,
                max: 10
            },
            plotOptions: {
                radar: {
                    polygons: {
                        strokeColors: '#e9e9e9',
                        fill: {
                            colors: ['#f8f8f8', '#fff']
                        }
                    }
                }
            }
        };

        const chart = new ApexCharts(document.querySelector("#radar-chart"), options);
        chart.render();
    }
    
    // 渲染价格图表
    function renderPriceChart() {
        $('#price-chart').empty();
        
        try {
            // 准备数据
            const closePrices = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.close || 0)
            }));
            
            const ma5Data = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.MA5 || null)
            }));
            
            const ma20Data = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.MA20 || null)
            }));
            
            const ma60Data = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.MA60 || null)
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: '价格',
                        type: 'line',
                        data: closePrices
                    },
                    {
                        name: 'MA5',
                        type: 'line',
                        data: ma5Data
                    },
                    {
                        name: 'MA20',
                        type: 'line',
                        data: ma20Data
                    },
                    {
                        name: 'MA60',
                        type: 'line',
                        data: ma60Data
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    }
                },
                stroke: {
                    width: [3, 2, 2, 2],
                    curve: 'smooth',
                    dashArray: [0, 0, 0, 0]
                },
                colors: ['#4e73df', '#36b9cc', '#1cc88a', '#f6c23e'],
                fill: {
                    type: 'solid',
                    opacity: [1, 0.8, 0.8, 0.8],
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value.toFixed(2);
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value) {
                            return value ? value.toFixed(2) : '-';
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                },
                grid: {
                    borderColor: '#e0e0e0',
                    strokeDashArray: 5,
                    xaxis: {
                        lines: {
                            show: false
                        }
                    },
                    yaxis: {
                        lines: {
                            show: true
                        }
                    }
                }
            };

            const chart = new ApexCharts(document.querySelector("#price-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染价格图表时出错:", error);
            $('#price-chart').html('<div class="alert alert-danger">价格图表渲染失败</div>');
        }
    }
    
    // 渲染技术指标图表
    function renderIndicatorsChart() {
        $('#indicators-chart').empty();
        
        try {
            // 准备数据
            const macdData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.MACD || 0)
            }));
            
            const signalData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.Signal || 0)
            }));
            
            const histogramData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.MACD_hist || 0)
            }));
            
            const rsiData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.RSI || 0)
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: 'MACD',
                        type: 'line',
                        data: macdData
                    },
                    {
                        name: 'Signal',
                        type: 'line',
                        data: signalData
                    },
                    {
                        name: 'Histogram',
                        type: 'bar',
                        data: histogramData
                    },
                    {
                        name: 'RSI',
                        type: 'line',
                        data: rsiData
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true
                    }
                },
                stroke: {
                    width: [3, 3, 0, 3],
                    curve: 'smooth'
                },
                colors: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
                plotOptions: {
                    bar: {
                        columnWidth: '80%'
                    }
                },
                fill: {
                    opacity: [1, 1, 0.8, 1]
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: [
                    {
                        title: {
                            text: 'MACD'
                        },
                        labels: {
                            formatter: function (value) {
                                return value.toFixed(3);
                            }
                        }
                    },
                    {
                        show: false,
                        seriesName: 'Signal'
                    },
                    {
                        show: false,
                        seriesName: 'Histogram'
                    },
                    {
                        opposite: true,
                        title: {
                            text: 'RSI'
                        },
                        min: 0,
                        max: 100,
                        seriesName: 'RSI',
                        labels: {
                            formatter: function (value) {
                                return value.toFixed(1);
                            }
                        }
                    }
                ],
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value, { seriesIndex }) {
                            if (seriesIndex === 0 || seriesIndex === 1 || seriesIndex === 2) {
                                return value.toFixed(3);
                            }
                            return value.toFixed(1);
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                }
            };

            const chart = new ApexCharts(document.querySelector("#indicators-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染指标图表时出错:", error);
            $('#indicators-chart').html('<div class="alert alert-danger">指标图表渲染失败</div>');
        }
    }
    
    // 渲染成交量图表
    function renderVolumeChart() {
        $('#volume-chart').empty();
        
        try {
            // 准备数据
            const volumeData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.volume || 0)
            }));
            
            const volMaData = stockData.map(item => ({
                x: new Date(item.date),
                y: parseFloat(item.Volume_MA || 0)
            }));
            
            // 创建图表配置
            const options = {
                series: [
                    {
                        name: '成交量',
                        type: 'bar',
                        data: volumeData
                    },
                    {
                        name: '均量线',
                        type: 'line',
                        data: volMaData
                    }
                ],
                chart: {
                    height: 400,
                    type: 'line',
                    animations: {
                        enabled: false
                    },
                    toolbar: {
                        show: true
                    }
                },
                stroke: {
                    width: [0, 3],
                    curve: 'smooth'
                },
                colors: ['#4e73df', '#1cc88a'],
                fill: {
                    opacity: [0.8, 1]
                },
                plotOptions: {
                    bar: {
                        columnWidth: '80%'
                    }
                },
                markers: {
                    size: 0
                },
                xaxis: {
                    type: 'datetime',
                    labels: {
                        formatter: function (value) {
                            return new Date(value).toLocaleDateString();
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value;
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    intersect: false,
                    y: {
                        formatter: function (value, { seriesIndex }) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(2) + ' 百万';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(2) + ' 千';
                            }
                            return value;
                        }
                    },
                    x: {
                        format: 'yyyy-MM-dd'
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'center'
                }
            };

            const chart = new ApexCharts(document.querySelector("#volume-chart"), options);
            chart.render();
        } catch (error) {
            console.error("渲染成交量图表时出错:", error);
            $('#volume-chart').html('<div class="alert alert-danger">成交量图表渲染失败</div>');
        }
    }
    
    // 启动AI分析
    function startAIAnalysis() {
        resetAIAnalysisUI();
        
        // 启动处理时间计时器
        processingTime = 0;
        processingTimer = setInterval(function() {
            processingTime++;
            $('#ai-processing-time').text(processingTime);
            
            // 更新进度条 - 创建一个假的进度
            const progressPercent = Math.min(95, processingTime / 2);
            $('#ai-progress-bar').css('width', progressPercent + '%');
        }, 1000);
        
        // 启动AI分析任务
        $.ajax({
            url: '/api/start_stock_analysis',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: marketType
            }),
            success: function(response) {
                // 检查是否已有结果
                if (response.status === 'completed' && response.result) {
                    // 任务已完成，直接处理结果
                    handleAIAnalysisResult(response.result);
                    return;
                }
                
                // 开始轮询任务状态
                pollAIAnalysisStatus(response.task_id);
            },
            error: function(xhr, status, error) {
                handleAIAnalysisError(xhr, status, error);
            }
        });
    }
    
    // 轮询AI分析状态
    function pollAIAnalysisStatus(taskId) {
        // 保存当前任务ID，用于取消
        window.currentAnalysisTaskId = taskId;
        
        function checkStatus() {
            $.ajax({
                url: `/api/analysis_status/${taskId}`,
                type: 'GET',
                success: function(response) {
                    // 根据任务状态处理
                    if (response.status === 'completed') {
                        // 分析完成，停止轮询和计时器
                        clearInterval(window.analysisStatusInterval);
                        
                        // 处理结果
                        handleAIAnalysisResult(response.result);
                    } else if (response.status === 'failed') {
                        // 分析失败，停止轮询
                        clearInterval(window.analysisStatusInterval);
                        
                        handleAIAnalysisError(null, 'failed', response.error || '未知错误');
                    } else {
                        // 更新进度
                        const progress = response.progress || 0;
                        $('#ai-progress-bar').css('width', progress + '%');
                    }
                },
                error: function(xhr, status, error) {
                    console.log("轮询状态出错:", error);
                    // 错误时仍继续轮询
                }
            });
        }
        
        // 立即执行一次
        checkStatus();
        
        // 设置轮询间隔
        window.analysisStatusInterval = setInterval(checkStatus, 2000);
    }
    
    // 处理AI分析结果
    function handleAIAnalysisResult(result) {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        
        // 更新AI分析状态
        $('#ai-analysis-status').text('分析完成').removeClass('bg-info bg-danger').addClass('bg-success');
        
        // 渲染AI分析内容
        const aiAnalysis = result.ai_analysis || '无法获取AI分析结果';
        $('#ai-analysis').html(formatAIAnalysis(aiAnalysis));
        
        // 隐藏加载动画，显示内容
        $('#ai-analysis-loader').fadeOut(300, function() {
            $('#ai-analysis-content').css('opacity', '1');
        });

        // 从AI分析结果中更新股票信息
        if (result.basic_info) {
            const stockName = result.basic_info.stock_name || $('#stock-title').text().split(' ')[0];
            const industry = result.basic_info.industry || $('#stock-info').text().split(' | ')[0];
            
            $('#stock-title').html(`${stockName} <span class="text-muted">${stockCode}</span>`);
            $('#stock-info').text(`${industry} | 数据日期: ${$('#stock-info').text().split(' | ')[1]}`);
            $('#ai-analysis-stock-name').text(stockName);
        }
}
    
    // 处理AI分析错误
    function handleAIAnalysisError(xhr, status, error) {
        // 停止计时器
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        
        // 更新AI分析状态
        $('#ai-analysis-status').text('分析失败').removeClass('bg-info bg-success').addClass('bg-danger');
        
        // 构建错误消息
        let errorMsg = '分析过程中出错';
        if (status === 'timeout') {
            errorMsg = '请求超时，分析可能需要较长时间';
        } else if (xhr && xhr.status === 524 || xhr && xhr.status === 504) {
            errorMsg = '请求超时，服务器处理时间过长';
        } else if (xhr && xhr.responseJSON && xhr.responseJSON.error) {
            errorMsg += ': ' + xhr.responseJSON.error;
        } else if (error) {
            errorMsg += ': ' + error;
        }
        
        // 显示错误重试区域
        $('#error-retry').show();
        
        // 隐藏加载动画
        $('#ai-analysis-loader').hide();
    }
    
    // 取消AI分析
    function cancelAnalysis() {
        if (window.currentAnalysisTaskId) {
            $.ajax({
                url: `/api/cancel_analysis/${window.currentAnalysisTaskId}`,
                type: 'POST',
                success: function(response) {
                    // 停止计时器
                    if (processingTimer) {
                        clearInterval(processingTimer);
                        processingTimer = null;
                    }
                    
                    // 停止轮询
                    if (window.analysisStatusInterval) {
                        clearInterval(window.analysisStatusInterval);
                    }
                    
                    // 更新UI
                    $('#ai-analysis-status').text('已取消').removeClass('bg-info bg-success').addClass('bg-warning');
                    $('#ai-analysis-loader').hide();
                    $('#ai-analysis').html('<div class="alert alert-warning">AI分析已取消，您可以点击"重试分析"按钮重新开始分析。</div>');
                    $('#ai-analysis-content').css('opacity', '1');
                    $('#error-retry').show();
                },
                error: function(error) {
                    console.error('取消分析失败:', error);
                }
            });
        }
    }
    
    // 重置AI分析UI
    function resetAIAnalysisUI() {
        // 停止现有计时器和轮询
        if (processingTimer) {
            clearInterval(processingTimer);
            processingTimer = null;
        }
        
        if (window.analysisStatusInterval) {
            clearInterval(window.analysisStatusInterval);
        }
        
        // 重置处理时间
        processingTime = 0;
        $('#ai-processing-time').text('0');
        
        // 重置进度条
        $('#ai-progress-bar').css('width', '0%');
        
        // 更新状态显示
        $('#ai-analysis-status').text('正在分析...').removeClass('bg-success bg-danger bg-warning').addClass('bg-info');
        
        // 显示加载动画，隐藏内容
        $('#ai-analysis-loader').show();
        $('#ai-analysis-content').css('opacity', '0');
        
        // 隐藏错误重试区域
        $('#error-retry').hide();
    }
    
    // 格式化AI分析
    function formatAIAnalysis(text) {
    if (!text) return '';

    // 对文本进行HTML转义，确保安全
    const safeText = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

    // 替换 Markdown 元素
    let formatted = safeText
        // 标题 - 增加对####和#####的支持
        .replace(/^# (.*?)$/gm, '<h4 class="mt-3 mb-2">$1</h4>')
        .replace(/^## (.*?)$/gm, '<h5 class="mt-2 mb-2">$1</h5>')
        .replace(/^### (.*?)$/gm, '<h6 class="mt-2 mb-1">$1</h6>')
        .replace(/^#### (.*?)$/gm, '<h6 class="mt-2 mb-1">$1</h6>')
        .replace(/^##### (.*?)$/gm, '<div class="section-header bg-light p-2 rounded mb-2"><strong>$1</strong></div>')
        .replace(/^###### (.*?)$/gm, '<div class="subsection-header p-1 border-bottom mb-2"><strong>$1</strong></div>')
        
        // 加粗和斜体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/__(.*?)__/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/_(.*?)_/g, '<em>$1</em>')
        
        // 颜色标记
        .replace(/\[\[上涨\]\]/g, '<span class="trend-up"><i class="fas fa-arrow-up"></i> 上涨</span>')
        .replace(/\[\[下跌\]\]/g, '<span class="trend-down"><i class="fas fa-arrow-down"></i> 下跌</span>')
        
        // 应用特殊样式到金融术语
        .replace(/支撑位/g, '<span class="keyword">支撑位</span>')
        .replace(/压力位/g, '<span class="keyword">压力位</span>')
        .replace(/趋势/g, '<span class="keyword">趋势</span>')
        .replace(/均线/g, '<span class="keyword">均线</span>')
        .replace(/MACD/g, '<span class="term">MACD</span>')
        .replace(/RSI/g, '<span class="term">RSI</span>')
        .replace(/KDJ/g, '<span class="term">KDJ</span>')
        
        // 突出显示价格模式和变动
        .replace(/\b(上涨|升|涨)\b/g, '<span class="trend-up">$1</span>')
        .replace(/\b(下跌|降|跌)\b/g, '<span class="trend-down">$1</span>')
        .replace(/\b(买入|做多|多头|突破)\b/g, '<span class="trend-up">$1</span>')
        .replace(/\b(卖出|做空|空头|跌破)\b/g, '<span class="trend-down">$1</span>')
        
        // 突出显示价格数值 (匹配如 31.25, 120.50)
        .replace(/(\d+\.\d{2})/g, '<span class="price">$1</span>')
        
        // 将多个换行转换为段落
        .replace(/\n\n+/g, '</p><p class="analysis-para">')
        .replace(/\n/g, '<br>');

    // 包装在段落标签中以保持一致的样式
    return '<p class="analysis-para">' + formatted + '</p>';
}
    
    // 辅助函数：获取评分颜色类
    function getScoreColorClass(score) {
        if (score >= 80) return 'bg-success';
        if (score >= 60) return 'bg-primary';
        if (score >= 40) return 'bg-warning text-dark';
        return 'bg-danger';
    }
    
    // 辅助函数：获取评分颜色
    function getScoreColor(score) {
        if (score >= 80) return '#28a745';
        if (score >= 60) return '#4e73df';
        if (score >= 40) return '#f6c23e';
        return '#e74a3b';
    }
    
    // 辅助函数：获取评分梯度颜色
    function getScoreGradientColor(score) {
        if (score >= 80) return '#1cc88a';
        if (score >= 60) return '#36b9cc';
        if (score >= 40) return '#f6c23e';
        return '#e74a3b';
    }
    
    // 辅助函数：获取评分描述
    function getScoreDescription(score) {
        if (score >= 90) return '强烈推荐';
        if (score >= 80) return '推荐';
        if (score >= 70) return '持续关注';
        if (score >= 60) return '关注';
        if (score >= 50) return '观望';
        if (score >= 40) return '中性';
        if (score >= 30) return '减持';
        return '卖出';
    }
    
    // 显示错误提示
    function showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        $('#alerts-container').html(alertHtml);
    }
    
    // 显示信息提示
    function showInfo(message) {
        const alertHtml = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        $('#alerts-container').html(alertHtml);
    }
</script>
{% endblock %}