# Cloudflare Pages Headers Configuration
# https://developers.cloudflare.com/pages/platform/headers/

# Global security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=(), payment=(), usb=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

# HTML files - short cache for updates
/*.html
  Cache-Control: public, max-age=3600, must-revalidate
  Content-Type: text/html; charset=utf-8

# CSS files - long cache with immutable
/css/*
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: text/css; charset=utf-8

# JavaScript files - long cache with immutable
/js/*
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: application/javascript; charset=utf-8

# Image files - long cache
/images/*
  Cache-Control: public, max-age=31536000, immutable
  Vary: Accept

# SVG files
/*.svg
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: image/svg+xml; charset=utf-8

# Font files (if any)
/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff2

/*.woff
  Cache-Control: public, max-age=31536000, immutable
  Content-Type: font/woff

# Favicon
/favicon.ico
  Cache-Control: public, max-age=86400
  Content-Type: image/x-icon

# Manifest and service worker (if added later)
/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/json; charset=utf-8

/sw.js
  Cache-Control: public, max-age=0, must-revalidate
  Content-Type: application/javascript; charset=utf-8
