# 股票数据缓存系统使用指南

## 系统概述

股票数据缓存系统是一个高效的本地数据存储解决方案，旨在减少API调用次数，提高股票分析程序的运行速度。系统包含三个核心组件：

1. **StockDataCache** - 核心缓存管理类
2. **stock_data_precacher.py** - 独立的数据预缓存程序
3. **cache_manager.py** - 缓存管理工具

## 快速开始

### 1. 预缓存股票数据

首次使用前，建议先预缓存股票数据：

```bash
# 缓存涨停数据文件中的所有股票
python stock_data_precacher.py -i zhangting_20250601_20250630.csv

# 缓存所有A股数据（需要较长时间）
python stock_data_precacher.py --all

# 使用自定义缓存目录和并发数
python stock_data_precacher.py -i data.csv --cache-dir my_cache --workers 10
```

### 2. 运行股票评分程序

修改后的评分程序会自动使用缓存：

```bash
# 使用缓存运行（默认）
python standalone_stock_scorer.py

# 禁用缓存，直接使用API
python standalone_stock_scorer.py --no-cache

# 使用自定义缓存目录
python standalone_stock_scorer.py --cache-dir my_cache
```

### 3. 管理缓存

使用缓存管理工具：

```bash
# 查看缓存状态
python cache_manager.py --status

# 清理过期缓存
python cache_manager.py --clean

# 列出已缓存的股票
python cache_manager.py --list

# 更新特定股票的缓存
python cache_manager.py --update 000001 000002 600036
```

## 详细功能说明

### 数据预缓存程序 (stock_data_precacher.py)

**主要功能：**
- 批量下载股票历史数据
- 支持从CSV文件读取股票列表
- 支持下载所有A股数据
- 并发下载提高效率
- 智能跳过已缓存的有效数据

**命令行参数：**
```bash
python stock_data_precacher.py [选项]

选项:
  -i, --input FILE     输入CSV文件路径
  -a, --all           下载所有A股数据
  -c, --cache-dir DIR  缓存目录路径 (默认: stock_cache)
  -w, --workers N      并发线程数 (默认: 5)
  -d, --days N         下载天数 (默认: 365)
  -f, --force         强制刷新所有缓存
  -s, --status        显示缓存状态
  --clean             清理过期缓存
```

**使用示例：**
```bash
# 从CSV文件缓存数据
python stock_data_precacher.py -i zhangting_20250601_20250630.csv

# 下载最近180天的数据，使用8个并发线程
python stock_data_precacher.py -i data.csv -d 180 -w 8

# 强制刷新所有缓存
python stock_data_precacher.py -i data.csv --force
```

### 缓存管理工具 (cache_manager.py)

**主要功能：**
- 查看缓存状态和统计信息
- 清理过期缓存文件
- 列出已缓存的股票
- 更新特定股票的缓存

**命令行参数：**
```bash
python cache_manager.py [选项]

选项:
  -d, --cache-dir DIR  缓存目录路径
  -s, --status        显示缓存状态
  -c, --clean         清理过期缓存
  -l, --list          列出已缓存的股票
  --limit N           列表显示限制 (默认: 20)
  -u, --update CODES  更新指定股票的缓存
  -f, --force         强制更新缓存
```

### 修改后的评分程序

**新增功能：**
- 自动检测并使用缓存系统
- 优先从本地缓存读取数据
- 缓存不存在或过期时自动调用API
- 新获取的数据自动保存到缓存

**新增命令行参数：**
```bash
python standalone_stock_scorer.py [选项]

新增选项:
  --no-cache          禁用缓存系统
  --cache-dir DIR     缓存目录路径
  --cache-status      显示缓存状态信息
```

## 缓存系统配置

### 默认配置

```python
config = {
    "data_expire_days": 7,    # 数据过期天数
    "retry_count": 3,         # 重试次数
    "retry_delay": 2,         # 重试延迟(秒)
    "request_delay": 0.1,     # 请求间隔(秒)
}
```

### 文件结构

```
stock_cache/                 # 缓存根目录
├── data/                   # 数据文件目录
│   ├── 000/               # 按股票代码前缀分组
│   │   ├── 000001.parquet
│   │   └── 000002.parquet
│   ├── 600/
│   │   └── 600036.parquet
│   └── ...
└── metadata/              # 元数据目录
    └── cache_index.json   # 缓存索引文件
```

## 性能优化建议

### 1. 预缓存策略
- 首次使用前预缓存所有需要的股票数据
- 定期更新缓存（建议每周一次）
- 使用合适的并发线程数（建议5-10个）

### 2. 缓存维护
- 定期清理过期缓存释放磁盘空间
- 监控缓存大小，避免占用过多磁盘空间
- 备份重要的缓存数据

### 3. 网络优化
- 在网络条件好的时候进行批量缓存
- 设置合适的请求间隔避免被限流
- 使用重试机制处理网络异常

## 故障排除

### 常见问题

**1. 缓存系统不可用**
```
⚠️ 缓存系统不可用，将直接使用API
```
- 检查是否正确安装了依赖包
- 确认 `stock_data_cache.py` 文件存在

**2. 缓存目录权限问题**
- 确保程序有读写缓存目录的权限
- 检查磁盘空间是否充足

**3. 数据不一致**
- 使用 `--force` 参数强制刷新缓存
- 清理过期缓存后重新下载

**4. API调用失败**
- 检查网络连接
- 确认AKShare库版本兼容性
- 适当增加重试次数和延迟

### 日志文件

系统会生成以下日志文件：
- `stock_precacher.log` - 预缓存程序日志
- `stock_scorer.log` - 评分程序日志

## 最佳实践

1. **定期维护**：每周运行一次缓存清理和更新
2. **监控空间**：定期检查缓存目录大小
3. **备份重要数据**：对关键的缓存数据进行备份
4. **合理配置**：根据网络条件调整并发数和重试参数
5. **版本控制**：重要的股票列表文件纳入版本控制

## 技术细节

- **存储格式**：使用Parquet格式，比CSV更高效
- **索引管理**：JSON格式的缓存索引，快速检索
- **并发安全**：使用线程锁保证并发安全
- **错误处理**：完善的重试和跳过机制
- **内存优化**：流式处理，避免内存溢出
