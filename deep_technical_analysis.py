#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度技术指标分析 - 验证数据源一致性和计算参数
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from standalone_stock_scorer import StockScorer, RealDataService

def verify_data_source_consistency():
    """验证数据源一致性"""
    print("=" * 80)
    print("002295 精艺股份数据源一致性验证")
    print("=" * 80)
    
    # 创建数据服务
    data_service = RealDataService()
    stock_code = "002295.XSHE"
    
    try:
        # 获取历史数据
        df = data_service.get_stock_price_history(stock_code)
        
        if df is None:
            print("❌ 无法获取历史数据")
            return None
        
        print(f"📊 数据基本信息:")
        print(f"  数据点数量: {len(df)}")
        print(f"  数据时间范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"  数据列: {list(df.columns)}")
        
        # 显示最近几天的价格数据
        print(f"\n📈 最近5个交易日价格数据:")
        recent_data = df.tail(5)[['date', 'open', 'high', 'low', 'close', 'volume']]
        for _, row in recent_data.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: 开{row['open']:.2f} 高{row['high']:.2f} 低{row['low']:.2f} 收{row['close']:.2f} 量{row['volume']:,.0f}")
        
        # 重点分析最新交易日数据
        latest = df.iloc[-1]
        print(f"\n🎯 最新交易日详细数据 ({latest['date'].strftime('%Y-%m-%d')}):")
        print(f"  开盘价: {latest['open']:.2f}")
        print(f"  最高价: {latest['high']:.2f}")
        print(f"  最低价: {latest['low']:.2f}")
        print(f"  收盘价: {latest['close']:.2f}")
        print(f"  成交量: {latest['volume']:,.0f}")
        
        return df
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def verify_technical_parameters():
    """验证技术指标计算参数"""
    print(f"\n" + "=" * 80)
    print("技术指标计算参数验证")
    print("=" * 80)
    
    # 从独立程序中获取参数设置
    scorer = StockScorer()
    
    print(f"📋 独立程序技术指标参数:")
    print(f"  RSI周期: 14天 (标准设置)")
    print(f"  MACD参数: 快线12, 慢线26, 信号线9 (标准设置)")
    print(f"  布林带参数: 周期20, 标准差倍数2 (标准设置)")
    print(f"  ATR周期: 14天 (标准设置)")
    print(f"  ROC周期: 10天")
    
    print(f"\n🔍 需要验证的关键问题:")
    print(f"  1. 网页版是否使用相同的参数设置？")
    print(f"  2. RSI计算是否使用Wilder平滑方法？")
    print(f"  3. MACD的EMA计算是否标准？")
    print(f"  4. 数据预处理是否一致？")

def calculate_detailed_indicators(df):
    """详细计算技术指标并显示中间过程"""
    print(f"\n" + "=" * 80)
    print("详细技术指标计算过程")
    print("=" * 80)
    
    scorer = StockScorer()
    
    # 计算技术指标
    df_with_indicators = scorer.calculate_technical_indicators(df)
    
    # 获取最近几天的指标数据
    recent_indicators = df_with_indicators.tail(5)
    
    print(f"\n📊 最近5天技术指标数据:")
    for i, (_, row) in enumerate(recent_indicators.iterrows()):
        print(f"\n  {row['date'].strftime('%Y-%m-%d')} (第{len(df_with_indicators)-4+i}天):")
        print(f"    收盘价: {row['close']:.2f}")
        print(f"    RSI: {row['RSI']:.2f}")
        print(f"    MACD: {row['MACD']:.4f}")
        print(f"    Signal: {row['Signal']:.4f}")
        print(f"    MACD_hist: {row['MACD_hist']:.4f}")
        print(f"    BB_upper: {row['BB_upper']:.2f}")
        print(f"    BB_middle: {row['BB_middle']:.2f}")
        print(f"    BB_lower: {row['BB_lower']:.2f}")
        bb_position = (row['close'] - row['BB_lower']) / (row['BB_upper'] - row['BB_lower'])
        print(f"    BB位置: {bb_position:.3f}")
    
    return df_with_indicators

def analyze_rsi_calculation(df):
    """深入分析RSI计算过程"""
    print(f"\n" + "=" * 80)
    print("RSI计算过程深度分析")
    print("=" * 80)
    
    # 手动计算RSI验证
    close_prices = df['close'].values
    
    # 计算价格变化
    delta = np.diff(close_prices)
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    
    # 使用Wilder平滑方法计算平均收益和损失
    period = 14
    avg_gain = np.zeros(len(gain))
    avg_loss = np.zeros(len(loss))
    
    # 初始平均值
    avg_gain[period-1] = np.mean(gain[:period])
    avg_loss[period-1] = np.mean(loss[:period])
    
    # Wilder平滑
    for i in range(period, len(gain)):
        avg_gain[i] = (avg_gain[i-1] * (period-1) + gain[i]) / period
        avg_loss[i] = (avg_loss[i-1] * (period-1) + loss[i]) / period
    
    # 计算RSI
    rs = np.divide(avg_gain, avg_loss, out=np.zeros_like(avg_gain), where=avg_loss!=0)
    rsi_manual = 100 - (100 / (1 + rs))
    
    # 对比最后几个RSI值
    print(f"📊 手动计算RSI vs 程序计算RSI (最近5天):")
    df_with_indicators = calculate_detailed_indicators(df)
    recent_data = df_with_indicators.tail(5)
    
    for i, (_, row) in enumerate(recent_data.iterrows()):
        manual_rsi = rsi_manual[len(df)-5+i] if len(df)-5+i < len(rsi_manual) else np.nan
        program_rsi = row['RSI']
        diff = abs(manual_rsi - program_rsi) if not np.isnan(manual_rsi) else 0
        print(f"  {row['date'].strftime('%Y-%m-%d')}: 手动{manual_rsi:.2f} vs 程序{program_rsi:.2f} (差异:{diff:.4f})")
    
    # 分析最新RSI值应该落在哪个评分区间
    latest_rsi = recent_data.iloc[-1]['RSI']
    print(f"\n🎯 最新RSI分析:")
    print(f"  RSI值: {latest_rsi:.2f}")
    
    if 40 <= latest_rsi <= 60:
        rsi_score = 7
        rsi_zone = "中性区域 (40-60)"
    elif 30 <= latest_rsi < 40 or 60 < latest_rsi <= 70:
        rsi_score = 10
        rsi_zone = "阈值区域 (30-40或60-70)"
    elif latest_rsi < 30:
        rsi_score = 8
        rsi_zone = "超卖区域 (<30)"
    elif latest_rsi > 70:
        rsi_score = 2
        rsi_zone = "超买区域 (>70)"
    
    print(f"  评分区间: {rsi_zone}")
    print(f"  应得分数: {rsi_score}分")
    
    return latest_rsi, rsi_score

def analyze_macd_calculation(df):
    """深入分析MACD计算过程"""
    print(f"\n" + "=" * 80)
    print("MACD计算过程深度分析")
    print("=" * 80)
    
    # 手动计算MACD验证
    close_prices = df['close']
    
    # 计算EMA
    ema12 = close_prices.ewm(span=12).mean()
    ema26 = close_prices.ewm(span=26).mean()
    
    # 计算MACD线
    macd_line = ema12 - ema26
    
    # 计算信号线
    signal_line = macd_line.ewm(span=9).mean()
    
    # 计算MACD柱状图
    macd_hist = macd_line - signal_line
    
    # 对比最后几个MACD值
    print(f"📊 手动计算MACD vs 程序计算MACD (最近5天):")
    df_with_indicators = calculate_detailed_indicators(df)
    recent_data = df_with_indicators.tail(5)
    
    for i, (_, row) in enumerate(recent_data.iterrows()):
        idx = len(df) - 5 + i
        manual_macd = macd_line.iloc[idx]
        manual_signal = signal_line.iloc[idx]
        manual_hist = macd_hist.iloc[idx]
        
        program_macd = row['MACD']
        program_signal = row['Signal']
        program_hist = row['MACD_hist']
        
        print(f"  {row['date'].strftime('%Y-%m-%d')}:")
        print(f"    MACD: 手动{manual_macd:.4f} vs 程序{program_macd:.4f} (差异:{abs(manual_macd-program_macd):.6f})")
        print(f"    Signal: 手动{manual_signal:.4f} vs 程序{program_signal:.4f} (差异:{abs(manual_signal-program_signal):.6f})")
        print(f"    Hist: 手动{manual_hist:.4f} vs 程序{program_hist:.4f} (差异:{abs(manual_hist-program_hist):.6f})")
    
    # 分析最新MACD状态
    latest = recent_data.iloc[-1]
    prev = recent_data.iloc[-2]
    
    print(f"\n🎯 最新MACD状态分析:")
    print(f"  MACD: {latest['MACD']:.4f}")
    print(f"  Signal: {latest['Signal']:.4f}")
    print(f"  MACD_hist: {latest['MACD_hist']:.4f}")
    print(f"  前日MACD_hist: {prev['MACD_hist']:.4f}")
    
    # 判断MACD状态和评分
    if latest['MACD'] > latest['Signal'] and latest['MACD_hist'] > 0:
        macd_score = 10
        macd_status = "金叉且柱状图为正"
    elif latest['MACD'] > latest['Signal']:
        macd_score = 8
        macd_status = "金叉"
    elif latest['MACD'] < latest['Signal'] and latest['MACD_hist'] < 0:
        macd_score = 0
        macd_status = "死叉且柱状图为负"
    elif latest['MACD_hist'] > prev['MACD_hist']:
        macd_score = 5
        macd_status = "柱状图增长"
    else:
        macd_score = 0
        macd_status = "其他情况"
    
    print(f"  MACD状态: {macd_status}")
    print(f"  应得分数: {macd_score}分")
    
    return macd_score

def main():
    """主函数"""
    print("开始深度技术指标分析...")
    
    # 1. 验证数据源一致性
    df = verify_data_source_consistency()
    if df is None:
        return
    
    # 2. 验证技术指标参数
    verify_technical_parameters()
    
    # 3. 详细计算技术指标
    df_with_indicators = calculate_detailed_indicators(df)
    
    # 4. 深入分析RSI计算
    latest_rsi, rsi_score = analyze_rsi_calculation(df)
    
    # 5. 深入分析MACD计算
    macd_score = analyze_macd_calculation(df)
    
    # 6. 总结分析结果
    print(f"\n" + "=" * 80)
    print("深度分析总结")
    print("=" * 80)
    
    print(f"\n🎯 关键发现:")
    print(f"  RSI评分: {rsi_score}分 (RSI={latest_rsi:.2f})")
    print(f"  MACD评分: {macd_score}分")
    print(f"  预期技术指标总分: {rsi_score + macd_score + 1}分 (包含布林带1分)")
    
    print(f"\n🔍 与实际结果对比:")
    print(f"  独立程序技术指标评分: 21分")
    print(f"  网页版技术指标评分: 13分")
    print(f"  计算预期评分: {rsi_score + macd_score + 1}分")
    
    if rsi_score + macd_score + 1 == 21:
        print(f"  ✅ 独立程序计算正确")
    else:
        print(f"  ❌ 存在计算差异，需要进一步调查")

if __name__ == "__main__":
    main()
