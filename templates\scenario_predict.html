{% extends "layout.html" %}

{% block title %}情景预测 - {{ stock_code }} - 智能分析系统{% endblock %}

{% block content %}
<div class="page-transition">
    <!-- Enhanced Material Design 3 分析表单 -->
    <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
        <div class="md3-card-header">
            <h2 class="md3-card-title">
                <i class="material-icons">lightbulb</i> 多情景预测
            </h2>
            <p class="md3-card-subtitle">基于AI模型的股价多情景预测分析</p>
        </div>
        <div class="md3-card-body">
            <form id="scenario-form" style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 20px; align-items: end;">
                <div class="md3-text-field md3-text-field-outlined">
                    <input type="text" class="md3-text-field-input" id="stock-code" placeholder=" " required>
                    <label class="md3-text-field-label">股票代码</label>
                    <div class="md3-text-field-supporting-text">例如：600519、0700.HK、AAPL</div>
                </div>

                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="market-type">
                        <option value="A" selected>A股</option>
                        <option value="HK">港股</option>
                        <option value="US">美股</option>
                    </select>
                    <label class="md3-text-field-label">市场类型</label>
                </div>

                <div class="md3-text-field md3-text-field-outlined">
                    <select class="md3-text-field-input" id="days">
                        <option value="30">30天</option>
                        <option value="60" selected>60天</option>
                        <option value="90">90天</option>
                        <option value="180">180天</option>
                    </select>
                    <label class="md3-text-field-label">预测周期</label>
                </div>

                <button type="submit" class="md3-button md3-button-filled md3-button-large">
                    <i class="material-icons">auto_graph</i> 开始预测
                </button>
            </form>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Loading Panel -->
    <div id="loading-panel" style="display: none;">
        <div class="md3-card md3-card-elevated md3-animate-fade-in">
            <div class="md3-card-body" style="text-align: center; padding: 64px 32px;">
                <div class="md3-progress-indicator" style="margin-bottom: 24px;"></div>
                <p style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-large-font); font-size: var(--md-sys-typescale-body-large-size); margin: 0 0 16px 0;">正在生成预测结果...</p>
                <p style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); margin: 0; display: flex; align-items: center; justify-content: center; gap: 8px;">
                    <i class="material-icons" style="font-size: 18px;">info</i>
                    AI分析需要一些时间，请耐心等待
                </p>
            </div>
        </div>
    </div>

    <!-- Enhanced Material Design 3 Scenario Result -->
    <div id="scenario-result" style="display: none;">
        <!-- 价格预测图表卡片 -->
        <div class="md3-card md3-card-elevated md3-animate-fade-in" style="margin-bottom: 32px;">
            <div class="md3-card-header">
                <h3 class="md3-card-title">
                    <i class="material-icons">show_chart</i> 价格预测图
                </h3>
                <p class="md3-card-subtitle">多情景股价走势预测</p>
            </div>
            <div class="md3-card-body" style="padding: 0;">
                <div id="price-prediction-chart" style="height: 450px;"></div>
            </div>
        </div>

        <!-- 情景预测卡片组 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 32px; margin-bottom: 32px;">
            <!-- 乐观情景卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-left">
                <div class="md3-card-header" style="background-color: var(--md-sys-color-success-container); color: var(--md-sys-color-on-success-container); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0;">
                    <h3 class="md3-card-title" style="color: var(--md-sys-color-on-success-container);">
                        <i class="material-icons">trending_up</i> 乐观情景
                    </h3>
                </div>
                <div class="md3-card-body">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 24px;">
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">目标价</span>
                            <div id="optimistic-price" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-success); margin-top: 8px;">--</div>
                        </div>
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">预期涨幅</span>
                            <div id="optimistic-change" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-success); margin-top: 8px;">--</div>
                        </div>
                    </div>
                    <p id="optimistic-analysis" style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5; margin: 0;"></p>
                </div>
            </div>

            <!-- 中性情景卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-fade-in" style="animation-delay: 0.1s;">
                <div class="md3-card-header" style="background-color: var(--md-sys-color-primary-container); color: var(--md-sys-color-on-primary-container); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0;">
                    <h3 class="md3-card-title" style="color: var(--md-sys-color-on-primary-container);">
                        <i class="material-icons">timeline</i> 中性情景
                    </h3>
                </div>
                <div class="md3-card-body">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 24px;">
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">目标价</span>
                            <div id="neutral-price" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-primary); margin-top: 8px;">--</div>
                        </div>
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">预期涨幅</span>
                            <div id="neutral-change" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-primary); margin-top: 8px;">--</div>
                        </div>
                    </div>
                    <p id="neutral-analysis" style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5; margin: 0;"></p>
                </div>
            </div>

            <!-- 悲观情景卡片 -->
            <div class="md3-card md3-card-elevated md3-animate-slide-in-right" style="animation-delay: 0.2s;">
                <div class="md3-card-header" style="background-color: var(--md-sys-color-error-container); color: var(--md-sys-color-on-error-container); border-radius: var(--md-sys-shape-corner-large) var(--md-sys-shape-corner-large) 0 0;">
                    <h3 class="md3-card-title" style="color: var(--md-sys-color-on-error-container);">
                        <i class="material-icons">trending_down</i> 悲观情景
                    </h3>
                </div>
                <div class="md3-card-body">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 24px;">
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">目标价</span>
                            <div id="pessimistic-price" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-error); margin-top: 8px;">--</div>
                        </div>
                        <div>
                            <span style="color: var(--md-sys-color-on-surface-variant); font-family: var(--md-sys-typescale-label-large-font); font-size: var(--md-sys-typescale-label-large-size); font-weight: 500;">预期涨幅</span>
                            <div id="pessimistic-change" style="font-family: var(--md-sys-typescale-headline-large-font); font-size: 28px; font-weight: 500; color: var(--md-sys-color-error); margin-top: 8px;">--</div>
                        </div>
                    </div>
                    <p id="pessimistic-analysis" style="color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-medium-font); font-size: var(--md-sys-typescale-body-medium-size); line-height: 1.5; margin: 0;"></p>
                </div>
            </div>
        </div>

        <!-- 风险与机会分析卡片 -->
        <div class="md3-card md3-card-elevated md3-animate-slide-in-up">
            <div class="md3-card-header">
                <h3 class="md3-card-title">
                    <i class="material-icons">balance</i> 风险与机会
                </h3>
                <p class="md3-card-subtitle">投资决策关键因素分析</p>
            </div>
            <div class="md3-card-body">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 32px;">
                    <div>
                        <h4 style="color: var(--md-sys-color-error); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                            <i class="material-icons">warning</i> 风险因素
                        </h4>
                        <ul id="risk-factors" style="list-style: none; padding: 0; margin: 0;">
                            <!-- 风险因素将在JS中填充 -->
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: var(--md-sys-color-success); font-family: var(--md-sys-typescale-title-medium-font); font-size: var(--md-sys-typescale-title-medium-size); font-weight: 500; margin-bottom: 20px; display: flex; align-items: center; gap: 8px;">
                            <i class="material-icons">lightbulb</i> 有利因素
                        </h4>
                        <ul id="opportunity-factors" style="list-style: none; padding: 0; margin: 0;">
                            <!-- 有利因素将在JS中填充 -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        $('#scenario-form').submit(function(e) {
            e.preventDefault();
            const stockCode = $('#stock-code').val().trim();
            const marketType = $('#market-type').val();
            const days = $('#days').val();

            if (!stockCode) {
                showError('请输入股票代码！');
                return;
            }

            fetchScenarioPrediction(stockCode, marketType, days);
        });
    });

    function fetchScenarioPrediction(stockCode, marketType, days) {
        $('#loading-panel').show();
        $('#scenario-result').hide();

        $.ajax({
            url: '/api/scenario_predict',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                stock_code: stockCode,
                market_type: marketType,
                days: parseInt(days)
            }),
            success: function(response) {
                $('#loading-panel').hide();

                // 检查响应是否包含错误
                if (response.error) {
                    showError('情景预测失败: ' + response.error);
                    return;
                }

                // 检查响应数据完整性
                if (!response.optimistic || !response.neutral || !response.pessimistic) {
                    showError('情景预测数据不完整，请稍后重试');
                    return;
                }

                renderScenarioPrediction(response, stockCode);
                $('#scenario-result').show();
            },
            error: function(xhr, status, error) {
                $('#loading-panel').hide();
                let errorMsg = '获取情景预测失败';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ': ' + xhr.responseJSON.error;
                } else if (error) {
                    errorMsg += ': ' + error;
                }
                showError(errorMsg);
            }
        });
    }

    function renderScenarioPrediction(data, stockCode) {
    try {
        // 安全地设置乐观情景数据
        $('#optimistic-price').text('¥' + formatNumber(data.optimistic?.target_price || 0, 2));
        $('#optimistic-change').text(formatPercent(data.optimistic?.change_percent || 0, 2));
        $('#optimistic-analysis').text(data.optimistic_analysis || '暂无分析');

        // 安全地设置中性情景数据
        $('#neutral-price').text('¥' + formatNumber(data.neutral?.target_price || 0, 2));
        $('#neutral-change').text(formatPercent(data.neutral?.change_percent || 0, 2));
        $('#neutral-analysis').text(data.neutral_analysis || '暂无分析');

        // 安全地设置悲观情景数据
        $('#pessimistic-price').text('¥' + formatNumber(data.pessimistic?.target_price || 0, 2));
        $('#pessimistic-change').text(formatPercent(data.pessimistic?.change_percent || 0, 2));
        $('#pessimistic-analysis').text(data.pessimistic_analysis || '暂无分析');

    // 设置风险与机会因素 - 使用API返回的数据
    if (data.risk_factors && Array.isArray(data.risk_factors)) {
        // 使用API返回的风险因素
        $('#risk-factors').html('');
        data.risk_factors.forEach(factor => {
            $('#risk-factors').append(`<li>${factor}</li>`);
        });
    } else {
        // 如果API没有返回风险因素，使用默认值
        setDefaultRiskFactors();
    }

    if (data.opportunity_factors && Array.isArray(data.opportunity_factors)) {
        // 使用API返回的机会因素
        $('#opportunity-factors').html('');
        data.opportunity_factors.forEach(factor => {
            $('#opportunity-factors').append(`<li>${factor}</li>`);
        });
    } else {
        // 如果API没有返回机会因素，使用默认值
        setDefaultOpportunityFactors();
    }

        // 渲染价格预测图表
        renderPricePredictionChart(data);
    } catch (error) {
        console.error('渲染情景预测数据时出错:', error);
        showError('渲染预测结果时出错，请稍后重试');
    }
}

    // 将原来的 setDefaultRiskOpportunityFactors 函数拆分为两个函数
    function setDefaultRiskFactors() {
        // 示例风险因素
        const riskFactors = [
            '宏观经济下行压力增大',
            '行业政策收紧可能性',
            '原材料价格上涨',
            '市场竞争加剧',
            '技术迭代风险'
        ];

        // 填充HTML
        $('#risk-factors').html('');
        riskFactors.forEach(factor => {
            $('#risk-factors').append(`<li style="padding: 8px 0; border-bottom: 1px solid var(--md-sys-color-outline-variant); color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-medium-font); display: flex; align-items: center; gap: 8px;"><i class="material-icons" style="color: var(--md-sys-color-error); font-size: 18px;">error_outline</i>${factor}</li>`);
        });
    }

    function setDefaultOpportunityFactors() {
        // 示例有利因素
        const opportunityFactors = [
            '行业景气度持续向好',
            '公司新产品上市',
            '成本控制措施见效',
            '产能扩张计划',
            '国际市场开拓机会'
        ];

        // 填充HTML
        $('#opportunity-factors').html('');
        opportunityFactors.forEach(factor => {
            $('#opportunity-factors').append(`<li style="padding: 8px 0; border-bottom: 1px solid var(--md-sys-color-outline-variant); color: var(--md-sys-color-on-surface); font-family: var(--md-sys-typescale-body-medium-font); display: flex; align-items: center; gap: 8px;"><i class="material-icons" style="color: var(--md-sys-color-success); font-size: 18px;">check_circle_outline</i>${factor}</li>`);
        });
    }

    // 保留原函数，但内部调用新的拆分函数
    function setDefaultRiskOpportunityFactors() {
        setDefaultRiskFactors();
        setDefaultOpportunityFactors();
    }
    
    function setDefaultRiskOpportunityFactors() {
        // 示例风险因素
        const riskFactors = [
            '宏观经济下行压力增大',
            '行业政策收紧可能性',
            '原材料价格上涨',
            '市场竞争加剧',
            '技术迭代风险'
        ];

        // 示例有利因素
        const opportunityFactors = [
            '行业景气度持续向好',
            '公司新产品上市',
            '成本控制措施见效',
            '产能扩张计划',
            '国际市场开拓机会'
        ];

        // 填充HTML
        $('#risk-factors').html('');
        riskFactors.forEach(factor => {
            $('#risk-factors').append(`<li>${factor}</li>`);
        });

        $('#opportunity-factors').html('');
        opportunityFactors.forEach(factor => {
            $('#opportunity-factors').append(`<li>${factor}</li>`);
        });
    }

    function renderPricePredictionChart(data) {
        try {
            // 准备数据
            const currentPrice = data.current_price || 0;

            // 安全地提取日期和价格路径
            const optimisticPath = data.optimistic?.path || {};
            const neutralPath = data.neutral?.path || {};
            const pessimisticPath = data.pessimistic?.path || {};

            const dates = Object.keys(optimisticPath);
            const optimisticPrices = Object.values(optimisticPath);
            const neutralPrices = Object.values(neutralPath);
            const pessimisticPrices = Object.values(pessimisticPath);

            // 检查数据是否有效
            if (dates.length === 0) {
                console.warn('价格预测路径数据为空，跳过图表渲染');
                return;
            }

        const options = {
            series: [
                {
                    name: '乐观情景',
                    data: optimisticPrices.map((price, i) => ({
                        x: new Date(dates[i]),
                        y: price
                    }))
                },
                {
                    name: '中性情景',
                    data: neutralPrices.map((price, i) => ({
                        x: new Date(dates[i]),
                        y: price
                    }))
                },
                {
                    name: '悲观情景',
                    data: pessimisticPrices.map((price, i) => ({
                        x: new Date(dates[i]),
                        y: price
                    }))
                }
            ],
            chart: {
                height: 400,
                type: 'line',
                zoom: {
                    enabled: true
                },
                toolbar: {
                    show: true
                }
            },
            colors: ['#20E647', '#2E93fA', '#FF4560'],
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: [3, 3, 3]
            },
            title: {
                text: '多情景预测',
                align: 'left'
            },
            grid: {
                borderColor: '#e7e7e7',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                },
            },
            markers: {
                size: 1
            },
            xaxis: {
                type: 'datetime',
                title: {
                    text: '日期'
                }
            },
            yaxis: {
                title: {
                    text: '价格 (¥)'
                },
                labels: {
                    formatter: function(val) {
                        return formatNumber(val, 2);
                    }
                }
            },
            legend: {
                position: 'top',
                horizontalAlign: 'right'
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(value) {
                        return '¥' + formatNumber(value, 2);
                    }
                }
            },
            annotations: {
                yaxis: [
                    {
                        y: currentPrice,
                        borderColor: '#000',
                        label: {
                            borderColor: '#000',
                            style: {
                                color: '#fff',
                                background: '#000'
                            },
                            text: '当前价格: ¥' + formatNumber(currentPrice, 2)
                        }
                    }
                ]
            }
        };

            const chart = new ApexCharts(document.querySelector("#price-prediction-chart"), options);
            chart.render();
        } catch (error) {
            console.error('渲染价格预测图表时出错:', error);
            // 在图表容器中显示错误信息
            $('#price-prediction-chart').html('<div class="text-center text-muted p-4">图表渲染失败，请稍后重试</div>');
        }
    }
</script>
{% endblock %}