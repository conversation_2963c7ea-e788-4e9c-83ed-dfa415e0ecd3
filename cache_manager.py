#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据缓存管理工具
提供缓存状态查看、清理、更新等管理功能
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from stock_data_cache import StockDataCache

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def show_cache_status(cache_dir: str):
    """显示缓存状态"""
    try:
        cache = StockDataCache(cache_dir=cache_dir)
        status = cache.get_cache_status()
        
        print("=" * 60)
        print("股票数据缓存状态")
        print("=" * 60)
        print(f"缓存目录: {status['cache_dir']}")
        print(f"总股票数: {status['total_stocks']}")
        print(f"有效缓存: {status['valid_stocks']}")
        print(f"过期缓存: {status['expired_stocks']}")
        print(f"缓存大小: {status['cache_size_mb']} MB")
        print(f"最后更新: {status['last_update']}")
        
        if status['expired_stocks'] > 0:
            print(f"\n⚠️ 发现 {status['expired_stocks']} 个过期缓存")
            print("建议运行: python cache_manager.py --clean 清理过期缓存")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 获取缓存状态失败: {e}")

def clean_expired_cache(cache_dir: str):
    """清理过期缓存"""
    try:
        cache = StockDataCache(cache_dir=cache_dir)
        
        print("正在清理过期缓存...")
        cleaned_count = cache.clean_expired_cache()
        
        if cleaned_count > 0:
            print(f"✅ 已清理 {cleaned_count} 个过期缓存文件")
        else:
            print("✅ 没有发现过期缓存文件")
            
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

def update_cache(cache_dir: str, stock_codes: list = None, force: bool = False):
    """更新缓存数据"""
    try:
        cache = StockDataCache(cache_dir=cache_dir)
        
        if stock_codes is None:
            print("❌ 请提供股票代码列表")
            return
        
        print(f"开始更新 {len(stock_codes)} 只股票的缓存数据...")
        
        results = cache.batch_download(
            stock_codes=stock_codes,
            force_refresh=force
        )
        
        success_count = sum(results.values())
        print(f"✅ 更新完成: {success_count}/{len(stock_codes)} 成功")
        
    except Exception as e:
        print(f"❌ 更新缓存失败: {e}")

def list_cached_stocks(cache_dir: str, limit: int = 20):
    """列出已缓存的股票"""
    try:
        cache = StockDataCache(cache_dir=cache_dir)
        
        if not cache.cache_index:
            print("缓存中没有股票数据")
            return
        
        print(f"已缓存的股票 (显示前 {limit} 只):")
        print("-" * 60)
        print(f"{'股票代码':<12} {'最后更新':<20} {'记录数':<8} {'状态'}")
        print("-" * 60)
        
        count = 0
        for stock_code, info in cache.cache_index.items():
            if count >= limit:
                break
                
            last_update = datetime.fromisoformat(info['last_update']).strftime('%Y-%m-%d %H:%M')
            record_count = info['record_count']
            is_valid = cache._is_cache_valid(stock_code)
            status = "有效" if is_valid else "过期"
            
            print(f"{stock_code:<12} {last_update:<20} {record_count:<8} {status}")
            count += 1
        
        if len(cache.cache_index) > limit:
            print(f"... 还有 {len(cache.cache_index) - limit} 只股票")
            
    except Exception as e:
        print(f"❌ 列出缓存股票失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="股票数据缓存管理工具")
    parser.add_argument("--cache-dir", "-d", default="stock_cache", help="缓存目录路径")
    parser.add_argument("--status", "-s", action="store_true", help="显示缓存状态")
    parser.add_argument("--clean", "-c", action="store_true", help="清理过期缓存")
    parser.add_argument("--list", "-l", action="store_true", help="列出已缓存的股票")
    parser.add_argument("--limit", type=int, default=20, help="列表显示限制（默认20）")
    parser.add_argument("--update", "-u", nargs="+", help="更新指定股票的缓存")
    parser.add_argument("--force", "-f", action="store_true", help="强制更新缓存")
    
    args = parser.parse_args()
    
    setup_logging()
    
    # 检查缓存目录
    if not os.path.exists(args.cache_dir):
        print(f"❌ 缓存目录不存在: {args.cache_dir}")
        return
    
    # 执行相应操作
    if args.status:
        show_cache_status(args.cache_dir)
    elif args.clean:
        clean_expired_cache(args.cache_dir)
    elif args.list:
        list_cached_stocks(args.cache_dir, args.limit)
    elif args.update:
        update_cache(args.cache_dir, args.update, args.force)
    else:
        # 默认显示状态
        show_cache_status(args.cache_dir)
        print("\n使用 --help 查看更多选项")

if __name__ == "__main__":
    main()
