#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据缓存系统演示脚本
展示缓存系统的主要功能和使用方法
"""

import os
import pandas as pd
from datetime import datetime, timedelta

def demo_cache_system():
    """演示缓存系统功能"""
    print("=" * 60)
    print("股票数据缓存系统演示")
    print("=" * 60)
    
    # 1. 导入缓存系统
    print("1. 导入缓存系统...")
    try:
        from stock_data_cache import StockDataCache
        print("✅ 缓存系统导入成功")
    except ImportError as e:
        print(f"❌ 缓存系统导入失败: {e}")
        return
    
    # 2. 创建缓存实例
    print("\n2. 创建缓存实例...")
    try:
        cache = StockDataCache(cache_dir="demo_cache")
        print("✅ 缓存实例创建成功")
    except Exception as e:
        print(f"❌ 缓存实例创建失败: {e}")
        return
    
    # 3. 查看缓存状态
    print("\n3. 查看缓存状态...")
    try:
        status = cache.get_cache_status()
        print(f"✅ 缓存状态获取成功:")
        print(f"   - 总股票数: {status['total_stocks']}")
        print(f"   - 有效缓存: {status['valid_stocks']}")
        print(f"   - 过期缓存: {status['expired_stocks']}")
        print(f"   - 缓存大小: {status['cache_size_mb']} MB")
    except Exception as e:
        print(f"❌ 获取缓存状态失败: {e}")
    
    # 4. 演示数据获取（模拟）
    print("\n4. 演示数据获取功能...")
    try:
        # 这里只是演示接口，不实际调用API
        print("✅ 数据获取接口可用:")
        print("   - get_stock_data(stock_code, start_date, end_date)")
        print("   - batch_download(stock_codes)")
        print("   - 支持自动缓存和过期管理")
    except Exception as e:
        print(f"❌ 数据获取演示失败: {e}")
    
    # 5. 演示评分器集成
    print("\n5. 演示评分器集成...")
    try:
        from standalone_stock_scorer import RealDataService
        
        # 创建启用缓存的数据服务
        service = RealDataService(use_cache=True, cache_dir="demo_cache")
        print("✅ 评分器缓存集成成功:")
        print("   - 自动检测缓存可用性")
        print("   - 优先从缓存读取数据")
        print("   - 缓存不存在时调用API")
        print("   - 新数据自动保存到缓存")
        
    except Exception as e:
        print(f"❌ 评分器集成演示失败: {e}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("缓存系统使用指南")
    print("=" * 60)
    
    print("\n📋 快速开始:")
    print("1. 预缓存股票数据:")
    print("   python stock_data_precacher.py -i zhangting_20250601_20250630.csv")
    print("   python stock_data_precacher.py --all  # 缓存所有A股")
    
    print("\n2. 运行股票评分程序:")
    print("   python standalone_stock_scorer.py  # 自动使用缓存")
    print("   python standalone_stock_scorer.py --no-cache  # 禁用缓存")
    
    print("\n3. 管理缓存:")
    print("   python cache_manager.py --status  # 查看状态")
    print("   python cache_manager.py --clean   # 清理过期缓存")
    print("   python cache_manager.py --list    # 列出已缓存股票")
    
    print("\n🚀 主要优势:")
    print("- 显著减少API调用次数")
    print("- 提高程序运行速度")
    print("- 支持离线分析")
    print("- 智能缓存管理")
    print("- 并发下载支持")
    
    print("\n📁 文件结构:")
    print("stock_cache/")
    print("├── data/           # 股票数据文件")
    print("│   ├── 000/        # 按代码前缀分组")
    print("│   ├── 600/")
    print("│   └── ...")
    print("└── metadata/       # 缓存索引和元数据")
    
    print("\n⚙️ 配置选项:")
    print("- data_expire_days: 7    # 数据过期天数")
    print("- retry_count: 3         # API重试次数")
    print("- max_workers: 5         # 并发下载线程数")
    
    print("\n🔧 故障排除:")
    print("- 如果缓存系统不可用，程序会自动降级到直接API调用")
    print("- 使用 --no-cache 参数可以完全禁用缓存")
    print("- 定期运行 --clean 清理过期缓存释放空间")
    
    print("\n" + "=" * 60)

def create_example_config():
    """创建示例配置文件"""
    config_content = """
# 股票数据缓存系统配置示例

## 基本配置
cache_dir = "stock_cache"          # 缓存目录
max_workers = 5                    # 并发下载线程数
data_expire_days = 7               # 数据过期天数

## API配置
retry_count = 3                    # 重试次数
retry_delay = 2                    # 重试延迟(秒)
request_delay = 0.1                # 请求间隔(秒)

## 存储配置
use_parquet = true                 # 优先使用Parquet格式
compression = "snappy"             # 压缩算法

## 日志配置
log_level = "INFO"                 # 日志级别
log_file = "cache_system.log"      # 日志文件
"""
    
    with open("cache_config_example.txt", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 示例配置文件已创建: cache_config_example.txt")

def main():
    """主函数"""
    print("股票数据缓存系统 - 完整演示")
    
    # 运行演示
    demo_cache_system()
    
    # 显示使用指南
    show_usage_guide()
    
    # 创建示例配置
    create_example_config()
    
    print("\n🎉 缓存系统已准备就绪！")
    print("请按照上述指南开始使用缓存系统。")

if __name__ == "__main__":
    main()
